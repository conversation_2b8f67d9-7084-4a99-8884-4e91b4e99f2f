package com.integral.is.oms.calculator;

// Copyright (c) 2001-2002 Integral Development Corp.  All rights reserved.

import com.integral.adaptor.order.configuration.OrderConfiguration;
import com.integral.adaptor.order.configuration.OrderConfigurationFactory;
import com.integral.adaptor.order.configuration.OrderConfigurationMBean;
import com.integral.aggregation.config.AggregationServiceFactory;
import com.integral.aggregation.config.AggregationServiceMBean;
import com.integral.broker.HourglassPricingUtil;
import com.integral.finance.currency.Currency;
import com.integral.finance.dealing.ContingencyType;
import com.integral.finance.dealing.DealingPrice;
import com.integral.finance.dealing.Quote;
import com.integral.finance.dealing.Request;
import com.integral.finance.dealing.RequestEvent;
import com.integral.finance.dealing.RequestEventC;
import com.integral.finance.fx.FXRateBasis;
import com.integral.is.ISCommonConstants;
import com.integral.is.common.ISConstantsC;
import com.integral.is.common.cache.UserCacheHandlerC;
import com.integral.is.common.mbean.ISFactory;
import com.integral.is.common.mbean.ISMBean;
import com.integral.is.common.util.ISUtilImpl;
import com.integral.is.common.util.MarketSnapshotUtil;
import com.integral.is.finance.dealing.workflowhandler.BaseWorkflowHandlerC;
import com.integral.is.message.MessageFactory;
import com.integral.is.oms.*;
import com.integral.is.oms.descriptors.EntityDescriptor;
import com.integral.is.oms.log.OMSLogger;
import com.integral.is.order.configuration.OMSConfigurationFactory;
import com.integral.is.spaces.fx.esp.util.DealingModelUtil;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.math.MathUtil;
import com.integral.message.ErrorMessage;
import com.integral.message.Message;
import com.integral.message.MessageEvent;
import com.integral.message.MessageHandler;
import com.integral.message.MessageStatus;
import com.integral.message.WorkflowMessage;
import com.integral.model.oms.OrderLiftRequest;
import com.integral.session.IdcSessionContext;
import com.integral.session.IdcSessionManager;
import com.integral.system.configuration.ConfigurationFactory;
import com.integral.system.runtime.RuntimeFactory;
import com.integral.system.runtime.ServerRuntimeMBean;
import com.integral.tradingvenue.TradingVenueClassificationEnums;
import com.integral.user.Organization;
import com.integral.user.User;
import com.integral.util.MathUtilC;
import com.integral.util.Tuple;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * Base calculator class for order workflow.
 */
public abstract class OrderWorkflowCalculatorC
{
	protected static UserCacheHandlerC userCache = UserCacheHandlerC.getUserCacheHandler();
	protected static OrderBookCache orderBookCache = OrderFactory.getOrderBookCache();
	protected static BaseWorkflowHandlerC baseWorkflowHandler = new BaseWorkflowHandlerC();
	protected static OrderConfigurationMBean orderConfig = OrderConfiguration.getInstance();
	protected static final ISMBean ismBean = ISFactory.getInstance().getISMBean();
    protected static final ServerRuntimeMBean serverRuntimeMBean = RuntimeFactory.getServerRuntimeMBean();
    private static AggregationServiceMBean aggregationServiceMBean =  AggregationServiceFactory.getInstance().getAggregationMBean();
    private static OrderConfigurationMBean orderConfgMBean = OrderConfigurationFactory.getOrderConfigurationMBean();

	private static final Log log = LogFactory.getLog(OrderWorkflowCalculatorC.class);
	
	private static final Set<String> empty_set = new HashSet<String>();

    /**
     * Character we use to indicate a true value for a flag in snapshot.
     */
    private static final char TRUE = MarketSnapshotUtil.TRUE;
    
    /**
     * Character we use to indicate a false value for a flag in snapshot.
     */
    private static final char FALSE = MarketSnapshotUtil.FALSE;
    
    // Different separator characters we use in the new snapshot.
    private static final char FIELD_SEPARATOR  = MarketSnapshotUtil.FIELD_SEPARATOR;
    private static final char RECORD_SEPARATOR = MarketSnapshotUtil.RECORD_SEPARATOR;
    private static final char TYPE_SEPARATOR   = MarketSnapshotUtil.TYPE_SEPARATOR;
    private static final char EMPTY_RECORD     = MarketSnapshotUtil.EMPTY_RECORD;
    
	protected double adjustPrecisionForAmount( double counterOrderAmount, double calculatedAmount )
	{
		double allowedDiff = OMSConfigurationFactory.getOMSConfig().getOrderCorrectionAmount();
		double diff = Math.abs(counterOrderAmount - calculatedAmount);
		if ( log.isDebugEnabled() )
		{
			log.debug("OrderWorkflowCalculator.adjustPrecisionForAmount:CounterOrderAmount:" + counterOrderAmount + " CalculatedAmount:" + calculatedAmount + " allowedDiff:" + allowedDiff + " Abs(Diff):" + diff);

		}
		if ( diff < allowedDiff )
		{
			return counterOrderAmount;
		}
		else
		{
			return calculatedAmount;
		}
	}

    public String getOrderBookSnapShot( OrderLiftRequest request, Order order, int boMode, String event )
    {
        String orderId = request.getOrderId();
        String externalRequestId = request.getClientReferenceId();
        return getOrderBookSnapshot(order, boMode, event, orderId, externalRequestId);
    }

	protected String getOrderBookSnapshot( Order order, int boMode, String event, String orderId, String externalRequestId )
	{
		if( serverRuntimeMBean.isOCXDeploymentEnabled())
			return "";

		OrderBook orderBook = OrderFactory.getOrderBookCache().getOrderBook(order);
		boolean isBid = boMode == DealingPrice.BID;
		String strboMode = isBid ? "Offer Rates " : "Bid Rates ";
		StringBuilder sbf = new StringBuilder(100);
		try
		{
			Collection<Order> orders;
			if(!order.isHourglassPricingSupported()) {
				orders = isBid ? orderBook.getSortedOfferQuotes() : orderBook.getSortedBidQuotes();
			}
			else {
				orders = isBid ? orderBook.getOfferQuotes() : orderBook.getBidQuotes();
				HourglassPricingUtil.filterNonHourglassQuotes(orders);
			}
			if(order.getEntityDescriptor().isStreamCategoryCheckEnabled()) {
				OMSUtilC.applyStreamCategoryBasedFilter(orders, order.getEntityDescriptor().getStreamCategoryFlag());
			}
			sbf.append(OMSLogger.MRKT_SNAP_SHOT).append(OMSLogger.whitespace);
			sbf.append(event).append(OMSLogger.whitespace);
			sbf.append(orderId).append(OMSLogger.whitespace);
			sbf.append(externalRequestId).append(OMSLogger.whitespace);
			sbf.append(strboMode).append(OMSLogger.whitespace);
			for ( Order anOrder : orders )
			{
				EntityDescriptor ed = anOrder.getEntityDescriptor();
				sbf.append(ed.getOrganization()).append(OMSLogger.whitespace);
				sbf.append(ed.getStream());
				sbf.append(anOrder.isActive() ? OMSLogger.whitespace_active : OMSLogger.whitespace_inactive);
				sbf.append(ed.getOrderPrice()).append(OMSLogger.whitespace);
				sbf.append(ed.getOrderAmount()).append(OMSLogger.whitespace);
			}
		}
		catch ( Exception e )
		{
			log.warn("OrderWorkflowCalculator.getOrderBookSnapShot - This exception can be IGNORED Failed to create - " + e.getMessage());
			if ( log.isDebugEnabled() )
			{
				log.debug("OrderWorkflowCalculator.getOrderBookSnapShot - Exception ", e);
			}
		}
		return sbf.toString();
	}

	protected void logResponse( WorkflowMessage wfMessage, String orderId, double filledAmount, double orderAmount )
	{
		StringBuilder sb = new StringBuilder(200);
		sb.append("OrderWorkflowCalculatorC.logResponse: ORDER-COMPLETED - Order Response [").append(" Event=").append(wfMessage.getEventName()).append(" Topic=").append(wfMessage.getTopic()).append(" OrderId=").append(orderId).append(" Filled Amount=").append(filledAmount).append(" Order Amount=").append(orderAmount).append(']');
		log.info(sb.toString());
	}

	/**
	 * Create a new workflowMessage with the object, topic, and event set on the message
	 *
	 * @param object workflow message object
	 * @param topic  topic
	 * @param event  event
	 * @return the populated workflowMessage
	 */
	public static WorkflowMessage getWorkflowMessage( Object object, String topic, MessageEvent event )
	{
		return getWorkflowMessage(object, topic, event, null);
	}

	/**
	 * Create a new workflowMessage with the object, topic, event, and note set on the message
	 *
	 * @param object object
	 * @param topic  topic
	 * @param event  event
	 * @param note   note
	 * @return the populated workflowMessage
	 */
	protected static WorkflowMessage getWorkflowMessage( Object object, String topic, MessageEvent event, String note )
	{
		WorkflowMessage msg = MessageFactory.newWorkflowMessage();
		msg.setObject(object);
		msg.setTopic(topic);
		msg.setEventName(event.getName());
		msg.setNote(note);
		msg.setStatus(MessageStatus.SUCCESS);
		return msg;
	}

	protected String createMarketSnapShotOrderLifted( Map<String, List<Order>> orderCollection, boolean isDealtInTerm, boolean sortQuotes, String preferredProvider, Comparator<Order> comparator, boolean isBid, Order takerOrder )
	{
		Map<String, List<Order>> updatedCollection = new HashMap<String, List<Order>>();
		List<Order> bidOrders = new ArrayList<Order>(orderCollection.get(ISConstantsC.BID_ORDERS));
		List<Order> offerOrders = new ArrayList<Order>(orderCollection.get(ISConstantsC.OFFER_ORDERS));
		updatedCollection.put(ISConstantsC.BID_ORDERS, bidOrders);
		updatedCollection.put(ISConstantsC.OFFER_ORDERS, offerOrders);
		if ( isBid )
		{
			offerOrders.add(takerOrder);
		}
		else
		{
			bidOrders.add(takerOrder);
		}
		if ( preferredProvider != null )
		{
			preferredProvider = preferredProvider + ',' + takerOrder.getEntityDescriptor().getOrganization();
		}
		return createMarketSnapShot(updatedCollection, isDealtInTerm, sortQuotes, preferredProvider, comparator, isBid, null, null, null);
	}

	protected String createMarketSnapShot(Map<String, List<Order>> orderCollection,
                                          boolean isDealtInTerm,
                                          boolean sortQuotes,
                                          String preferredProvider,
                                          Comparator<Order> comparator,
                                          boolean isBid,
                                          OrderExecutionDetails oed,
                                          String providerOrg,
                                          String origOrderOrg )
	{
		List<Order> bidOrders = orderCollection.get(ISConstantsC.BID_ORDERS);
		List<Order> offerOrders = orderCollection.get(ISConstantsC.OFFER_ORDERS);

		if (bidOrders == null || offerOrders == null || (bidOrders.isEmpty() && offerOrders.isEmpty())) {
			log.info("createMarketSnapShot: Returning an empty market snapshot since "
					+ "both bid and offer order collections are either null or empty.");
			return "";
		}

		// Currently, we truncate the snapshot to MARKET_SNAPSHOT_MAX_LENGTH while setting it
		// in the request. But, since the new format snapshot does not have the orders sorted,
		// truncating it could cause loss of some required orders. To work this around, we still
		// use the old format when we expect the snapshot to be large so that we avoid truncation
		// of the new snapshot.

		// We include the bid and offer quotes and some additional buffer in the estimated size.
		int estimatedSize = (bidOrders.size() + offerOrders.size()) * 70 + 200;

		// Also add preferred providers length if they are specified.
		if (preferredProvider != null) {
			estimatedSize += preferredProvider.length();
		}

		if (ConfigurationFactory.getServerMBean().isIntegralSpacesEnabled() && ( serverRuntimeMBean.isOCXDeploymentEnabled() || orderConfgMBean.isMarketSnapshotTruncateEnabled() ||
				estimatedSize < com.integral.finance.dealing.RequestC.MARKET_SNAPSHOT_MAX_LENGTH ) ) 
		{
			return createNewMarketSnapshot(bidOrders, offerOrders, isDealtInTerm, sortQuotes,
					preferredProvider, comparator, isBid, oed, providerOrg);
        }
        else  {
			log.info("createMarketSnapShot: Creating market snapshot in old format for quotes with "
					+ bidOrders.size() + " bids and " + offerOrders.size() + " offers.");
			return createOldFormatMarketSnapShot(bidOrders, offerOrders, isDealtInTerm, sortQuotes,
					preferredProvider, comparator, isBid, oed, providerOrg, origOrderOrg);
		}
    }

    /**
     * Create market snapshot in old format where we sort all the quotes before creating the snapshot.
     * See {@link #createNewMarketSnapshot}
     * for more details on how we create a new snapshot when spaces is enabled.
     *
     * <p>Note: The scope of  this method was kept protected only for the sake of unit test but
     * this should never be overriden otherwise.</p>
     */
    protected String createOldFormatMarketSnapShot(List<Order> bidOrders, List<Order> offerOrders, boolean isDealtInTerm, boolean sortQuotes, String preferredProvider, Comparator<Order> comparator, boolean isBid, OrderExecutionDetails oed, String providerOrg, String origOrderOrg )
    {
        long currentTime = 0L;
        if (log.isDebugEnabled()) {
            currentTime = System.nanoTime();
        }

        StringBuilder marketSnapShot = null;
        try
		{
            SimpleDateFormat dateFormat = new SimpleDateFormat("HH:mm:ss:SSS");
			boolean preferredProviderOnly = preferredProvider != null;
			String[] prfprvs = preferredProviderOnly ? preferredProvider.split(",") : new String[] {};
			if ( log.isDebugEnabled() )
			{
				log.debug("createMarketSnapShot: bidOrders : " + bidOrders + " offerOrders : " + offerOrders);
			}

			if ( bidOrders == null || offerOrders == null )
			{
				log.warn("createMarketSnapShot:BidOrders or OfferOrder Collection is null.Hence returning");
				return "";
			}

			if ( sortQuotes )
			{
				if ( isBid )
				{
					Collections.sort(bidOrders, ComparatorFactory.getComparatorFactory().getSnapshotComparator(true));

					if ( comparator == null )
					{
						comparator = ComparatorFactory.getComparatorFactory().getSnapshotComparator(false);
					}
					Collections.sort(offerOrders, comparator);
				}
				else
				{
					if ( comparator == null )
					{
						comparator = ComparatorFactory.getComparatorFactory().getSnapshotComparator(true);
					}
					Collections.sort(bidOrders, comparator);

					Collections.sort(offerOrders, ComparatorFactory.getComparatorFactory().getSnapshotComparator(false));
				}

			}
			if ( log.isDebugEnabled() )
			{
				log.debug("createMarketSnapShot: After sorting bidOrders : " + bidOrders + " offerOrders : " + offerOrders);
			}

			List<Order> filteredBidOrders = new ArrayList<Order>(bidOrders);
			List<Order> filteredOfferOrders = new ArrayList<Order>(offerOrders);
			Order hiddenProviderMatchedOrder = null;
			boolean isHiddenProviderMatch = oed != null && providerOrg != null && oed.isHiddenProviderMatch();
			if ( preferredProviderOnly || isHiddenProviderMatch ) 
			{
				ListIterator<Order> itr = filteredBidOrders.listIterator();
				while ( itr.hasNext() ) 
				{
					Order temp = itr.next();
                    if(temp.getEntityDescriptor().isVenueMarketRate() && !temp.getEntityDescriptor().isNoLastLookStreamQuote()){
                        if(temp.getEntityDescriptor().getOrganizationReference().getTradingVenueOrgFunction().getTradingVenue().getVenueType() == TradingVenueClassificationEnums.VenueType.REX){
                            itr.remove();
                        }
                    }else if ( preferredProviderOnly && !isContainsProvider(prfprvs, temp.getEntityDescriptor().getOrganization()) )
					{
						itr.remove();
					} 
					else if ( isHiddenProviderMatch && providerOrg.equals(temp.getEntityDescriptor().getOrganization()) ) 
					{
						if ( !isBid && hiddenProviderMatchedOrder == null ) 
						{
							hiddenProviderMatchedOrder = temp;
						}
						itr.remove();
					}
				}

				itr = filteredOfferOrders.listIterator();
				while (itr.hasNext()) 
				{
					Order temp = itr.next();
                    if(temp.getEntityDescriptor().isVenueMarketRate() && !temp.getEntityDescriptor().isNoLastLookStreamQuote()){
                        if(temp.getEntityDescriptor().getOrganizationReference().getTradingVenueOrgFunction().getTradingVenue().getVenueType() == TradingVenueClassificationEnums.VenueType.REX){
                            itr.remove();
                        }
                    }else if (preferredProviderOnly && !isContainsProvider(prfprvs, temp.getEntityDescriptor().getOrganization()))
					{
						itr.remove();
					} 
					else if (isHiddenProviderMatch && providerOrg.equals(temp.getEntityDescriptor().getOrganization())) 
					{
						if (isBid && hiddenProviderMatchedOrder == null) 
						{
							hiddenProviderMatchedOrder = temp;
						}
						itr.remove();
					}
				}
			}

			if ( log.isDebugEnabled() )
			{
				log.debug("createMarketSnapShot: After sorting filteredBidOrders : " + filteredBidOrders + " filteredOfferOrders : " + filteredOfferOrders);
			}

			Iterator<Order> bid_itr = filteredBidOrders.iterator();
			Iterator<Order> offer_itr = filteredOfferOrders.iterator();
			boolean isCumulativeOrderAdded = false;
			marketSnapShot = new StringBuilder((filteredBidOrders.size() + filteredOfferOrders.size()) * 35);

			while ( bid_itr.hasNext() || offer_itr.hasNext() )
			{
				Order temp_Bid_Ord = null;
				Order temp_Offer_Ord = null;
				if ( bid_itr.hasNext() )
				{
					// build bid string
					temp_Bid_Ord = bid_itr.next();
					if ( !temp_Bid_Ord.isActive() )
					{
						temp_Bid_Ord = null;
					}
					else if ( preferredProviderOnly && !isContainsProvider(prfprvs, temp_Bid_Ord.getEntityDescriptor().getOrganization()) )
					{
						//If not in the list then set it to null
						//Not preferred provider skip
						temp_Bid_Ord = null;
					}
				}

				if ( offer_itr.hasNext() )
				{
					// build offer string
					temp_Offer_Ord = offer_itr.next();
					if ( !temp_Offer_Ord.isActive() )
					{
						temp_Offer_Ord = null;
					}
					else if ( preferredProviderOnly && !isContainsProvider(prfprvs, temp_Offer_Ord.getEntityDescriptor().getOrganization()) )
					{
						temp_Offer_Ord = null;
					}
				}

				isCumulativeOrderAdded = appendHiddenProviderQuote(marketSnapShot, isCumulativeOrderAdded, isHiddenProviderMatch, hiddenProviderMatchedOrder, isBid, temp_Offer_Ord, temp_Bid_Ord, oed, isDealtInTerm, dateFormat);

				if ( temp_Bid_Ord != null && temp_Offer_Ord != null )
				{
					appendBidQuoteDetails(marketSnapShot, temp_Bid_Ord, isDealtInTerm, dateFormat);
					appendOfferQuoteDetails(marketSnapShot, temp_Offer_Ord, isDealtInTerm, dateFormat);
					marketSnapShot.append('|');
				}
				else if ( temp_Bid_Ord != null && temp_Offer_Ord == null )
				{
					appendBidQuoteDetails(marketSnapShot, temp_Bid_Ord, isDealtInTerm, dateFormat);
					// build dummy place holder for offer 5 semi-colons
					marketSnapShot.append(";;;;;");
					marketSnapShot.append('|');
				}
				else if ( temp_Bid_Ord == null && temp_Offer_Ord != null )
				{
					// build dummy place holder for bid 4 semi-colons
					marketSnapShot.append(";;;;");
					appendOfferQuoteDetails(marketSnapShot, temp_Offer_Ord, isDealtInTerm, dateFormat);
					marketSnapShot.append('|');
				}
			}

			if ( filteredBidOrders.size() == 0 && filteredOfferOrders.size() == 0 )
			{
				isCumulativeOrderAdded = appendHiddenProviderQuote(marketSnapShot, isCumulativeOrderAdded, isHiddenProviderMatch, hiddenProviderMatchedOrder, isBid, null, null, oed, isDealtInTerm, dateFormat);
			}
		}
		catch ( Exception e )
		{
			log.error("createMarketSnapShot:Exception while creating marketsnapShot:", e);
			marketSnapShot = new StringBuilder();
		}
		if ( log.isDebugEnabled() )
		{
			log.debug("createMarketSnapShot:Time taken (in NANOs) - " + (System.nanoTime() - currentTime) + " Snapshot - " + marketSnapShot.toString());
		}
		
		return marketSnapShot == null ? "" : marketSnapShot.toString();
	}

	private boolean isContainsProvider( String[] prprvs, String provider )
	{
		if ( prprvs == null )
		{
			return false;
		}
		for ( String current : prprvs )
		{
			if ( current.equals(provider) )
			{
				return true;
			}
		}
		return false;
	}

	private boolean appendHiddenProviderQuote( StringBuilder marketSnapShot, boolean isCumulativeOrderAdded, boolean isHiddenProviderMatch, Order hiddenProviderMatchedOrder, boolean isBid, Order temp_Offer_Ord, Order temp_Bid_Ord, OrderExecutionDetails oed, boolean isDealtInTerm, SimpleDateFormat dateFormat )
	{
		//if cumulativeamt
		if ( !isCumulativeOrderAdded && isHiddenProviderMatch )
		{
			if ( isBid && hiddenProviderMatchedOrder != null && (temp_Offer_Ord == null || temp_Offer_Ord.getOrderPrice() >= oed.getHiddenMatchPrice()) )
			{
				marketSnapShot.append(";;;;");
				marketSnapShot.append(';').append(oed.getHiddenMatchPrice());
				marketSnapShot.append(';').append(1);
				if ( !isDealtInTerm )
				{
					marketSnapShot.append(';').append(oed.getMatchableAmount());
				}
				else
				{
					marketSnapShot.append(';').append(MathUtilC.multiply(oed.getMatchableAmount(), oed.getHiddenMatchPrice()));
				}
				marketSnapShot.append(';').append(hiddenProviderMatchedOrder.getEntityDescriptor().getOrganization());
				marketSnapShot.append(';').append(dateFormat.format(hiddenProviderMatchedOrder.getCreationTime()));
				marketSnapShot.append('|');
				return true;
			}
			else if ( !isBid && hiddenProviderMatchedOrder != null && (temp_Bid_Ord == null || temp_Bid_Ord.getOrderPrice() <= oed.getHiddenMatchPrice()) )
			{
				marketSnapShot.append(dateFormat.format(hiddenProviderMatchedOrder.getCreationTime()));
				marketSnapShot.append(';').append(hiddenProviderMatchedOrder.getEntityDescriptor().getOrganization());
				if ( !isDealtInTerm )
				{
					marketSnapShot.append(';').append(oed.getMatchableAmount());
				}
				else
				{
					marketSnapShot.append(';').append(MathUtilC.multiply(oed.getMatchableAmount(), oed.getHiddenMatchPrice()));
				}
				marketSnapShot.append(';').append(1);
				marketSnapShot.append(';').append(oed.getHiddenMatchPrice());
				marketSnapShot.append(";;;;;");
				marketSnapShot.append('|');
				return true;
			}
		}
		return isCumulativeOrderAdded;
	}

	private void appendBidQuoteDetails( StringBuilder marketSnapShot, Order order, boolean isDealtInTerm, SimpleDateFormat dateFormat )
	{
		marketSnapShot.append(dateFormat.format(order.getCreationTime()));
		marketSnapShot.append(';').append(order.getEntityDescriptor().getOrganization());
		if ( !isDealtInTerm )
		{
			if ( order.getEntityDescriptor().isVenueMarketRate() )
				marketSnapShot.append(';').append(order.getDiscloseAmount());
			else if ( order.getEntityDescriptor().isLiftOrder() )
			{
				marketSnapShot.append(';').append(order.getOrderAmount());
			}
			else
			{
				marketSnapShot.append(';').append(order.getUnfilledAmount());
			}
		}
		else
		{
			if ( order.getEntityDescriptor().isVenueMarketRate() )
				marketSnapShot.append(';').append(order.getDiscloseAmount());
			if ( order.getEntityDescriptor().isLiftOrder() )
			{
				marketSnapShot.append(';').append(MathUtilC.multiply(order.getOrderAmount(), order.getOrderPrice()));
			}
			else
			{
				marketSnapShot.append(';').append(MathUtilC.multiply(order.getUnfilledAmount(), order.getOrderPrice()));
			}
		}
		marketSnapShot.append(';').append(order.getEntityDescriptor().getTierNo() + 1);
        if( order.getEntityDescriptor().isMarketData() )
        {
            marketSnapShot.append(';').append(order.getEntityDescriptor().getAdjustedMatchPrice());
        }
        else
        {
            marketSnapShot.append(';').append(order.getOrderPrice());
        }
	}

	private void appendOfferQuoteDetails( StringBuilder marketSnapShot, Order order, boolean isDealtInTerm, SimpleDateFormat dateFormat )
	{
        if( order.getEntityDescriptor().isMarketData() )
        {
            marketSnapShot.append(';').append(order.getEntityDescriptor().getAdjustedMatchPrice());
        }
        else
        {
            marketSnapShot.append(';').append(order.getOrderPrice());
        }
		marketSnapShot.append(';').append(order.getEntityDescriptor().getTierNo() + 1);
		if ( !isDealtInTerm )
		{
			if ( order.getEntityDescriptor().isLiftOrder() )
			{
				marketSnapShot.append(';').append(order.getOrderAmount());
			}
			else
			{
				marketSnapShot.append(';').append(order.getUnfilledAmount());
			}
		}
		else
		{
			if ( order.getEntityDescriptor().isLiftOrder() )
			{
				marketSnapShot.append(';').append(MathUtilC.multiply(order.getOrderAmount(), order.getOrderPrice()));
			}
			else
			{
				marketSnapShot.append(';').append(MathUtilC.multiply(order.getUnfilledAmount(), order.getOrderPrice()));
			}
		}
		marketSnapShot.append(';').append(order.getEntityDescriptor().getOrganization());
		marketSnapShot.append(';').append(dateFormat.format(order.getCreationTime()));
	}
	
    /**
     * Creates a new market snapshot from the given order collection and returns
     * it as a string. Please note that the snapshot will not have quotes in
     * sorted order but we capture all the raw information in the snapshot
     * so that the clients can reconstruct proper snapshot completely.
     * 
     * @see {@link MarketSnapshotUtil} for more details on the format of new
     * snapshot. We should also modify MarketSnapshotUtil whenever this method
     * is modified.
     */
    public static String createNewMarketSnapshot( List<Order> bidOrders, List<Order> offerOrders,
                                                  boolean isDealtInTerm, boolean sortQuotes, String preferredProvider,
                                                  Comparator<Order> comparator, boolean isBid, OrderExecutionDetails oed,
                                                  String providerOrg )
    {
        long currentTime = 0L;
        if (log.isDebugEnabled()) {
            currentTime = System.nanoTime();
        }
        
        if (log.isDebugEnabled()) {
            log.debug("createNewMarketSnapShot: bidOrders : " + bidOrders + " offerOrders : " + offerOrders);
        }

        int maxsize = (serverRuntimeMBean.isOCXDeploymentEnabled() || orderConfgMBean.isMarketSnapshotTruncateEnabled()) ? 10 : Integer.MAX_VALUE;
        
        //for ocx preferred provider from user is not applicable.
        boolean preferredProviderOnly = preferredProvider != null && !serverRuntimeMBean.isOCXDeploymentEnabled();
        
        boolean isHiddenProviderMatch = (oed != null) && (providerOrg != null) && oed.isHiddenProviderMatch();
        Order hiddenProviderMatchedOrder = null;
        
        StringBuilder snapshot = new StringBuilder((bidOrders.size() + offerOrders.size() + 1) * 40);
        
        // Add a version character.
        snapshot.append(MarketSnapshotUtil.SNAPSHOT_VERSION);
        snapshot.append(TYPE_SEPARATOR);

        // Add all the flags information.
        snapshot.append(sortQuotes ? TRUE : FALSE);
        snapshot.append(isHiddenProviderMatch ? TRUE : FALSE);
        snapshot.append(isBid ? TRUE : FALSE);
        snapshot.append(isDealtInTerm ? TRUE : FALSE);
        snapshot.append((comparator != null) ? TRUE : FALSE);
        snapshot.append(preferredProviderOnly ? TRUE : FALSE);
        
        Set<String> preferredProviders;
        if (preferredProviderOnly) {
            snapshot.append(preferredProvider);
            
            preferredProviders = new HashSet<String>();
            String[] providers = preferredProvider.split(",");
            for (int i = 0; i < providers.length; i++) {
                preferredProviders.add(providers[i]);
            }
        }
        else {
            preferredProviders = empty_set;
        }
        
        snapshot.append(TYPE_SEPARATOR);
		boolean sort = orderConfgMBean.isSortQuotesInSnapShot();
		if (sort) {
			if (isBid) {
				Collections.sort(bidOrders, ComparatorFactory
						.getComparatorFactory().getSnapshotComparator(true));

				if (comparator == null) {
					comparator = ComparatorFactory.getComparatorFactory()
							.getSnapshotComparator(false);
				}
				Collections.sort(offerOrders, comparator);
			} else {
				if (comparator == null) {
					comparator = ComparatorFactory.getComparatorFactory()
							.getSnapshotComparator(true);
				}
				Collections.sort(bidOrders, comparator);

				Collections.sort(offerOrders, ComparatorFactory
						.getComparatorFactory().getSnapshotComparator(false));
			}
		}
        if (bidOrders.isEmpty()) {
            snapshot.append(EMPTY_RECORD);
        }
        else {
            for (int i = 0, j = 0; i < bidOrders.size() && j < maxsize ; i++) {
                Order temp = bidOrders.get(i);
                Double amount = null;
				if(temp.getEntityDescriptor().isVenueMarketRate())
					amount = temp.getDiscloseAmount();
				else
					amount = temp.getOrderAmount();
                if(temp.getEntityDescriptor().isVenueMarketRate() && !temp.getEntityDescriptor().isNoLastLookStreamQuote() && temp.getEntityDescriptor().getOrganizationReference() != null && temp.getEntityDescriptor().getOrganizationReference().getTradingVenueOrgFunction() != null){
                    if(temp.getEntityDescriptor().getOrganizationReference().getTradingVenueOrgFunction().getTradingVenue().getVenueType() == TradingVenueClassificationEnums.VenueType.CLOB){
                        appendBidQuoteDetails(snapshot, temp);
                        j++;
                    }else{
                        continue;
                    }
                }
                else if(!temp.getEntityDescriptor().isMarketData()){
                    continue;
                }
                else if ( preferredProviderOnly && !preferredProviders.contains(temp.getEntityDescriptor().getOrganization() ) )
                {
                    continue;
                }
                else if(amount == null || amount <= 0.0)
                {
                	continue;
				}
                else if(isHiddenProviderMatch && providerOrg.equals(temp.getEntityDescriptor().getOrganization()) )
                {
                    if( !isBid && hiddenProviderMatchedOrder == null )
                    {
                        hiddenProviderMatchedOrder = temp;
                        j++;
                    }
                    continue;
                }
                else if (temp.isActive()) {
                    appendBidQuoteDetails(snapshot, temp);
                    j++;
                }
                else {
                    log.info("Dropping bid order: " + temp.getOrderId() + " from market snapshot creation.");
                }
            }
        }

        snapshot.append(TYPE_SEPARATOR);

        if (offerOrders.isEmpty()) {
            snapshot.append(EMPTY_RECORD);
        }
        else {
            for (int i = 0, j=0; i < offerOrders.size() && j < maxsize; i++) {
                Order temp = offerOrders.get(i);
				Double amount = null;
				if(temp.getEntityDescriptor().isVenueMarketRate())
					amount = temp.getDiscloseAmount();
				else
					amount = temp.getOrderAmount();
                if(temp.getEntityDescriptor().isVenueMarketRate() && !temp.getEntityDescriptor().isNoLastLookStreamQuote() && temp.getEntityDescriptor().getOrganizationReference() != null && temp.getEntityDescriptor().getOrganizationReference().getTradingVenueOrgFunction() != null){
                    if(temp.getEntityDescriptor().getOrganizationReference().getTradingVenueOrgFunction().getTradingVenue().getVenueType() == TradingVenueClassificationEnums.VenueType.CLOB){
                        appendOfferQuoteDetails(snapshot, temp);
                        j++;
                    }else{
                        continue;
                    }
                }
                else if(!temp.getEntityDescriptor().isMarketData()){
                    continue;
                }
                else if ( preferredProviderOnly && !preferredProviders.contains(temp.getEntityDescriptor().getOrganization() )  )
                {
                    continue;
                }
				else if(amount == null || amount <= 0.0)
				{
					continue;
				}
                else if( isHiddenProviderMatch && providerOrg.equals( temp.getEntityDescriptor().getOrganization()) )
                {
                    if( isBid && hiddenProviderMatchedOrder == null )
                    {
                        hiddenProviderMatchedOrder = temp;
                        j++;
                    }
                    continue;
                }
                else if (temp.isActive()) {
                    appendOfferQuoteDetails(snapshot, temp);
                    j++;
                }
                else {
                    log.info("Dropping offer order: " + temp.getOrderId() + " from market snapshot creation.");
                }
            }
        }

        if (hiddenProviderMatchedOrder != null) {
            snapshot.append(TYPE_SEPARATOR);
            appendHiddenProviderQuote(snapshot, hiddenProviderMatchedOrder, oed);
        }
        else if (isHiddenProviderMatch) {
            log.warn("No hidden provider quote is being appended to market snapshot for the hidden provider order: "
                     + (oed != null ? oed.getCandidateOrderID() : null));
        }
        
        String str = snapshot.toString();
        
        if (log.isDebugEnabled()) {
            log.debug("createNewMarketSnapShot:Time taken (in NANOs) - "
                      + (System.nanoTime() - currentTime) + " Snapshot - " + str);
        }
        
        return str;
    }
    
    private static void appendHiddenProviderQuote(
        StringBuilder marketSnapShot, Order hiddenProviderMatchedOrder, OrderExecutionDetails oed)
    {
        marketSnapShot.append(hiddenProviderMatchedOrder.getCreationTime());
        marketSnapShot.append(FIELD_SEPARATOR).append(hiddenProviderMatchedOrder.getEntityDescriptor().getOrganization() );
        marketSnapShot.append(FIELD_SEPARATOR).append(oed.getMatchableAmount());
        marketSnapShot.append(FIELD_SEPARATOR).append(oed.getHiddenMatchPrice());
    }
        
    private static void appendOfferQuoteDetails(StringBuilder marketSnapShot, Order order)
    {
        marketSnapShot.append(order.getCreationTime());
        marketSnapShot.append(FIELD_SEPARATOR).append(order.getEntityDescriptor().getOrganization());
        if(order.getEntityDescriptor().isVenueMarketRate())
            marketSnapShot.append(FIELD_SEPARATOR).append(order.getDiscloseAmount());
        else
        marketSnapShot.append(FIELD_SEPARATOR).append(order.getOrderAmount());
        marketSnapShot.append(FIELD_SEPARATOR).append(order.getEntityDescriptor().getTierNo() + 1);
        if( order.getEntityDescriptor().isMarketData() )
        {
            marketSnapShot.append(FIELD_SEPARATOR).append(order.getEntityDescriptor().getAdjustedMatchPrice());
        }
        else
        {
            marketSnapShot.append(FIELD_SEPARATOR).append(order.getOrderPrice());
        }
        if(order.getEntityDescriptor().isVenueMarketRate())
            marketSnapShot.append(FIELD_SEPARATOR).append(Math.min(order.getDiscloseAmount(), order.getUnfilledAmount()));
        marketSnapShot.append(FIELD_SEPARATOR).append(order.getUnfilledAmount());
        marketSnapShot.append(FIELD_SEPARATOR).append(order.getEntityDescriptor().isLiftOrder() ? TRUE : FALSE );
        marketSnapShot.append(FIELD_SEPARATOR).append(order.getEntityDescriptor().isUnspreadedMatchEnabled() ? TRUE : FALSE );
        marketSnapShot.append(RECORD_SEPARATOR);

    }
        
    private static void appendBidQuoteDetails(StringBuilder marketSnapShot, Order order)
    {
        marketSnapShot.append(order.getCreationTime());
        marketSnapShot.append(FIELD_SEPARATOR).append(order.getEntityDescriptor().getOrganization());
        if(order.getEntityDescriptor().isVenueMarketRate())
            marketSnapShot.append(FIELD_SEPARATOR).append(order.getDiscloseAmount());
        else
        marketSnapShot.append(FIELD_SEPARATOR).append(order.getOrderAmount());
        marketSnapShot.append(FIELD_SEPARATOR).append(order.getEntityDescriptor().getTierNo() + 1);
        if( order.getEntityDescriptor().isMarketData() )
        {
            marketSnapShot.append(FIELD_SEPARATOR).append(order.getEntityDescriptor().getAdjustedMatchPrice());
        }
        else
        {
            marketSnapShot.append(FIELD_SEPARATOR).append(order.getOrderPrice());
        }
        if(order.getEntityDescriptor().isVenueMarketRate())
            marketSnapShot.append(FIELD_SEPARATOR).append(Math.min(order.getDiscloseAmount(), order.getUnfilledAmount()));
        else
        marketSnapShot.append(FIELD_SEPARATOR).append(order.getUnfilledAmount());
        marketSnapShot.append(FIELD_SEPARATOR).append(order.getEntityDescriptor().isLiftOrder() ? TRUE : FALSE);
        marketSnapShot.append(FIELD_SEPARATOR).append(order.getEntityDescriptor().isUnspreadedMatchEnabled() ? TRUE : FALSE);
        marketSnapShot.append(RECORD_SEPARATOR);
    }

	/*
	 * PegRate = TOB Bid/Offer + offset . Offset is signed.
	 * 
	 * Returns true if PegRate is valid.
	 */
	protected boolean isRatePeggable( boolean isBidOrder, double limitRate, double pegRate )
	{
		//return ( isBidOrder ) ? ( tobRate < limitRate ) : ( tobRate > limitRate );
        if(limitRate == 0d)
        {
            return true;
        }

		if ( isBidOrder )
		{
			//market is buying at lower peg to market.
			if ( pegRate <= limitRate )
			{
				return true;
			}
		}
		else
		{
			//market is selling lower 
			if ( pegRate >= limitRate )
			{
				return true;
			}
		}
		return false;
	}

	protected double getTOBRate( List<Order> quotes, boolean preferredProviderOnly, String preferredProviders, boolean ignorePeggedOrdersRates)
	{
		double tobrate = 0.0d;

		if ( quotes == null )
		{
			return tobrate;
		}

		for ( Order marketData : quotes )
		{
			if ( !marketData.isActive() || marketData.getEntityDescriptor().isMarketRateBroadcastOrder() )
			{
				continue;
			}

			if ( marketData.getOrderAmount() <= 0 || marketData.getOrderPrice() <= 0 )
			{
				continue;
			}

			if ( preferredProviderOnly && preferredProviders.indexOf(marketData.getEntityDescriptor().getOrganization()) == -1 )
			{
				continue;
			}

            if(ignorePeggedOrdersRates && marketData.isRateSourcePeggedOrder())
            {
                continue;
            }

			tobrate = marketData.getOrderPrice();

			break;
		}
		return tobrate;
	}

	public double getTOBRate( List<Order> quotes, boolean preferredProviderOnly, List<String> preferredProviders )
	{
		double tobrate = 0.0d;

		if ( quotes == null )
		{
			return tobrate;
		}

		for ( Order marketData : quotes )
		{
			if ( preferredProviderOnly && !preferredProviders.contains(marketData.getEntityDescriptor().getOrganization()) )
			{
				continue;
			}

			if ( !marketData.isActive() )
			{
				continue;
			}

			if ( marketData.getOrderAmount() <= 0 || marketData.getOrderPrice() <= 0 )
			{
				continue;
			}

			tobrate = marketData.getOrderPrice();

			break;
		}
		return tobrate;
	}

//	protected String getMarketSnapShot( Order order )
//	{
//		try
//		{
//			Map<String, List<Order>> orderCollectionMap = new HashMap<String, List<Order>>();
//			OrderBook orderBook = OrderFactory.getOrderBookCache().getOrderBook(order);
//			List<Order> bidOrders = orderBook.getSortedBidQuotes();
//			List<Order> offerOrders = orderBook.getSortedOfferQuotes();
//			bidOrders = filterOrders(bidOrders, order);
//			offerOrders = filterOrders(offerOrders, order);
//			orderCollectionMap.put(ISConstantsC.BID_ORDERS, bidOrders);
//			orderCollectionMap.put(ISConstantsC.OFFER_ORDERS, offerOrders);
//			return createMarketSnapShot(orderCollectionMap, order
//					.getEntityDescriptor().isDealingInTerm(), false, null,
//					null, order.getEntityDescriptor().isBid(), null, null, null);
//		}
//		catch ( Exception e )
//		{
//			log.error("OrderWorkflowCalculatorC.getMarketSnapShot(Order):Exception while creating marketSnapShot for Order:" + order.getOrderId(), e);
//		}
//		return null;
//	}
//
//	protected List<Order> filterOrders( Collection<Order> orders, Order order )
//	{
//		List<Order> sortedOrders = new ArrayList<Order>();
//		String orderIdtoBeIgnored = order.getOrderId();
//		for ( Order tempOrder : orders )
//		{
//			EntityDescriptor ed = tempOrder.getEntityDescriptor();
//			if ( !orderIdtoBeIgnored.equals(tempOrder.getOrderId()) && !ed.isLiftOrder() && ed.isActive() && !tempOrder.isCancelled() && !tempOrder.isExpired() && MatchingCriteriaValidatorC.checkTradingRelationshipAndCredit(ed, order.getEntityDescriptor()) )
//			{
//				sortedOrders.add(tempOrder);
//			}
//		}
//		return sortedOrders;
//	}


	public static String getOrderBookName( String ccyPair, String org , String tenor)
	{
		StringBuffer orderBookName = new StringBuffer(20);
		String ingredients = OMSConfigurationFactory.getOMSConfig().getOrderBookNameConstituents();
		if ( ingredients.indexOf("org") != -1 )
		{
			orderBookName.append(org).append('+');
		}
		orderBookName.append(ccyPair);
		if ( null != tenor && !"".equals(tenor.trim()) )
		{
			orderBookName.append("+").append(tenor);
		}
		return orderBookName.toString();
	}

	public StringBuffer getErrorMessage( Collection<ErrorMessage> col )
	{
		StringBuffer sb = new StringBuffer(100);
		
		try {
			for (ErrorMessage errorMessage : col) {
			    sb.append(' ').append(errorMessage.getErrorCode());
			}
			return sb;
		} 
		catch ( Exception e )
		{
			return sb.append("Exception in getting Error Message");
		}
	}

    public void setInitialAmountsOnRequest( OrderLiftRequest orderLiftRequest ) {
        //todo-SpacesDO
    }

    protected boolean isOrderUnFilledLessThanMinFillAmt(double toBeFilledAmount, Order order, double sliceRegularSize) {
        EntityDescriptor entityDescriptor = order.getEntityDescriptor();
        MatchingRiders orderRiders = order.getRiders();
        if (orderRiders == null) {
            orderRiders = new MatchingRiders();
        }
        if (entityDescriptor.isTWAPOrder() || entityDescriptor.isFixingTWAPOrder() || entityDescriptor.isFixingTWAPStrategyOrder()) {
            if (toBeFilledAmount < sliceRegularSize) {
                if (order.isFillAtMarketExecution()) { // fill at market at expiry case
                    if (entityDescriptor.isIgnoreRegularSizeForResidue()) {
                        orderRiders.ignoreRegSize = true;
                    }
                } else { // cancel order at expiry case
                    if (entityDescriptor.isIgnoreRegularSizeForResidue()) {
                        orderRiders.ignoreRegSize = true;
                    } else {
                    	if( order.getEntityDescriptor().isSmartSliceOrder() ){
                    		log.info("OrderWorkflowCalculatorC.isOrderUnFilledLessThanMinFillAmt toBeFilledAmount < celingfactor. tbfa="+toBeFilledAmount+",sf="+sliceRegularSize);
                    	}else{
                    		log.warn("OrderWorkflowCalculatorC.isOrderUnFilledLessThanMinFillAmt - Regular size check failed for TWAP Order Id -" + order.getOrderId() + " toBeFilledAmount - " + toBeFilledAmount + " regularSize - " + entityDescriptor.getRegularSize());
                    		return true;
                    	}
                    }
                }
            }
        } else if (entityDescriptor.getRegularSize() > 0) {
            if (toBeFilledAmount < entityDescriptor.getRegularSize()) {
                if (entityDescriptor.isIgnoreRegularSizeForResidue()) {
                    orderRiders.ignoreRegSize = true;
                } else {
                    log.warn("OrderWorkflowCalculatorC.isOrderUnFilledLessThanMinFillAmt - Regular size check failed. Order Id -" + order.getOrderId() + " toBeFilledAmount - " + toBeFilledAmount + " regularSize - " + entityDescriptor.getRegularSize());
                    return true;
                }
            }
        }
        //if min trade size is set then ignore OrderMinimumMarketRateAmount.
        double minTrdSize = entityDescriptor.getMinimumTradeSize();
        if (minTrdSize > 0) {
            if (toBeFilledAmount < minTrdSize) {
                if (entityDescriptor.isTWAPOrder() || entityDescriptor.isFixingTWAPOrder()) {
                    if (order.isFillAtMarketExecution()) { // fill at market at expiry case
                        if (entityDescriptor.isIgnoreMinMatchSizeForResidue()) {
                            orderRiders.ignoreMinTradeSize = true;
                        }
                    } else { // cancel order at expiry case
                        if (entityDescriptor.isIgnoreMinMatchSizeForResidue()) {
                            orderRiders.ignoreMinTradeSize = true;
                        } else {
                            log.warn("OrderWorkflowCalculatorC.isOrderUnFilledLessThanMinMatchAmt - MinTradeSize check failed. Order Id -" + order.getOrderId() + " toBeFilledAmount - " + toBeFilledAmount + " minTradeSize - " + minTrdSize);
                            return true;
                        }
                    }
                } else {
                    if (entityDescriptor.isIgnoreMinMatchSizeForResidue()) {
                        orderRiders.ignoreMinTradeSize = true;
                    } else {
                        log.warn("OrderWorkflowCalculatorC.isOrderUnFilledLessThanMinMatchAmt - MinTradeSize check failed. Order Id -" + order.getOrderId() + " toBeFilledAmount - " + toBeFilledAmount + " minTradeSize - " + minTrdSize);
                        return true;
                    }
                }
            }
        }
        //Removed the minOrder Size check completely for the residual amount
        order.setRiders(orderRiders);
        return false;
    }

    public void updateOrderAmountOnRPF( OrderLiftRequest liftRequest, double filledAmount, double filledPrice )
    {
        Currency dealtCcy = liftRequest.getDealtCurrency();
        FXRateBasis rb = liftRequest.getFXRateBasis();
        //RequestAttributes attr = liftRequest.getRequestAttributes();
        double ordersAmount = liftRequest.getDealtAmount();
        double unfilledAmount;
        double ordersFilledAmount = liftRequest.getFilledAmount();
        double avgFillPrice = liftRequest.getAverageFillRate();
        avgFillPrice = ((ordersFilledAmount * avgFillPrice) + (filledPrice * filledAmount)) / (ordersFilledAmount + filledAmount);
        if ( rb != null )
        {
            int precision = DealingModelUtil.isRateInverted( rb,liftRequest.getCurrencyPair().getVariableCurrency().getShortName() ) ? rb.getInverseSpotPrecision() + 2 : rb.getSpotPrecision() + 2;
            avgFillPrice = MathUtil.round(avgFillPrice, precision, rb.getRoundingType());
        }
        ordersFilledAmount += filledAmount;
        unfilledAmount = ordersAmount - ordersFilledAmount;

        if ( unfilledAmount < 0 )
        {
            //1. Over fill case due to term currency rounding -
            if ( Math.abs(unfilledAmount) < 1 )
            {
                ordersFilledAmount = ordersFilledAmount - Math.abs(unfilledAmount);
                unfilledAmount = 0.0;
            }
            else
            {
                StringBuilder buff = new StringBuilder("OrderWorkflowCalculatorC.updateOrderAmount - OVER-FILL - Order ID ");
                buff.append( liftRequest.getOrderId() );
                buff.append(" Order Amount - ").append(ordersAmount);
                buff.append(" Filled Amount - ").append(ordersFilledAmount);
                buff.append(" Un-Filled Amount - ").append(unfilledAmount);
                log.warn(buff.toString());
            }
        }

        ordersFilledAmount = dealtCcy.round(ordersFilledAmount);
        unfilledAmount = dealtCcy.round(unfilledAmount);
        liftRequest.setUnfilledAmount( unfilledAmount );
        liftRequest.setFilledAmount(ordersFilledAmount);
        liftRequest.setAverageFillRate( avgFillPrice );
    }

	/**
	 * Update state of request.
	 *
	 * @param request request
	 * @param state   state
	 * @return state transition result
	 */
	public boolean updateState( Request request, String state )
	{
		setSessionContext(request.getUser());
		ISUtilImpl.getInstance().doRequestStateTransition(request, state);
		request.update();
		return true;
	}

	public void setSessionContext( User user )
	{
		IdcSessionContext ctx = IdcSessionManager.getInstance().getSessionContext();
		if ( ctx == null )
		{
			ctx = IdcSessionManager.getInstance().getSessionContext(user);
			IdcSessionManager.getInstance().setSessionContext(ctx);
		}
	}

	/*
	  * For .NET client with capability to cross at client end, this will be set to '0'.
	  */
	public static boolean isClientCrossRequest( Request orderRequest )
	{
		return isClientCrossRequest(orderRequest.getExecutionFlags(), orderRequest.getTradeChannel());
	}

	public static boolean isClientCrossRequest( int execFlags, String channel )
	{
        //TODO cleanup is required for the usage of this method
		return false;
	}
	
	protected RequestEvent updateRequestEventOnVerification(Order order, String fillId, boolean isTradeRequestPending, User user ,
			double currentFillAmount, double cumFilledAmount, double dealtAmount, boolean isMultipleFillProvider, int cType)
	{
		RequestEvent updateEvent = order.getOUOUpdateEvent(fillId);
		if ( updateEvent == null )
		{
			log.info("OWC.updateOUORequestAmountsOnVerification. No OUO event found associated to fillId . OrderId " + order.getOrderId() + " FillId " + fillId);
			return null;
		}
		if ( (((order.isInitial() || order.isPartiallyFilled() ) && order.getUnfilledAmount() == 0.0) || order.getEntityDescriptor().isOMSOrder())  && !isTradeRequestPending )
		{
			log.info("OWC.updateOUORequestAmountsOnVerification. Unfilled amount reached zero. canceling linked order. OrderId " + order.getOrderId());
			cancelRequest(order, user);
			return null;
		}

		//Ignore fillids where fill amount it zero.
		if ( Double.valueOf(updateEvent.getParam5()) == 0.0 )
		{
			log.info("OWC.updateOUORequestAmountsOnVerification. associated amount with this fillId was zero. Order will not be restated . OrderId " + order.getOrderId() + " FillId " + fillId);
			return null;
		}

		boolean isAdjustAmount = (dealtAmount - cumFilledAmount) > 0;

		//Provider has filled partially.
		if ( isAdjustAmount )
		{
			double p2 = order.getOrderAmount(); // old order amount
			double p1 = order.getOrderAmount(); //New order amount
			double p5 = Double.valueOf(updateEvent.getParam5()); // amount locked in order for this fillId. it may be less then actual amount filled.    

			if ( cType == ContingencyType.OUO_ABSOLUTE )
			{
				if ( isMultipleFillProvider )
				{
					//in this case adjust of the order will be required.
					//compare p5 with currentFilled amount, amount locked (p5) in order for this fillId. it may be less then actual amount filled.
					RequestEvent dbRE = cloneRequestEvent(updateEvent);
					if ( p5 < currentFillAmount )
					{
						//first fill from multifill provider is more this entire locked amount with this fillid.
						//subsequent update should be ignored.
						p1 = p2 - p5;
						updateEvent.setParam5(String.valueOf(0.0));
					}
					else
					{
						//else p5 will be filled in chunks
						p1 = p2 - currentFillAmount;
						p5 = p5 - currentFillAmount;
						updateEvent.setParam5(String.valueOf(p5)); // Reduce locked amount
					}

					log.info("owc.updateOUORequestAmountsOnVerification - updatedRE(ABS) -  orderId " + order.getOrderId() + " RE- " + updateEvent.getParam1() + "," + updateEvent.getParam2() + "," + updateEvent.getParam3() + "," + updateEvent.getParam4() + "," + updateEvent.getParam5() + ",");

					//for rest of workflow replace updateEvent with dbRE;
					updateEvent = dbRE;
				}
				else
				//case partial verification by -single fill provider.
				{
					//compare p5 with currentFilled amount, amount locked (p5) in order for this fillId. it may be less then actual amount filled.
					if ( p5 < currentFillAmount )
					{
						//JUST REDUCE THE AMOUNT WHICH IS FILLED.
						p1 = p2 - currentFillAmount;
						//TODO DIFFERENCE BETWEEN p5 and currentFilledAmount should be reinstate.
					}
					else
					{
						p1 = p2 - p5;
					}
					//TODO - REMOVE OUO EVENT
				}
			}
			else
			{
				//P6 stores original p5. which should be used to calculate percent.
				double originalP5 = Double.valueOf(updateEvent.getParam6());
				double percentFill = currentFillAmount / dealtAmount;
				double updatedP5 = percentFill * originalP5; // proportionate amount filled for this order.
				p1 = p2 - updatedP5;
				if ( isMultipleFillProvider )
				{
					//clone for db and Trade Request Expire message and for subsequent fills.
					RequestEvent dbRE = cloneRequestEvent(updateEvent);

					p5 = p5 - updatedP5; //remaining portion which needs fill.
					updateEvent.setParam5(String.valueOf(p5)); // Reduce locked amount

					log.info("owc.updateOUORequestAmountsOnVerification - updatedRE(pro) -  orderId " + order.getOrderId() + " RE- " + updateEvent.getParam1() + "," + updateEvent.getParam2() + "," + updateEvent.getParam3() + "," + updateEvent.getParam4() + "," + updateEvent.getParam5() + ",");

					//for rest of workflow replace updateEvent with dbRE;
					updateEvent = dbRE;
				}
			}

			updateEvent.setParam1(String.valueOf(p1));
			updateEvent.setParam2(String.valueOf(p2));
		}
		else
		{
			updateEvent.setParam2(String.valueOf(order.getOrderAmount()));
			//No adjustment required.
			//Entire amount is filled just deduct the amount locked at the time of match.
			//currentFilledAmount can't be used in this case as it is not proportional.
			double newOA = order.getOrderAmount() - Double.valueOf(updateEvent.getParam5());
			updateEvent.setParam1(String.valueOf(newOA));
			//TODO - REMOVE OUO EVENT
		}
		return updateEvent;
	}

	/**
	 * @param order
	 * @param user
	 */
	public abstract void cancelRequest( Order order, User user );

	protected RequestEvent cloneRequestEvent( RequestEvent event )
	{
		RequestEvent clone = new RequestEventC();
		clone.setParam1(event.getParam1());
		clone.setParam2(event.getParam2());
		clone.setParam3(event.getParam3());
		clone.setParam4(event.getParam4());
		clone.setParam5(event.getParam5());
		clone.setParam6(event.getParam6());
		clone.setParam7(event.getParam7());
		clone.setParam8(event.getParam8());
		clone.setSeqID(event.getSeqID());
		clone.setRequest(event.getRequest());
		return clone;
	}

	
	protected boolean _isTradeRequestPending( Order o )
	{
		log.info("_isTradeRequestPending id="+o.getOrderId()+",extid="+ o.getExternalOrderId() +",fa="+o.getFilledAmount()+",ca="+o.getConfirmedAmount());
		if ( ( MathUtilC.subtract(o.getFilledAmount(), o.getConfirmedAmount()) ) >= MathUtilC.getMinAmount(o.getOrderAmountCurrency()) )
		{
			return true;	
		}
		return false;
	}

	public Message notifyUser( User user, WorkflowMessage message, MessageHandler handler )
	{
		return message;
	}

	public Double getMarketVWAPPrice(List<Order> quotes, boolean isDealtInTerm, boolean sortQuotes, String preferredProvider, Comparator<Order> comparator, double unfilledAmount, boolean isBid, double clientOrderPrice, int pricePrecision, String orgShortname)
	{
		Double quotesvwap = null;
		try
		{
			boolean preferredProviderOnly = preferredProvider != null;
			if ( log.isDebugEnabled() )
			{
				log.debug("OrderWorkflowCalculatorC.getMarketVWAPPrice: quotes : " + quotes);
			}

			if ( quotes == null )
			{
				log.debug("OrderWorkflowCalculatorC.getMarketVWAPPrice: Quotes are null. ");
				return quotesvwap;
			}

			if ( sortQuotes )
			{
				Collections.sort(quotes, comparator);
			}

			double cumulativeAmt = 0.0;
			double volumePriceProduct = 0.0;
			Double lastPrice = null;
			Map<String, Tuple<Double, Double>> multiTierProviders = new HashMap<String, Tuple<Double, Double>>();
			for ( Order marketData : quotes )
			{
				if ( preferredProviderOnly )
				{
                    if( preferredProvider.indexOf(marketData.getEntityDescriptor().getOrganization()) == -1 && !considerRiskNetLiquidity( marketData.getEntityDescriptor().getOrganizationReference(), orgShortname) ){
                        continue;
                    }
				}

                if(((Quote)(marketData.getEntityDescriptor().getEntity())).isFXIDirectStreamQuote()){
                    if ( log.isDebugEnabled() ) {
                        log.debug( "OrderWorkflowCalculatorC.getMarketVWAPPrice not considering FA quote in mvwap calculation for org=" + marketData.getEntityDescriptor().getOrganization() );
                    }
                    continue; // only considering non-FA streams and bypassing FA streams
                }

				if ( !marketData.isActive() )
				{
					continue;
				}

				if ( isBid && clientOrderPrice != 0.0 && !(clientOrderPrice >= marketData.getOrderPrice()) )
				{
					break;
				}
				else if ( !isBid && clientOrderPrice != 0.0 && !(clientOrderPrice <= marketData.getOrderPrice()) )
				{
					break;
				}

				double amt = isDealtInTerm ? marketData.getDiscloseAmount() * marketData.getOrderPrice() : marketData.getDiscloseAmount();

				if ( marketData.getEntityDescriptor().isMultiTier() )
				{
					String key = marketData.getEntityDescriptor().getOrganization();
					Tuple<Double, Double> lastMatch = multiTierProviders.get(key);
					if ( lastMatch != null )
					{
						//undo first
						cumulativeAmt = cumulativeAmt - lastMatch.first;
						volumePriceProduct = volumePriceProduct - lastMatch.second;
					}

					if ( amt > (unfilledAmount - cumulativeAmt) )
					{
						amt = unfilledAmount - cumulativeAmt;
					}

					Tuple<Double, Double> currentMatch = new Tuple<Double, Double>(amt, amt * marketData.getOrderPrice());
					multiTierProviders.put(key, currentMatch);
				}
				else if ( amt > (unfilledAmount - cumulativeAmt) )
				{
					amt = unfilledAmount - cumulativeAmt;
				}

				cumulativeAmt = cumulativeAmt + amt;
				volumePriceProduct = volumePriceProduct + amt * marketData.getOrderPrice();
				lastPrice = marketData.getOrderPrice();

				if ( unfilledAmount <= cumulativeAmt )
				{
					break;
				}
			}

			if ( cumulativeAmt < unfilledAmount )
			{
				return lastPrice;
			}

			if ( cumulativeAmt != 0.0 && volumePriceProduct != 0.0 )
			{
				quotesvwap = volumePriceProduct / cumulativeAmt;
				if ( isBid )
				{
					quotesvwap = MathUtil.round(quotesvwap, pricePrecision, BigDecimal.ROUND_FLOOR);
				}
				else
				{
					quotesvwap = MathUtil.round(quotesvwap, pricePrecision, BigDecimal.ROUND_CEILING);
				}
			}

		}
		catch ( Exception e )
		{
			if ( log.isDebugEnabled() )
			{
				log.debug("OrderWorkflowcalculatorC.getMarketVWAPPrice:Exception while calculating  market VWAP price", e);
			}
			quotesvwap = null;
		}
		return quotesvwap;
	}

    public boolean considerRiskNetLiquidity( Organization makerOrg, String customerOrgName ){
        if( ISUtilImpl.getInstance().isVenueProvider( makerOrg) ){
            if( aggregationServiceMBean.isRiskNetSubscriptionEnabled( customerOrgName ) ){
                return true;
            }
        }
        return false;
    }

	public Double getMarketVWAPPrice(Order order, OrderBook book)
	{
		EntityDescriptor ed = order.getEntityDescriptor();
		double price = ed.getMatchPrice();
		if ( order.isStop() || ed.isMarketOrder() )
		{
			price = 0.0;
		}

		if ( ed.isBid() )
		{
			return getMarketVWAPPrice(book.getSortedOfferQuotes(), ed.isDealingInTerm(), false, ed.getDerivedPreferredProviders(), ed.getSnapshotComparator(), order.getUnfilledAmount(), true, price, order.getEntityDescriptor().getPricePrecision(), book.getOrganization());
		}
		else
		{
			return getMarketVWAPPrice(book.getSortedBidQuotes(), ed.isDealingInTerm(), false, ed.getDerivedPreferredProviders(), ed.getSnapshotComparator(), order.getUnfilledAmount(), false, price, order.getEntityDescriptor().getPricePrecision(), book.getOrganization());
		}
	}	
}
