package com.integral.is.oms.descriptors;

import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.currency.Currency;
import com.integral.finance.fx.FXRate;
import com.integral.finance.fx.FXRateBasis;
import com.integral.is.oms.Order;
import com.integral.message.MessageHandler;
import com.integral.model.dealing.TimeInForce;
import com.integral.time.IdcDate;
import com.integral.user.Organization;
import com.integral.user.User;

import java.util.Comparator;
import java.util.List;
import java.util.Map;

//Copyright (c) 2001-2005 Integral Development Corp.  All rights reserved.

/**
 * Description of the interface or class.
 *
 * <AUTHOR> Development Corp.
 */
public interface EntityDescriptor
{
	int ED_TYPE_LimitRequestEntityDescriptor = 1;
	int ED_TYPE_MarketDataOrder = 2;
	int ED_TYPE_OrderRequestEntityDescriptor = 3;
	int ED_TYPE_SpotQuoteEntityDescriptor = 4;
	int ED_TYPE_StagingOrderEntityDescriptor = 5;
	int ED_Type_MarketDataOrderForPQEntityDescriptor = 6;

	int ED_Type_MarketDataOrderForAtBestEntityDescriptor = 7;

	/**
	 *
	 * @return ED_TYPE constant associated with the Entity Descriptor.
	 */
	public int getEntityDescriptorType();

	/**
	 *
	 */
	public void reset();

	/**
	 * Initializes the entity descriptor for the order book name.
	 *
	 * @param orderBookName order book name
	 * @param fiOrgName
	 */
	public void init( String orderBookName, String fiOrgName );

	/**
	 * returns the criteria to be used for matching
	 *
	 * @return matching criteria
	 */
	public MatchingCriteria getMatchingCriteria();

	/**
	 * Returns the entity which is used to create this order.
	 *
	 * @return entity
	 */
	public Object getEntity();

	/**
	 * Returns the amount of the order.
	 *
	 * @return order amount
	 */
	public double getOrderAmount();

	/**
	 * Returns the limit price of the order. This is the price at which the order will be executed.
	 *
	 * @return order price
	 */
	public double getOrderPrice();

	/**
	 * Returns the base currency shortname for the order.
	 *
	 * @return base currency short name
	 */
	public String getBaseCurrency();

	/**
	 * Returns the base currency of the order.
	 *
	 * @return base currency
	 */
	public Currency getBaseCcy();

	/**
	 * Returns the Variable currency shortname for the order
	 *
	 * @return variable currency short name
	 */
	public String getVariableCurrency();

	/**
	 * Returns the variable currency object of the order.
	 *
	 * @return variable currency
	 */
	public Currency getVariableCcy();

	/**
	 * Returns true if the order is a bid order.
	 *
	 * @return bid
	 */
	public boolean isBid();

	/**
	 * Returns the external order id associated with the order originator.
	 * In case of quotes this represents GUID generated by adaptor which doesn't change
	 * internal republication IS layer
	 * @return external order id
	 */
	public String getExternalOrderId();

	/**
	 * Returns the currency pair of the order.
	 *
	 * @return currency pair
	 */
	public String getCurrencyPair();

	/**
	 * Returns the short name of the owning organization of the order.
	 *
	 * @return organization
	 */
	public String getOrganization();

	/**
	 * Returns the Organization Reference of the owning organization of the order.
	 *
	 * @return org reference
	 */
	public Organization getOrganizationReference();

	/**
	 * Returns the short name of the user who made the order.
	 *
	 * @return user
	 */
	public String getUser();

	/**
	 * Returns the Reference of user who made the order.
	 *
	 * @return user reference
	 */
	public User getUserReference();

	/**
	 * indicates whether the entity is Quote or an executable order.
	 * implys only one order to be maintained in order book at a time
	 *
	 * @return market data
	 */
	public boolean isMarketData();

	/**
	 * get order book name to which this order belongs
	 *
	 * @return order book name
	 */
	public String getOrderBookName();

	/**
	 * get stream for the order applicable for quote only for orders determined at the time of matching
	 *
	 * @return stream
	 */
	public String getStream();

	/**
	 * indicates whether order is active or not
	 * market rate are active if the quote classification is tradable
	 * orders are always active
	 *
	 * @return active
	 */
	public boolean isActive();

	/**
	 * The price name of the order
	 * would be BID and OFFER for normal quotes
	 * would be BID1 , BID2 .... ORDER1 , ORDER2 for tiered quote
	 *
	 * @return price name
	 */
	public String getPriceName();

	/**
	 * @return disclose amount
	 */
	public double getDiscloseAmount();

	/**
	 * Tells if order execution is enabled for this entity or not.
	 * Currently this flag is linked to com.integral.user.Organization.orderExecution variable.
	 *
	 * @return order execution enabled
	 */
	public boolean isOrderExecutionEnabled();

	/**
	 * Tells if this entity can be broadcasted.
	 *
	 * @return broadcast enabled
	 */
	public boolean isBroadcastEnabled();

	/**
	 * Tells is this entity can be expired.
	 *
	 * @return expiry enabled
	 */
	public boolean isExpiryEnabled();

	/**
	 * indicates whether order is taker or not
	 *
	 * @return taker
	 */
	public boolean isTaker();

	/**
	  * returns the minimum fill amount
	  */
	public double getMinimumFillAmount();

	/**
	 * Original minimum fill amount submitted. 
	 * @return
	 */
	public double getMinimumFillAmountSubmitted();

	/**
	 * gets the org which placed the order This will be different from user org when it is placed by an SDOrg
	 *
	 * @return Organization
	 */
	public Organization getPlacedByOrg();

	/**
	 * check whether LP crossing is enabled or not
	 *
	 * @return lp crossing enabled
	 */
	public boolean isLPCrossingEnabled();

	/**
	 * return whether order is FOK order
	 *
	 * @return Fill Or Kill
	 */
	public boolean isFOK();

	/**
	 * return whether order is IOC order
	 *
	 * @return Immediate Or Cancel
	 */
	public boolean isIOC();

	/**
	 *
	 * @return true if the order is a Day Order.
	 */
	public boolean isDayOrder();

	/**
	 * returns true if this is a price taking request from IS.
	 *
	 * @return lift order
	 */
	public boolean isLiftOrder();

	/**
	 * @return true if the ordertype is Market order.
	 */
	public boolean isMarketOrder();

	/**
	 * @return true if the ordertype is Market order and marketRange is specified
	 */
	public boolean isMarketRangeOrder();

	/**
	 * @return market range
	 */
	public double getMarketRangeAppliedPrice();

	/**
	 * @return multi-tier
	 */
	public boolean isMultiTier();

	/**
	 * get FXRate Obj
	 *
	 * @return rate object
	 */
	public FXRate getRateObj();

	public int getTierNo();

	public void setEntity( Object entity );

	/**
	 * @return Stop Price
	 */
	public double getStopPrice();

	/**
	 * @return stop
	 */
	public boolean isStop();

	/**
	 * @return trigger type
	 */
	public int getTriggerType();

	/**
	 * @return stop limit
	 */
	public boolean isStopLimit();

	/**
	 * @return stop market range
	 */
	public boolean isStopMarketRange();

	/**
	 * Returns true if customers wants only top of the book prices.
	 *
	 * @return best price
	 */
	public boolean isBestPrice();

	/**
	 * Return the at rate trigger for stop orders.
	 *
	 * @return at rate trigger
	 */
	public boolean isAtRateTrigger();

	/**
	 * Returns whether the execution strategy is VWAP(Volume Weighted Average Price).
	 *
	 * @return VWAP
	 */
	public boolean isVWAP();

	/**
	 * Returns true is price encapsulates by this order is from Multi-stream provider.
	 *
	 * @return multi stream
	 */
	public boolean isMultiStream();

	public boolean isSupportsMarketRangeOrders();

	public boolean isUsedQuoteMatchEnabled();

    public int getAcceptanceMode();

	public boolean isOCOOrder();

	public void setOCOOrder( boolean isOCOOrder );

	public boolean isOUOOrder();

	public void setOUOOrder( boolean isOUOOrder , int ouoContingencyType);
	
	public boolean isOTOOrder();

	public void setOTOOrder( boolean isOTOOrder );


	/**
	 * Returns price priority asked by user. Defaults to Time if none specified.
	 *
	 * @return price priority
	 */
	public String getPricePriority();

	/**
	 * Returns list of provider should used for order matching. Provider at index
	 * '0' has higher priority then Provider at index '1' and so on.
	 *
	 * @return preferred providers
	 */
	public List<Organization> getPreferredProviders();

    /**
     * Returns the list of preferred providers to match for the order request.
     * This is the derived list from user input and liquidity provisioning.
     *
     * @return list of preferred providers
     */
    public String getDerivedPreferredProviders();

	/**
	 * Returns comparator to be used for sorting quotes for this order.
	 * Not applicable to {@link SpotQuoteEntityDescriptor}
	 *
	 * @return comparator
	 */
	Comparator<Order> getComparator();

	boolean isDealingInTerm();

	/**
	 * Returns the precision of the order rate. This is the spot rate precision of the currency pair.
	 *
	 * @return spot rate precision of the currency pair.
	 */
	public int getPricePrecision();

	/**
	 * Return the order price adjusted with market range if applicable.
	 *
	 * @return price to match
	 */
	public double getMatchPrice();

	public double getMinimumTradeSize();

	/**
	 * Returns true is this entity is MarketRate and Broadcast order.
	 *
	 * @return
	 */
	public boolean isMarketRateBroadcastOrder();

    public boolean isVenueMarketRate();

	/**
	 * Return provider's rate id.
	 */
	public String getProviderRateId();

	public String getMatchedOrderIds();

	public double getBasePrice();

	public boolean isUnspreadedMatchEnabled();

	Comparator<Order> getSnapshotComparator();

	/**
	 * Accessor method for dealing price's value date.
	 *
	 * @return
	 */
	public IdcDate getValueDate();

	public LegalEntity getLegalEntity();

	public void setLPCrossingEnabled( boolean b );

	public boolean isSweepExcInst();

	public double getRegularSize();

    /**
     * Returns the TWAP regular size for this entity descriptor. Implementations can throw UnsupportedOperationException
     * if they doesn't support TWAP orders.
     */
	public double getTWAPRegularSize();

	/**
	 * Returns true is underlying order is TWAP order.
	 * @return
	 */
	public boolean isTWAPOrder();
		
	
	/** ---------------------------------------------------------------------------------------- */ 

	public boolean isRandomizeTwapSliceSize();
	
	public boolean isRandomizeTwapSliceInterval();
		
	public double getTwapMinimumSliceSize();
	
	public double getTwapSliceTopOfBookPercent();
	
	public double getTwapSliceSize();
	
	public long getTwapSliceInterval();
	
	public long getTwapMinimumSliceInterval();
	
	/** ---------------------------------------------------------------------------------------- */

	/**
	 * Returns true is order is strategy order. Peg orders are strategy orders
	 * @return
	 */
	public boolean isStrategyOrder();

	/**
	 * Returns version of market data objects
	 * @return
	 */
	public long getVersionNumber();

	public boolean isEntityVersionUpdated();

	/**
	 * 
	 */
	public void setExpirableIOCOrder();

	public boolean isFOKSlices();

	/**
	 * return true if order can be pegged.
	 * @return
	 */
	public boolean isPegOrder();

	/**
	 * return true is order is currently pegged to market rate.
	 * if false then it is pegged to limit rate.
	 * @return
	 */
	public boolean isPeggedToMarket();

	public void setPeggedToMarket( boolean value );

	public double getPegPriceAtSubmission();

    public void setNewPegPrice( double price );

	public FXRateBasis getRateBasis();

	/**
	 * @param newOrderAmount
	 */
	public void amendAmount( Double newOrderAmount );

	public double getLimitRate();

	public boolean isSDOrder();

	/**
	 * @param newOrderRate
	 */
	public void amendRate( Double newOrderRate, double newPegRate );

	public double getDefaultRangeAppliedPriceForMkt();

	public double getDefaultRangeForMkt();

	public void setOrderPegEnable( boolean value );

	public boolean isOrderPegEnable();

	public void amendTriggerRate( Double newTriggerRate );

	public boolean isStopLossTriggered();

	/**
	 *
	 * @return true if the strategy order's execution has been suspended
	 */
	public boolean isStrategyExecutionSuspended();

    /**
     * Returns true if the order has the giveback spread computed and available.
     *
     * @return true if the order has the giveback spread computed and available
     */
    public boolean isGivebackApplied();

    /**
     * Returns the Limit Matching Price with Giveback spread applied.
     *
     * @return the Limit Matching Price with Giveback spread applied.
     */
    public double getAdjustedMatchPrice();

	public abstract boolean isGTC();

	public abstract long getDerivedExpiryTime();
	
	public boolean isSetPreferredProviders();
	
	public Map<String, Boolean> getPreferredProvidersMap();

    public double getNoWorseThanPrice();

    public boolean isIgnoreMinMatchSizeForResidue();

    public boolean isIgnoreRegularSizeForResidue();

    public boolean isClobRatesMatchAllowed();

    public boolean isRiskNetRatesMatchAllowed();

    void setOrderMatchingBeginTime( long time );

    void setDerivedPreferredProviders(String preferredProviders);

    List<Organization> getPreferredProviderOrganizations();

    List<Organization> getExclusionList();

    void updateEntityOnSuspend();

    void updateEntityOnResume();

    void setTriggeredByRate(Order order, double triggeredByPrice, String triggeredBy );

    /**
     * Valid for lift only
     * @return
     */
    boolean isPBCoverTradeEnabled();

    long getOrderReceivedTime();

	public int getOuoContingencyType();
	
	/** ---------------------------------------------------------------------------------------- */ 
	
	public int getExecutionFlags();
	
	public double getPassiveTime();
	
	public long getOrderExecutionEndTime();
	
	public void setExecutionSuspended(Boolean value);
	
	public TimeInForce getTimeInForce();
	
	public double getTOBRange();
	
	public char getPegType();
	
	public double getPegOffset();
	
    public double getPegOffsetIncrement();
    
    public long getPegOffsetIncrementInterval();
    
    public boolean isRandomizePegOffsetIncrement();

    /** ---------------------------------------------------------------------------------------- */ 
    
    long getSubmitTimeNano();
    
    long getSubmissionTime();

    long getCancellationReceivedTime();

    long getCancellationReceivedTimeNano();
    
    
    /** ---------------------------------------------------------------------------------------- */ 
    
    public boolean isWarmUpRequest();
    
	public MessageHandler getMessageHandler();
	
	public String getSLTriggerType();
    
    /** ---------------------------------------------------------------------------------------- */
	
	public void notifyStrategyOrderSubmissionToUser();
	
	public void setBADirectedOrder();

	void setLPDirectedOrder();
	
	public boolean isPrimaryOrder();

    public void addProcessingTime(String function, long delta);

    public boolean isFromFXIDirectStream();

    public boolean isRiskNetDirectedOrder();

    public boolean isAlgoRiskNetDirectedOrder();

    public boolean isClobDirectedOrder();

    public boolean isDirectedOrder();

    public boolean isFixingTWAPOrder();
    
    public boolean isFixingTWAPStrategyOrder();

    public boolean isFixingMarketOrder();

    public boolean isUsePriceRegenerationInOrderMatch();

    public String getProviderTPCcyPairKey();

  	public boolean isDirectedOrderFixing();
  
    public double getPreRateFilledAmount();
  
    public boolean isNoLastLookStreamQuote();
  	
  	public void recordFill();
  	
  	public void recordReject();
  
    public boolean isRoutingPermitted();
  
  	public boolean isStreamCategoryCheckEnabled();
  
  	public int getStreamCategoryFlag();
  
    public boolean isExecutionDisabled();
  
    public boolean isNoWorseThanPriceProvidedByCustomer();
      
    public boolean isSmartSliceOrder();
    
    public boolean isLitSwitchOrder();
    
    public boolean isDarkSwitchOrder();
    
    public boolean isOMSOrder();
      
  	public void turnOffSmartSlicing();
  
  	public Organization getDefaultVenueForRouting();

    public double getVwapPriceProtectionRangeInBasisPoints();

    boolean isOnlyFAStreams();

    public boolean isLimitOrder();

    public boolean isPQOrder();

    public String getPQReferenceQuoteId();

    public boolean isPQDirectedOrder();

	boolean isAlgoAtBestDirectedOrder();

	boolean isV4Initiated();
	public void setV4Initiated();

	boolean isCreditCheckDisabled();

    boolean isUndisclosedProvider();

	boolean isUndisclosedProviderMatchingDisabled();

    /** ---------------------------------------------------------------------------------------- */
    /** Trailing Stop Order Methods */
    /** ---------------------------------------------------------------------------------------- */

    /**
     * Returns true if this is a trailing stop order.
     * @return true if this is a trailing stop order
     */
    boolean isTrailingStopOrder();

	/**
     * Returns the trailing distance for trailing stop orders.
     * This is the distance the stop price trails behind the best achieved price.
     * @return trailing distance
     */
    double getTrailingStopDistance();

    /**
     * Sets the trailing distance for trailing stop orders.
     * @param trailingDistance the distance to trail behind best price
     */
    void setTrailingStopDistance(double trailingDistance);

    /**
     * Returns the best price achieved for trailing stop orders.
     * For buy orders, this is the lowest price seen.
     * For sell orders, this is the highest price seen.
     * @return best price achieved
     */
    double getTrailingStopBestPrice();

    /**
     * Sets the best price achieved for trailing stop orders.
     * @param bestPrice the best price achieved so far
     */
    void setTrailingStopBestPrice(double bestPrice);

    /**
     * Sets the stop price for the order. Used by trailing stop logic to update trigger price.
     * @param stopPrice the new stop price
     */
    void setStopPrice(double stopPrice);

	/**
	 * runtime counter for stop price updates
	 * this will get reset to 0 when the application restarts
	 * @return number of stop price updates
	 */
	long getStopPriceUpdateCount();

	long getLastPersistedStopPriceUpdateCount();

	void updateEntityOnTriggerUpdate(boolean persist);

	void persistEntityOnTriggerUpdate();

	long getLastNotifiedStopPriceUpdateCount();

	void setLastNotifiedStopPriceUpdateCount(long count);
}