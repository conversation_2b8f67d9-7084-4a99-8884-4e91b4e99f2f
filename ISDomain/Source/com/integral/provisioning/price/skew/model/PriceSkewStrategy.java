package com.integral.provisioning.price.skew.model;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.NavigableMap;
import java.util.TreeMap;
import java.util.concurrent.ConcurrentSkipListMap;

import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.dealing.DealingPrice;
import com.integral.finance.dealing.skew.SkewStrategy;
import com.integral.finance.marketData.fx.FXMarketDataElement;
import com.integral.is.common.ISConstantsC;
import com.integral.is.common.cache.MDSFactory;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.provisioning.price.notification.PriceProvisionEvent;
import com.integral.provisioning.price.skew.cache.PriceSkewStrategyCacheC;
import com.integral.provisioning.price.skew.log.PSSLogger;
import com.integral.provisioning.price.skew.scheduler.SkewEventScheduler;
import com.integral.user.Organization;
import com.integral.user.OrganizationRelationship;
import com.integral.util.Tuple;

public class PriceSkewStrategy
{
	Log log = LogFactory.getLog(this.getClass());
	/*
	 * Possible value BID = DealingPrice.BID, OFFER = DealingPrice.OFFER , NEUTRAL = -1;
	 */
	volatile int currentSkewedSide = -1; 
	volatile int lastSkewedSide;
	//double cumulativeBaseAmount;
	
	boolean isSkewStrategyEnabled;
	double amountThreshold;
	double amountThresholdInBase;
	long expiryTime;

	private Organization fi;
	private Organization lp;
	private CurrencyPair ccyp;
	
	private String name = ""; 
	
	//private List<TradeInfo> participantTrades = new ArrayList<TradeInfo>();
	private NavigableMap<Long, List<TradeInfo>> participantTrades;
	
	private SkewStrategy skewStrategy;
	
	volatile private boolean isSkewEffective;
	
	private long accumulationTime;
	
	private double activationLimit;
	
	private boolean backToBack;
	
	private int spotSpreadMultiplier;
	
	private long maxRiskThresholdPeriod;
	
	volatile private boolean maxRiskEventInEffect;
	
	private static final long EXPIRY_TOLERANCE = 100; // Tolerance for rescheduling
	
	private double lastSkewTriggerAmount = 0.0;

	
	public PriceSkewStrategy(Organization fi, Organization lp, CurrencyPair ccyp)
	{
		this.fi = fi;
		this.lp = lp;
		this.ccyp = ccyp;
		this.name = fi.getShortName() + '_' + lp.getShortName() + '_' + ccyp.getName(); 
		//this.participantTrades = new ConcurrentSkipListMap<Long, List<TradeInfo>>();
		this.participantTrades = new TreeMap<Long, List<TradeInfo>>();
		OrganizationRelationship rel = lp.getOrgRelationship(fi, ISConstantsC.LP_ORG_RELATIONSHIP);
		skewStrategy = rel.getCustomerPriceProvision().getSkewStrategy();
		this.isSkewStrategyEnabled = skewStrategy.isEnabled();
		this.amountThreshold = skewStrategy.getAmountThreshold();
		this.expiryTime = skewStrategy.getExpiryTime();
		this.accumulationTime = skewStrategy.getAccumulationTime();
		this.activationLimit = skewStrategy.getActivationLimit();
		this.backToBack = skewStrategy.isBackToBack();
		this.spotSpreadMultiplier = skewStrategy.getSpotSpreadMultiplier();
		this.maxRiskThresholdPeriod = skewStrategy.getMaxRiskThresholdPeriod();
	}

	public Organization getFI()
	{
		return fi;
	}

	public Organization getLP() 
	{
		return lp;
	}
	/*
	public double getCumulativeBaseAmount() 
	{
		return cumulativeBaseAmount;
	}
	*/

	public CurrencyPair getCcyp() 
	{
		return ccyp;
	}

	public String getName() 
	{
		return name;
	}

	public boolean isSkewEffective() 
	{
		return isSkewEffective;
	}

	public SkewStrategy getSkewStrategy() 
	{
		return skewStrategy;
	}

	public int getCurrentSkewedSide() 
	{
		return currentSkewedSide;
	}

	synchronized public List<TradeInfo> getParticipantTrades() 
	{
		List<TradeInfo> tradeList = new ArrayList<TradeInfo>();
		for (Map.Entry<Long, List<TradeInfo>> entry : this.participantTrades.entrySet()) {
			tradeList.addAll(entry.getValue());
		}
		return tradeList;
	}
	
	public double getAmountThreashold()
	{
		return amountThreshold;
	}
	
	public boolean isSkewStrategyEnabled()
	{
		return isSkewStrategyEnabled;
	}
	
	public long getExpiryTime()
	{
		return expiryTime;
	}
	

	/**
	 * @param trdInfo
	 */
	synchronized public void addTrade( TradeInfo trdInfo )
	{
//		if( !skewStrategy.isEnabled() )
//		{
//			if( log.isDebugEnabled() )
//			{
//				log.debug("PriceSkewStrategy.addTrade - Strategy is not enabled.fi="+fi.getShortName()+
//					",lp="+lp.getShortName()+
//					",cp="+ccyp.getName());
//			}
//			return;
//		}
		
		long arrival = System.currentTimeMillis();
		trdInfo.setTimestamp(arrival);		
		PSSLogger.getInstance().logSkewStrategyNewTradeInfo(this, trdInfo);
		boolean isFlipped = false;
		boolean isThreasholdBreached = false;
		double amountThresholdUSD = skewStrategy.getAmountThreshold();
		double amountThresholdBase = MDSFactory.getInstance().convertAmount(this.fi.getShortName(), PriceSkewStrategyUtilC.CURRENCY_USD, this.ccyp.getBaseCurrency(), amountThresholdUSD, null);
		double activationLimitUSD = skewStrategy.getActivationLimit();
		double activationLimitBase = 0.0;
		if (activationLimitUSD > 0.0) {
			activationLimitBase =  MDSFactory.getInstance().convertAmount(this.fi.getShortName(), PriceSkewStrategyUtilC.CURRENCY_USD, this.ccyp.getBaseCurrency(), activationLimitUSD, null);
		}
		boolean disableSkew = false;
		double tradeBaseAmount = trdInfo.getBaseAmount();
		if (amountThresholdUSD != 0.0 && amountThresholdBase == 0.0) {
			// conversion couldn't happen from USD amount to base amount because market data element not available
			disableSkew = true;
		}
		double cumulativeBaseAmount = 0.0;
		if( currentSkewedSide == -1 )
		{
			currentSkewedSide = trdInfo.isBuyingBase() ? DealingPrice.OFFER : DealingPrice.BID;
			cumulativeBaseAmount = trdInfo.getBaseAmount();
			isThreasholdBreached = cumulativeBaseAmount >= amountThresholdBase && !disableSkew;
			long timestamp = trdInfo.getTimestamp();
			//participantTrades.add(trdInfo);
			List<TradeInfo> tiList = participantTrades.get(timestamp);
			if (tiList == null) {
				tiList = new ArrayList<TradeInfo>();
				participantTrades.put(timestamp, tiList);
			}
			tiList.add(trdInfo);
		}
		else
		{
			int tradeSide = trdInfo.isBuyingBase() ? DealingPrice.OFFER : DealingPrice.BID;
			if( currentSkewedSide == tradeSide )
			{
				//cumulativeBaseAmount += trdInfo.getBaseAmount();
				long timestamp = trdInfo.getTimestamp();
				List<TradeInfo> tiList = participantTrades.get(timestamp);
				if (tiList == null) {
					tiList = new ArrayList<TradeInfo>();
					participantTrades.put(timestamp, tiList);
				}
				tiList.add(trdInfo);
				cumulativeBaseAmount = computeCumulativeBaseAmount();
				isThreasholdBreached = cumulativeBaseAmount >= amountThresholdBase && !disableSkew;
				//participantTrades.add(trdInfo);

			}
			else
			{
				//1. Side has changed - reset.
				isFlipped = true;
				currentSkewedSide = tradeSide;
				cumulativeBaseAmount = trdInfo.getBaseAmount();
				//isThreasholdBreached = cumulativeBaseAmount >= skewStrategy.getAmountThreshold();
				isThreasholdBreached = cumulativeBaseAmount >= amountThresholdBase && !disableSkew;
				participantTrades.clear();
				long timestamp = trdInfo.getTimestamp();
				List<TradeInfo> tiList = participantTrades.get(timestamp);
				if (tiList == null) {
					tiList = new ArrayList<TradeInfo>();
					participantTrades.put(timestamp, tiList);
				}
				tiList.add(trdInfo);
				if ( isSkewEffective )
				{
					//2. if currently skewing then stop skewing.
					isSkewEffective = false;
					maxRiskEventInEffect = false;
					// Cancel corresponding expiry task
					this.lastSkewTriggerAmount = 0.0;
					SkewEventScheduler.getInstance().cancel(this);

					if ( isFlipped && !isThreasholdBreached ) 
					{
						// Do not send STOP skew, instead directly send START with flipped side
						PriceProvisionEvent ppe = sendStopSkewEvent();
						PSSLogger.getInstance().logFlippedSkewEvent(this, ppe);
					}
				}
			}
		}
		
		if( isThreasholdBreached )
		{
			//if strategy is already effective then continue;
			if( !isSkewEffective )
			{
				isSkewEffective = true;
				this.lastSkewTriggerAmount = cumulativeBaseAmount;
				
				// Schedule expiry task (stop event)
				SkewEventScheduler.getInstance().scheduleExpiry(this, skewStrategy.getExpiryTime() * 1000);
				
				//Send skew start event
				PriceProvisionEvent ppe = sendStartSkewEvent(currentSkewedSide, cumulativeBaseAmount);
				
				// If STOP event is not sent in case of flipped and threshold breach
				if( isFlipped )
				{
					PSSLogger.getInstance().logFlippedSkewEvent(this, ppe);
				}
				else
				{
					PSSLogger.getInstance().logStartSkewEvent(this, ppe);
				}

			}
			else
			{
				//Send skew start event with new cumulative base amount
				this.lastSkewTriggerAmount += tradeBaseAmount;
				if (this.maxRiskEventInEffect) {
					// Max Risk event in effect so do not send any further event until expiry as max risk event has no effect on cumulative amount

					if ( log.isDebugEnabled() ) 
					{
						log.debug("PriceSkewStrategy.addTrade : Maxrisk Event in effect. Continuing. Strategy="
								+ this.toString());
					}
				} else {
					// check if accumulated amount is greater than activation limit
					if (activationLimitBase > 0.0 && this.lastSkewTriggerAmount >= activationLimitBase) {
						this.maxRiskEventInEffect = true;
						// stop already scheduled expiry
						SkewEventScheduler.getInstance().cancel(this);
						PriceProvisionEvent ssse = sendStopSkewEvent();
						PSSLogger.getInstance().logStopSkewEvent(this, ssse);
						PriceProvisionEvent ppe = sendMaxRiskSkewEvent(currentSkewedSide, this.lastSkewTriggerAmount);
						SkewEventScheduler.getInstance().scheduleExpiry(this, skewStrategy.getMaxRiskThresholdPeriod() * 1000);
						PSSLogger.getInstance().logStartSkewEvent(this, ppe);
						if (log.isDebugEnabled()) {
							log.debug("PriceSkewStrategy.addTrade : Skew already in effect. Continuing. Strategy="
									+ this.toString());
						}
					} else {
						PriceProvisionEvent ppe = sendStartSkewEvent(currentSkewedSide, this.lastSkewTriggerAmount);
						PSSLogger.getInstance().logStartSkewEvent(this, ppe);
						if ( log.isDebugEnabled() ) 
						{
							log.debug("PriceSkewStrategy.addTrade : Skew already in effect. Continuing. Strategy="
									+ this.toString());
						}
					}
				}
			}
		}
	}
	
	private double computeCumulativeBaseAmount() {
		// check total accumulated amount in configured accumulation time
		// basically remove all the entries older than accumulation time
		double cumulativeBaseAmount = 0.0;
		try {
			StringBuilder sb = new StringBuilder("PriceSkewStrategy.computeCumulativeBaseAmount:");
			sb.append(":FI:").append(fi.getShortName()).append(":LP:").append(lp.getShortName()).append(":CCYPAIR:").append(ccyp.getName());
			if (this.participantTrades.size() > 0) {
				long lastTradeTime = this.participantTrades.lastKey();
				sb.append(":LastTradeTime:").append(lastTradeTime);
				long accumlationStartTime = lastTradeTime - (this.skewStrategy.getAccumulationTime()*1000);
				sb.append(":AccumulationStartTime:").append(accumlationStartTime);
				// take submap from accumulation start time to last entyry
				NavigableMap<Long, List<TradeInfo>> submap = this.participantTrades.subMap(accumlationStartTime, true, lastTradeTime, true);
				for (Map.Entry<Long, List<TradeInfo>> entry : submap.entrySet()) {
					List<TradeInfo> trdInfoList = entry.getValue();
					for (TradeInfo trdInfo : trdInfoList) {
						cumulativeBaseAmount += trdInfo.getBaseAmount();
					}
				}
				sb.append(":CumulatibeBaseAmount:").append(cumulativeBaseAmount);
				// delete any key less than accumulation start time
				this.participantTrades.headMap(accumlationStartTime, false).clear();
			}
			if (log.isDebugEnabled()) {
				log.debug(sb.toString());
			}
			
		} catch (Throwable t) {
			log.info("PriceSkewStrategy.computeCumulativeBaseAmount:Problem with computing cumulative amount:", t);
			cumulativeBaseAmount = 0.0;
		}
		return cumulativeBaseAmount;
		
	}
	
	synchronized public void handleExpired()
	{

		// if accumulation amount reaches to activate amount then 
		// increase the spread as per spot spread multiplier
		// and schedule expiry after activation time
		/*
		if (this.activationLimit > 0) {
			//double cumulativeBaseAmount = computeCumulativeBaseAmount();
			double cumulativeBaseAmount = this.lastSkewTriggerAmount;

			double activationLimitBase = MDSFactory.getInstance().
					convertAmount(this.fi.getShortName(), PriceSkewStrategyUtilC.CURRENCY_USD, this.ccyp.getBaseCurrency(), this.activationLimit, null);
			if (cumulativeBaseAmount >= activationLimitBase) {
				if (this.isSkewEffective) {
					// if skew is in effect then only fire max risk even if accumulated amount breached the activation limit
					if (!this.maxRiskEventInEffect) {
						this.maxRiskEventInEffect = true;
						sendMaxRiskSkewEvent(currentSkewedSide, cumulativeBaseAmount);
						// schedule expiry of max risk skew event to max risk threshold period
						SkewEventScheduler.getInstance().scheduleExpiry(this, this.maxRiskThresholdPeriod * 1000);
						return;
					}  else {
						if (log.isDebugEnabled()) {
							log.debug("PriceSkewStrategy.handleExpired:Max Risk Event was already in effect so not activating it again");
						}
					}
				} else {
					if (log.isDebugEnabled()) {
						log.debug("PriceSkewStrategy.handleExpired:Max Risk Event was not activated as Normal skew is not in effect. "
								+ "Max Risk Event can only be fired after normal skew event");
					}
				}
			}
		}
		*/
		if(  isSkewEffective  )
		{
			PriceProvisionEvent ppe = sendExpireSkewEvent();
			PSSLogger.getInstance().logExpireSkewEvent(this, ppe);
			// reset last skew trigger amount to zero
			this.lastSkewTriggerAmount = 0.0;
			this.participantTrades.clear();
			this.maxRiskEventInEffect = false;
			this.isSkewEffective = false;
			PriceSkewStrategyCacheC.getInstance().remove(fi, lp, ccyp);
			PSSLogger.getInstance().logSkewStrategyRemoval(this);
			// Any other object destroy procedure?
		}
		else
		{
			log.info("PriceSkewStrategy.handleExpired . Strategy is not effective. strategy="+name);
		}
		
	}
/*	
	
	public void handleExpired()
	{
		long remainingTime = 0;
		
		synchronized ( this )
		{
			if ( !participantTrades.isEmpty() )
			{
				long now = System.currentTimeMillis();
				TradeInfo latestTradeInfo = participantTrades.get(participantTrades.size() - 1);
				remainingTime =  latestTradeInfo.getTimestamp() + (skewStrategy.getExpiryTime() * 1000) - now; // Latest trade timestamp
			}
		}
		
		if ( remainingTime <= EXPIRY_TOLERANCE ) // tolerance level
		{
			if(  isSkewEffective  )
			{
				log.info("PriceSkewStrategy.handleExpired : delta <= tolerance [" + remainingTime + "ms <= " + EXPIRY_TOLERANCE + "ms] for strategy - " + this.toString());
				PriceProvisionEvent ppe = sendExpireSkewEvent();
				PSSLogger.getInstance().logExpireSkewEvent(this, ppe);
				this.isSkewEffective = false;
				this.participantTrades.clear();
				PriceSkewStrategyCacheC.getInstance().remove(fi, lp, ccyp);
				PSSLogger.getInstance().logSkewStrategyRemoval(this);
				// Any other object destroy procedure?
			}
			else
			{
				log.info("PriceSkewStrategy.handleExpired . Strategy is not effective. strategy="+name);
			}
		}
		else
		{
			// Reschedule for remaining time
			log.info("PriceSkewStrategy.handleExpired : Rescheduling for " + remainingTime + "ms for strategy - " + this.toString());
			SkewEventScheduler.getInstance().scheduleExpiry(this, remainingTime);
		}
	}
*/
	
	synchronized public void handleChanged()
	{
		/*
		 *  Future plan: Look for specific changes in amount threshold and expiry time 
		 *  (other params are verified in PPRCM class) and take actions accordingly.
		 */
		
		// Do proper logging (PSSLogger)
		PSSLogger.getInstance().logSkewStrategyUpdated(this);
		
		// Cancel any scheduled task, if any
		boolean status = SkewEventScheduler.getInstance().cancel(this);
		if ( !status )
		{
			log.warn("PriceSkewStrategy.handleChanged : SES failed to cancel scheduled expiry task for strategy - " + this.toString());
		}
		
		// Send stop skew event
		PriceProvisionEvent ppe = sendStopSkewEvent();
		PSSLogger.getInstance().logStopSkewEvent(this, ppe);

		// Remove self from strategy cache
		PriceSkewStrategyCacheC.getInstance().remove(fi, lp, ccyp);
		PSSLogger.getInstance().logSkewStrategyRemoval(this);
	}
	
	public PriceProvisionEvent sendStartSkewEvent (int skewedSide, double cumulativeBaseAmount)
	{
		log.info("PriceSkewStrategy.sendStartSkewEvent. Start price skew. " +
				"fi="+fi.getShortName()+
				",lp="+lp.getShortName()+
				",cp="+ccyp.getName()+
				",side="+skewedSide +
				",cumulativeBaseAmount="+cumulativeBaseAmount);
		PriceProvisionEvent ppe = PriceSkewStrategyUtilC.sendStartSkewEvent(fi, lp, ccyp, skewedSide, cumulativeBaseAmount);
		return ppe;
	}
	
	public PriceProvisionEvent sendMaxRiskSkewEvent (int skewedSide, double cumulativeBaseAmount)
	{
		
		log.info("PriceSkewStrategy.sendMaxRiskSkewEvent. Start price skew. " +
				"fi="+fi.getShortName()+
				",lp="+lp.getShortName()+
				",cp="+ccyp.getName()+
				",side="+skewedSide +
				",cumulativeBaseAmount="+cumulativeBaseAmount + 
				",spotSpreadMultiplier="+spotSpreadMultiplier);
		PriceProvisionEvent ppe = PriceSkewStrategyUtilC.sendMaxRiskSkewEvent(fi, lp, ccyp, skewedSide, 
				cumulativeBaseAmount, this.skewStrategy.getSpotSpreadMultiplier(), this.skewStrategy.isBackToBack());
		return ppe;
	}
	
	public PriceProvisionEvent sendStopSkewEvent ()
	{
		log.info("PriceSkewStrategy.sendStopSkewEvent. Stop price skew. " +
				"fi="+fi.getShortName()+
				",lp="+lp.getShortName()+
				",cp="+ccyp.getName()+
				",side="+currentSkewedSide+
				",thrs=" + skewStrategy.getAmountThreshold());
		PriceProvisionEvent ppe = PriceSkewStrategyUtilC.sendStopSkewEvent(fi, lp, ccyp);
		return ppe;
	}
	
	public PriceProvisionEvent sendResetSkewEvent ()
	{
		log.info("PriceSkewStrategy.sendResetSkewEvent. Stop price skew. " +
				"fi=ALL"+
				",lp=ALL"+
				",cp=ALL");
		PriceProvisionEvent ppe = PriceSkewStrategyUtilC.sendResetSkewEvent();
		return ppe;
	}
	
	public PriceProvisionEvent sendExpireSkewEvent()
	{
		log.info("PriceSkewStrategy.sendExpireSkewEvent : Expiry time reached. Stop skew price. " +
				"fi="+fi.getShortName()+
				",lp="+lp.getShortName()+
				",cp="+ccyp.getName() );
		PriceProvisionEvent ppe = PriceSkewStrategyUtilC.sendExpireSkewEvent(fi, lp, ccyp);
		return ppe;
	}

	synchronized public PriceProvisionEvent sendHeartbeatEvent()
	{
		PriceProvisionEvent ppe = null;
		if ( isSkewEffective )
		{
			double cumulativeBaseAmount = computeCumulativeBaseAmount();
			if (maxRiskEventInEffect) {
				ppe = sendMaxRiskSkewEvent(currentSkewedSide, cumulativeBaseAmount);
			} else {
				ppe = sendStartSkewEvent(currentSkewedSide, cumulativeBaseAmount);
			}
		}
		else
		{
			ppe = sendStopSkewEvent();
		}
		PSSLogger.getInstance().logHearbeatSkewEvent(this, ppe);
		return ppe;
	}

	@Override
	public String toString() 
	{
		return "PriceSkewStrategy [isSkewEffective=" + isSkewEffective + ", currentSkewedSide=" + currentSkewedSide
				+ ", lastSkewedSide=" + lastSkewedSide
				+ ", amountThreshold=" + amountThreshold + ", isSkewStrategyEnabled=" + isSkewStrategyEnabled
				+ ", expiryTime=" + expiryTime + ", fi=" + fi.getShortName() + ", lp=" + lp.getShortName() 
				+ ", ccyp=" + ccyp.getName() + ", skewStrategy={" + skewStrategy.getToString() 
				+ ",lastSkewTriggerAmount=" + lastSkewTriggerAmount + "}]";
	}
	

}
