package com.integral.provisioning.price.skew.client;

import static com.integral.provisioning.price.notification.PriceProvisionEvent.BS;
import static com.integral.provisioning.price.notification.PriceProvisionEvent.CCYP;
import static com.integral.provisioning.price.notification.PriceProvisionEvent.FI;
import static com.integral.provisioning.price.notification.PriceProvisionEvent.LP;
import static com.integral.provisioning.price.notification.PriceProvisionEvent.OS;
import static com.integral.provisioning.price.notification.PriceProvisionEvent.SPREADCLSF;
import static com.integral.provisioning.price.notification.PriceProvisionEvent.CBA;
import static com.integral.provisioning.price.notification.PriceProvisionEvent.SP;
import static com.integral.provisioning.price.notification.PriceProvisionEvent.SSM;
import static com.integral.provisioning.price.notification.PriceProvisionEvent.BB;
import static com.integral.provisioning.price.notification.PriceProvisionEvent.SS;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

import javax.jms.TextMessage;

import com.integral.config.util.ConfigUtil;
import com.integral.finance.dealing.DealingPrice;
import com.integral.finance.dealing.priceProvision.SpotSpreadProfile;
import com.integral.finance.dealing.skew.SkewSpread;
import com.integral.finance.fx.FXRateBasis;
import com.integral.is.common.Provider;
import com.integral.is.common.util.ISUtilImpl;
import com.integral.is.finance.dealing.ServiceFactory;
import com.integral.is.priceprovision.PriceProvisionRulesCacheManagerC;
import com.integral.is.priceprovision.SpreadInitializationManager;
import com.integral.is.priceprovision.rules.SpreadRule;
import com.integral.is.priceprovision.rules.SpreadRuleParameter;
import com.integral.is.priceprovision.util.PriceProvisionUtilC;
import com.integral.is.util.PriceProvisionUtil;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.lp.Adaptor;
import com.integral.lp.ProviderManagerC;
import com.integral.lp.QuoteDistributionInfo;
import com.integral.lp.QuoteDistributionManager;
import com.integral.lp.SubscriptionUtil;
import com.integral.provisioning.price.notification.PriceProvisionEvent;
import com.integral.provisioning.price.notification.PriceProvisionEventSerializer;
import com.integral.provisioning.price.notification.PriceProvisionEventSerializerFactory;
import com.integral.provisioning.price.notification.PriceProvisionEvent.ActionType;
import com.integral.provisioning.price.skew.config.PriceSkewServiceMbean;
import com.integral.provisioning.price.skew.log.PSSLogger;
import com.integral.user.Organization;
import com.integral.util.MathUtilC;
import com.integral.util.Tuple;


public abstract class SkewEventListenerC implements SkewEventListener {

	Log log = LogFactory.getLog(this.getClass());
	PriceProvisionEventSerializer deserializer;
	final ConcurrentHashMap<String, Long> skewEventSeqCache = new ConcurrentHashMap<String, Long>();
	
	@Override
	public String setup( PriceSkewServiceMbean config )
	{
		try
		{
			deserializer = PriceProvisionEventSerializerFactory.getInstance().getPriceProvisionEventSerializer("skew");
		}
		catch ( Exception e )
		{
			log.error("Failed to setup listener(super)." , e);
			return "Failed to setup listener(super).";
		}
		return null;
	}
	
	protected void handleMessage(String strMsg){
		
		try{
			
			if( deserializer != null )
			{
			    log.info( "SEL.onMessge Received message - " + strMsg );
				
				PriceProvisionEvent event = deserializer.deserialize(strMsg);
				
				if( event == null )
				{
					if( log.isDebugEnabled() )
					{
						log.debug("JMSSEL.onMessage - Invalid message.msg="+strMsg);
					}
					return;
				}
				
				if( event.getActionType() == ActionType.RESET  )
				{
					resetSkew();
					return;
				}
				
				String fi = (String) event.getValue(FI);
				String lp = (String) event.getValue(LP);
				String ccyPair = (String) event.getValue(CCYP);
				double cumBaseAmount = 0.0;
				Object cumBaseAmountObj = event.getValue(CBA);
				if (cumBaseAmountObj instanceof String) {
					cumBaseAmount = Double.parseDouble((String)cumBaseAmountObj);
				}
				String skewProfileStr = null;
				Object skewProfileObj = event.getValue(SP);
				if (skewProfileObj instanceof String) {
					skewProfileStr = (String)skewProfileObj;
				}
				int spotSpreamMultiplier = -1;
				Object ssmObj = event.getValue(SSM);
				if (ssmObj instanceof String) {
					spotSpreamMultiplier = Integer.parseInt((String)ssmObj);
				}
				Object bbObj = event.getValue(BB);
				boolean backToBack = false;
				if (bbObj instanceof String) {
					backToBack = Boolean.parseBoolean((String)bbObj);
				}
				
				int skewSide = -1;
				Object skewSideObj = event.getValue(SS);
				if (skewSideObj instanceof String) {
					skewSide = Integer.parseInt((String)skewSideObj);
				}
				
				StringBuilder cacheKey = new StringBuilder(128);
				cacheKey.append(fi).append('_').append(lp).append('_').append(ccyPair);
				
				Long lastSeq = skewEventSeqCache.get(cacheKey.toString());
				if ( lastSeq != null && lastSeq >= event.getSeqNo() )
				{
					// drop skew event
					log.info( "JMSSEL.onMessage : Dropping out of sequence skew event. Event=" + event.toString() +",lastSeq=" + lastSeq );
					PSSLogger.getInstance().logDroppedSkewEvent(event, lastSeq);
					return;
				}
				else
				{
					// Update cache
					skewEventSeqCache.put(cacheKey.toString(), event.getSeqNo());
				}
				
				SpreadRuleParameter srp =  ServiceFactory.getPriceProvisionRulesCacheManager().getSpreadRuleParameter(fi, lp, ccyPair);
				
				if( srp != null )
				{
					ActionType actType = event.getActionType();
					boolean enableSkew = (actType == ActionType.START);
					boolean resetSpread = (actType == ActionType.STOP || 
								actType == ActionType.EXPIRE || actType == ActionType.RESET);
					double bidSkew = 0.0, offerSkew = 0.0;
					
					String objBidSkew = (String)event.getValue(BS);
					bidSkew = objBidSkew != null ? Double.valueOf(objBidSkew) : bidSkew;
					
					String objOfferSkew = (String)event.getValue(OS);
					offerSkew = objOfferSkew != null ? Double.valueOf(objOfferSkew) : offerSkew;
					
					String objSkewType = (String)event.getValue(SPREADCLSF);
					int skewType = objSkewType != null ? Integer.valueOf(objSkewType) : SkewSpread.PIPS;
					StringBuilder sb = new StringBuilder("SkewEventListenerC.handleMessage:");
					sb.append(":ActionType:").append(event.getActionType());
					sb.append(":CumBaseAmount:").append(cumBaseAmount);
					sb.append(":BackToBack:").append(backToBack);
					sb.append(":SpotSpreadMultiplier:").append(spotSpreamMultiplier);
					sb.append(":SkewSide:").append(skewSide);
					if (resetSpread) {
						bidSkew = 0.0;
						offerSkew = 0.0;
						srp.initSkew(enableSkew, bidSkew, skewType, offerSkew, skewType);
						srp.setBackToBack(backToBack);
						srp.updateTierBasedSpread(spotSpreamMultiplier, resetSpread);
						SpreadInitializationManager.instance().initializeSpreadRule(srp, false);
					}
					else if ((event.getActionType() == ActionType.MAXRISK) ) {
						bidSkew = 0.0;
						offerSkew = 0.0;
						srp.initSkew(enableSkew, bidSkew, skewType, offerSkew, skewType);
						if (backToBack) {
							srp.setBackToBack(backToBack);
						} else {
							// take spot spread profile 
							String sspStr = srp.getSpotSpreadProfileId();
							sb.append(":FI:").append(srp.getFI().getShortName());
							sb.append(":LP:").append(srp.getLP().getShortName());
							sb.append(":CurrencyPair:").append(ccyPair);
							sb.append(":SpotSpreadProfile:").append(sspStr);
							srp.updateTierBasedSpread(spotSpreamMultiplier, false);
							SpreadInitializationManager.instance().initializeSpreadRule(srp, false);
						}
					} else {
						if (skewProfileStr != null) {
							sb.append(":FI:").append(srp.getFI().getShortName());
							sb.append(":LP:").append(srp.getLP().getShortName());
							sb.append(":CurrencyPair:").append(ccyPair);
							sb.append(":SkewSpreadProfile:").append(skewProfileStr);
							SpotSpreadProfile ssp =  ConfigUtil.getSpotSpreadProfile(srp.getLP(), skewProfileStr);
							if (ssp != null) {
								bidSkew = 0.0;
								offerSkew = 0.0;
								skewType = ssp.getSpreadType();
								sb.append(":Found Spot Spread Profile:SpreadType:").append(skewType);
								Tuple<Double, Double> skewSpreadTuple = PriceProvisionUtilC.getSkewSpread(srp, ssp, cumBaseAmount);
								sb.append(":SkewSpreadTuple:").append(skewSpreadTuple.first).append(":").append(skewSpreadTuple.second);
								if (skewSpreadTuple != null) {
									if (skewSpreadTuple.first != null && skewSpreadTuple.first > 0.0) {
										bidSkew = skewSpreadTuple.first;
										if (DealingPrice.OFFER == skewSide) {
											bidSkew = -1 * bidSkew;
										}
										sb.append(":BidSkew:").append(bidSkew);
									}
									if (skewSpreadTuple.second != null && skewSpreadTuple.second > 0.0) {
										offerSkew = skewSpreadTuple.second;
										if (DealingPrice.BID == skewSide) {
											offerSkew = -1 * offerSkew;
										}
										sb.append(":OfferSkew:").append(offerSkew);
									}
								}
							}
						}
						// if it' start event then also reset the spot spread to original value
						srp.updateTierBasedSpread(spotSpreamMultiplier, true);
						SpreadInitializationManager.instance().initializeSpreadRule(srp, false);
						srp.initSkew(enableSkew, bidSkew, skewType, offerSkew, skewType);
					}
					log.info(sb.toString());
					//srp.initSkew(enableSkew, bidSkew, skewType, offerSkew, skewType);
					republishRates(fi, lp, ccyPair);
				}
			}
		} catch(Exception e) {
			log.error("SEL.onMessage - Error. m=" + strMsg, e);
		}
	}
	
	
	protected void republishRates(String fi, String lp, String ccyPair)
	{
		try
		{
			Provider prov = ProviderManagerC.getInstance().getProvider(lp);
			if ( prov == null )
			{
				return;
			}
			Adaptor adaptor = prov.getAdaptor();
			if ( adaptor == null )
			{
				return;
			}
			
			// If masked lp, then republish for real lp
			Organization lpOrg = ISUtilImpl.getInstance().getOrg(prov.getName());
			if( lpOrg.isMasked() )
			{
				lp = lpOrg.getRealLP().getShortName();
			}
			
			QuoteDistributionManager qdm = adaptor.getQuoteDistributionManager();
			Collection<ConcurrentHashMap<String, QuoteDistributionInfo>> quoteDisInfoMaps = qdm.quoteSubscriptionsMap.values();
			Set<String> selectorSet = new HashSet<String>();
			for ( ConcurrentHashMap<String, QuoteDistributionInfo> quoteDisInfoMap : quoteDisInfoMaps )
			{
				Collection<QuoteDistributionInfo> infos = quoteDisInfoMap.values();
				for ( QuoteDistributionInfo qdi : infos )
				{
					if ( qdi.adaptorOrg.getShortName().equals(lp) && qdi.customerOrgName.equals(fi) && qdi.ccyPair.equals(ccyPair) )
					{
						selectorSet.add(qdi.selector);
					}
				}
			}
			Map<String, Set<String>> selectorSetMap = new HashMap<String, Set<String>>();
			selectorSetMap.put(adaptor.getName(), selectorSet);
			SubscriptionUtil.republishRates(selectorSetMap, "SkewEvent", false);
		}
		catch ( Exception e )
		{
			log.error("JMSSEL.republishRate . Exception. fi="+fi+",lp"+lp+",cp="+ccyPair,e);
		}

	}
	
	protected void resetSkew()
	{
		skewEventSeqCache.clear();
		Map<String,SpreadRuleParameter>  srps = ((PriceProvisionRulesCacheManagerC) ServiceFactory.getPriceProvisionRulesCacheManager()).getSpreadRuleParameterMap();
		if( srps != null )
		{
			for ( SpreadRuleParameter srp : srps.values() )
			{
				boolean wasEnabled = srp.resetSkew();
				if( wasEnabled )
				{
					republishRates(srp.getFI().getShortName(), srp.getLP().getShortName(), srp.getCurrencyPairName());
				}
			}
		}
	}

}
