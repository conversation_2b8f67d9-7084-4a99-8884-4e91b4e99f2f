package com.integral.monitor.dao;

import com.integral.db.embedded.HibernateSessionFactory;
import com.integral.monitor.model.fx.Request;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.monitor.model.user.User;
import org.hibernate.Session;
import org.hibernate.Criteria;
import org.hibernate.criterion.Projections;
import org.hibernate.criterion.Order;
import org.hibernate.criterion.Restrictions;

import java.util.List;
import java.util.Date;
import java.util.Calendar;

/**
 * <AUTHOR> Development Corporation.
 */
public class UserOrgDAOC implements UserOrgDAO
{

	protected Log log = LogFactory.getLog(this.getClass());
	private HibernateSessionFactory sessionFactory;

	protected void startService()
	{
		log.info("STARTED UserOrgDAOC ");
	}

	public List geUserOrganizationList( String org ) throws IllegalStateException
	{

		List results = null;
		Session session = sessionFactory.getSession();
		Criteria criteria;
		try
		{

			// Get one month old date from the calendar instance.
			Calendar calendar = Calendar.getInstance();
			calendar.add(Calendar.MONTH, -1);
			Date oneMonthOld = calendar.getTime();
			log.info("Obtain unique Variable currencies for trades done in last month.");

			criteria = session.createCriteria(Request.class);
			criteria.add(Restrictions.ge("tradeDate", oneMonthOld));
			if ( org != null )
			{
				criteria.add(Restrictions.or(Restrictions.eq("cptyAOrg", org), Restrictions.eq("brokerForCptyA", org)));
			}

			criteria.setProjection(Projections.projectionList().add(Projections.groupProperty("organization"))).addOrder(Order.asc("organization"));

			results = criteria.list();

			if ( log.isDebugEnabled() )
			{
				log.debug("UserOrgDAOC.geUserOrganizationList: #results returned: " + results.size());
			}
		}
		catch ( Exception e )
		{
			log.error("UserOrgDAOC.geUserOrganizationList: Error obtaining user org list " + e);
		}
		finally
		{
			sessionFactory.closeSession(session);
		}

		return results;
	}

	public List getAllCustomers()
	{
		List results = null;
		Session session = sessionFactory.getSession();
		Criteria criteria;
		try
		{
			criteria = session.createCriteria(User.class);

			criteria.setProjection(Projections.projectionList().add(Projections.groupProperty("shortName"))).addOrder(Order.asc("shortName"));

			results = criteria.list();

			if ( log.isDebugEnabled() )
			{
				log.debug("UserOrgDAOC.getAllCustomers: #results returned: " + results.size());
			}
		}
		catch ( Exception e )
		{
			log.error("UserOrgDAOC.getAllCustomers: Error obtaining user org list " + e);
		}
		finally
		{
			sessionFactory.closeSession(session);
		}

		return results;
	}

	public HibernateSessionFactory getSessionFactory()
	{
		return sessionFactory;
	}

	public void setSessionFactory( HibernateSessionFactory sessionFactory )
	{
		this.sessionFactory = sessionFactory;
	}

	public void stopService() throws Exception
	{
	}
}