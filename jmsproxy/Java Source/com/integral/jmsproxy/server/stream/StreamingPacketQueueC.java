package com.integral.jmsproxy.server.stream;

import com.integral.jmsproxy.protocol.Packet;
import com.integral.jmsproxy.protocol.StreamElementPacket;
import com.integral.jmsproxy.protocol.http11.HTTPChunkedHeader;
import com.integral.jmsproxy.protocol.http11.HTTPChunkedTrailer;
import com.integral.jmsproxy.protocol.http11.HTTPPackets;
import com.integral.jmsproxy.protocol.http11.HTTPResponsePacket;
import com.integral.jmsproxy.protocol.http11.HeaderFields;
import com.integral.jmsproxy.protocol.http11.JavaScriptChunkC;
import com.integral.jmsproxy.server.PacketChannel;
import com.integral.log.Log;
import com.integral.log.LogFactory;

import java.io.IOException;
import java.nio.ByteBuffer;
import java.util.List;

// Copyright (c) 2001-2004 Integral Development Corp.  All rights reserved.

/**
 * A source of streaming data for the underlying channel.
 *
 * <AUTHOR> Development Corp.
 */
public class StreamingPacketQueueC implements StreamingPacketQueue, StreamObserver
{
    protected Log log = LogFactory.getLog( this.getClass() );

    public static final int START_STREAMING = 1;
    public static final int STREAMING = 2;
    public static final int STOP_STREAMING = 3;
    public static final int STOPPED = 4;

    protected PacketChannel channel = null;

    protected int state = STOPPED;

    protected Stream stream = null;

    protected long livelinessNanos = -1;

    protected int count = 10;

    //Default ctor.
    public StreamingPacketQueueC( PacketChannel channel, int count )
    {
        this.channel = channel;
        this.count = count;
    }

    /**
     * Add a packet to the queue.
     *
     * @param packet
     */
    public synchronized void add( HTTPResponsePacket packet )
    {
        throw new UnsupportedOperationException( "Method not supported for Streaming Queues." );
    }

    /**
     * Add a list of packets to the queue.
     *
     * @param packets
     */
    public void add( List<HTTPResponsePacket> packets )
    {
        throw new UnsupportedOperationException( "Method not supported for Streaming Queues." );
    }

    /**
     * Clear the queue - this will clear any messages that are still waiting to the sent out.
     * If there were any EndOfStreamPackets  queued, they will also be cleared - therefore, it is
     * recommended that this method be used with caution.<br>
     * The most likely usage of this method is when an underlying session is being destroyed.
     */
    public synchronized void clear()
    {
        throw new UnsupportedOperationException( "Method not supported for Streaming Queues." );
//        log.warn("StreamingPacketQueueC.clear: Clearing streaming items - " + stream.size());
//        stream.get(stream.size());
    }

    /**
     * Remove a packet from the top of the queue. This method will not remove the packet from the queue.
     *
     * @return
     */
    public synchronized HTTPResponsePacket getFirst()
    {
        HTTPResponsePacket p = null;
        switch ( state )
        {
            case START_STREAMING:
                p = HTTPPackets.chunkedHeader;
                state = STREAMING;
                break;
            case STOP_STREAMING:
                p = HTTPPackets.chunkedTrailer;
                state = STOPPED;
                break;
            case STREAMING:
                StreamElement se = stream.get();
                break;
            case STOPPED:
                //return null;
                break;
        }

        return p;
    }

    /**
     * The length of the queue.
     *
     * @return
     */
    public synchronized int length()
    {
        int size = stream.size();

        switch ( state )
        {
            case START_STREAMING:
            case STOP_STREAMING:
                size++;
                break;
        }

        return size;
    }

    public boolean hasNext()
    {
        switch ( state )
        {
            case STOPPED:
                return false;
        }

        return stream.hasNext();
    }

    /**
     * Get the stream that this queue is associated with.
     *
     * @return
     */
    public Stream getStream()
    {
        return stream;
    }

    /**
     * Set the stream that this queue is associated with.
     *
     * @param stream
     */
    public void setStream( Stream stream )
    {
        this.stream = stream;
        stream.setPacketQueue( this );
    }

    public synchronized void startStreaming( HTTPChunkedHeader sosp )
    {
        state = START_STREAMING;
        reactivateWriting();
    }

    public synchronized void stopStreaming( HTTPChunkedTrailer eosp )
    {
        state = STOP_STREAMING;
        reactivateWriting();
    }

    protected void reactivateWriting()
    {
        try
        {
//            count++;
//            if (count >= 100) {
            channel.reactivateWriting();
//                count = 0;
//            }
        }
        catch ( IOException e )
        {
            log.error( "Exception: " + e.getMessage(), e );
            channel.close();
        }
    }

    public void streamUpdated( int size )
    {
        if ( size == 1 )
        {
            reactivateWriting();
//            ByteBuffer buff = ByteBuffer.allocate(this.channel.getSendBufferSize());
//            get(buff);
//            if(!channel.preEmptiveWrite(buff))
//                reactivateWriting();
        }
    }

    ByteBuffer cachedBuffer = ByteBuffer.allocate( 0 );

    public void get( ByteBuffer buffer )
    {

        if ( !cachedBuffer.hasRemaining() )
        {
            switch ( state )
            {
                case STREAMING:
                    List<StreamElement> list = stream.get( count, 0, 0 );
                    if ( list.size() > 0 )
                    {
                        //23CRLF
                        //dataCRLFCRLF
                        //
                        //
                        StringBuilder contentBuilder = new StringBuilder( list.size() * 32 );
                        StreamElementToJSON.convert( list, contentBuilder );
                        String contentStr = contentBuilder.toString();
                        StringBuilder builder = new StringBuilder( 10 );

                        //write the header length + CRLF.
                        builder.append( Integer.toHexString( contentStr.length() ) ).append( "\r\n" );
                        //construct the byte string for the header.
                        ByteBuffer headerBytes = HeaderFields.httpCharset.encode( builder.toString() );

                        //create the byte represetnation of the content.
                        ByteBuffer bytes = Packet.charset.encode( contentStr );

                        //allocate the buffer for the chunk.
                        ByteBuffer buff = ByteBuffer.allocate( bytes.remaining() + headerBytes.remaining() + 2 ); //2  for CRLF
                        buff.put( headerBytes );
                        buff.put( bytes );
                        buff.put( HeaderFields.httpCharset.encode( "\r\n" ) );
                        buff.flip();
                        //assign the buffer to cached buffer.
                        cachedBuffer = buff;

                        //is it end of stream?
                        if ( list.get( list.size() - 1 ) instanceof EndOfStream )
                        {
                            state = STOP_STREAMING;
                        }
                        java.util.Iterator itList = list.iterator();
                        while( itList.hasNext() )
                        {
                        	Object o = itList.next();
                        	if( o instanceof RateStreamElementC )
                        		stream.returnStreamElementToPool((RateStreamElementC)o);
                        }
                        
                    }
                    break;

                case START_STREAMING:
                    cachedBuffer = ByteBuffer.wrap( HTTPPackets.chunkedHeader.asBytes() );
                    state = STREAMING;
                    break;
                case STOP_STREAMING:
                    cachedBuffer = ByteBuffer.wrap( HTTPPackets.chunkedTrailer.asBytes() );
                    state = STOPPED;
                    break;
                case STOPPED:
                    //return null;
                    break;
            }
        }

        if ( cachedBuffer.remaining() > buffer.remaining() )
        {
            while ( buffer.hasRemaining() )
            {
                buffer.put( cachedBuffer.get() );
            }
        }
        else
        {
            buffer.put( cachedBuffer );
        }

        buffer.flip();

        if ( cachedBuffer.hasRemaining() )
        {
            log.warn( "StreamingPacketQueueC.get: cachedBuffer.size() " + cachedBuffer.remaining() + " - " + buffer.remaining() );

            reactivateWriting();
            return;
        }

        switch ( state )
        {
            case STOP_STREAMING:
                reactivateWriting();
                break;
            case STREAMING:
                if ( stream.size() > 0 )
                {
                    reactivateWriting();
                }
                break;
        }
    }

    public StreamElementPacket newStreamElementPacket( StreamElement streamElement )
    {
        return new JavaScriptChunkC( streamElement );
    }

    public void setLivelinessNanos( long interval )
    {
        this.livelinessNanos = interval;
    }

    public long getReconnectInterval()
    {
        return this.livelinessNanos;
    }

    public void startStreaming( Stream stream )
    {
        //To change body of implemented methods use File | Settings | File Templates.
    }

    public void stopStreaming()
    {
        //To change body of implemented methods use File | Settings | File Templates.
    }

    public void close()
    {
        //To change body of implemented methods use File | Settings | File Templates.
    }

    public boolean hasRemaining()
    {
        return cachedBuffer.hasRemaining();
    }

}
