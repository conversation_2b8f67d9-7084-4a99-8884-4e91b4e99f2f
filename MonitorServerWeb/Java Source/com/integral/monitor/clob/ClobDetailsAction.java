package com.integral.monitor.clob;

import java.io.IOException;
import java.net.URL;
import java.util.Map;
import java.util.Set;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Map.Entry;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.struts.action.Action;
import org.apache.struts.action.ActionForm;
import org.apache.struts.action.ActionForward;
import org.apache.struts.action.ActionMapping;
import org.codehaus.jackson.map.ObjectMapper;
import org.codehaus.jackson.type.TypeReference;
import org.json.JSONWriter;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.monitor.mv.ReloadDeployedVenues;
import com.integral.monitor.mv.VSVenueInfo;
import com.integral.monitor.mv.VenueDetails;
import com.integral.monitor.rex.REXJSONAction;

/**
 * <AUTHOR>
 *
 */
public class ClobDetailsAction extends Action{
	
	private static Log log = LogFactory.getLog(ClobDetailsAction.class);
	
	/* (non-Javadoc)
	 * @see org.apache.struts.action.Action#perform(org.apache.struts.action.ActionMapping, org.apache.struts.action.ActionForm, javax.servlet.http.HttpServletRequest, javax.servlet.http.HttpServletResponse)
	 */
	public ActionForward perform(ActionMapping mapping, ActionForm form, HttpServletRequest request, HttpServletResponse response)
            throws IOException, ServletException
    {
		String clobName = request.getParameter("clobName");
		String ccyPair = request.getParameter("ccypair");
		String operation = request.getParameter("operation");
		ObjectMapper objectMapper = new ObjectMapper();
		try{
			String location = getLocation(clobName);
			if(location != null){
				if(operation.equals("getTopOrders")){
					ArrayList<ClobDetails> list = gateWayInfo(location, objectMapper, clobName, ccyPair);
					response.getWriter().write(objectMapper.writeValueAsString(list));
				}else if(operation.equals("getStatusInfo")){
					VenueDetails result = getClobStatus(location, objectMapper, clobName, ccyPair);
					sendVenueDetail(result, location, response, objectMapper);
				}else if(operation.equals("startClob")){
					VenueDetails result = startClob(location, objectMapper, clobName, ccyPair);
					sendVenueDetail(result, location, response, objectMapper);
				}else if(operation.equals("stopClob")){
					VenueDetails result = stopClob(location, objectMapper, clobName, ccyPair);
					sendVenueDetail(result, location, response, objectMapper);
				}else if(operation.equals("changeMarketDataPublishStatus")){
					String marketDataStatus = request.getParameter("status");
					VenueDetails result = changeMarketDataPublishStatus(location, objectMapper, clobName, ccyPair, Boolean.parseBoolean(marketDataStatus));
					sendVenueDetail(result, location, response, objectMapper);
				}/*else if(operation.equals("cancelAllOrders")){
					System.out.println("ClobDetailsAction.perform() Cancel All Orders");
					cancelAllOrders(location, objectMapper, clobName, ccyPair);
					if (result == null || !result) {
						VenueDetails venueDetail = new VenueDetails();
						venueDetail.setStatus(VenueDetails.FAILED);
						venueDetail.setFailureReason("Request failed becuase of invalid input");
						venueDetail.setHostname(location);
					    response.getWriter().write(objectMapper.writeValueAsString(venueDetail));
					}
				}*/
			}
        }catch ( Exception ex ){
        	log.error("unable to perform clob details req", ex);
        }
		return null;
    }
	
	private boolean cancelAllOrders(String location, ObjectMapper objectMapper, String clobName, String ccyPair) throws Exception{
        URL url =
            new URL("http://" + location + "/?service=ManagementService&method=cancelAllOrders&args"+clobName);
        TypeReference<Boolean> typeRef = new TypeReference<Boolean>() {};
        Boolean result = objectMapper.readValue(url, typeRef);
        return result;
	}
	
	private void sendVenueDetail(VenueDetails result, String location, HttpServletResponse response, ObjectMapper objectMapper) throws IOException{
		if (result == null) {
	      result = new VenueDetails();
	      result.setStatus(VenueDetails.FAILED);
	      result.setFailureReason("Request failed becuase of invalid input");
	    }
	    result.setHostname(location);
	    response.getWriter().write(objectMapper.writeValueAsString(result));
	}
	
	private VenueDetails changeMarketDataPublishStatus(String location, ObjectMapper objectMapper, String clobName, String ccyPair, boolean changeStatus) throws Exception{
         URL url =
             new URL("http://" + location + "/clob/?operation=changeMarketDataPublishStatus&venue=" + clobName
                 + "&ccy=" + ccyPair + "&status=" + changeStatus);
         TypeReference<VenueDetails> typeRef = new TypeReference<VenueDetails>() {};
         VenueDetails venueDetails = objectMapper.readValue(url, typeRef);
         return venueDetails;
	}
	
	
	private VenueDetails getClobStatus(String location, ObjectMapper objectMapper, String clobName, String ccyPair) throws Exception{
		URL url =
                new URL("http://" + location + "/clob/?operation=getStatusInfo&venue=" + clobName
                    + "&ccy=" + ccyPair);
        TypeReference<VenueDetails> typeRef = new TypeReference<VenueDetails>() {};
        VenueDetails venueDetails = objectMapper.readValue(url, typeRef);
        return venueDetails;
	}
	
	private VenueDetails stopClob(String location, ObjectMapper objectMapper, String clobName, String ccyPair) throws Exception{
		URL url =
	              new URL("http://" + location + "/clob/?operation=stopOrderBook&venue=" + clobName
	                  + "&ccy=" + ccyPair);
      TypeReference<VenueDetails> typeRef = new TypeReference<VenueDetails>() {};
      VenueDetails venueDetails =  objectMapper.readValue(url, typeRef);
      return venueDetails;
	}
	
	private VenueDetails startClob(String location, ObjectMapper objectMapper, String clobName, String ccyPair) throws Exception{
		URL url =
	              new URL("http://" + location + "/clob/?operation=startOrderBook&venue=" + clobName
	                  + "&ccy=" + ccyPair);
      TypeReference<VenueDetails> typeRef = new TypeReference<VenueDetails>() {};
      VenueDetails venueDetails = objectMapper.readValue(url, typeRef);
      return venueDetails;
	}
	
	private ArrayList<ClobDetails> gateWayInfo(String location, ObjectMapper objectMapper, String clobName, String ccyPair) throws Exception{
		URL url = new URL("http://" + location + "/clob/?operation=getTopOrders&venue="+clobName+"&ccy="+ ccyPair);
		
        TypeReference<ArrayList<ClobDetails>> typeRef = new TypeReference<ArrayList<ClobDetails>>() {};
        ArrayList<ClobDetails> gateWayDetailsList = objectMapper.readValue(url, typeRef);
        //ArrayList<GateWayDetails> sortedGateWayDetailsList = getTopFiveSortedOrders(gateWayDetailsList);
        return gateWayDetailsList;
	}
	
	/**
	 * @param clobName
	 * @return tenantLocation
	 */
	private String getLocation(String clobName) {
		String location = null;
		Set<VSVenueInfo> tenants = ReloadDeployedVenues.INSTANCE.tenantLocation.keySet();
		for(VSVenueInfo vsVenueInfo : tenants){
			if(vsVenueInfo.getVenueType().equals("Clob") && vsVenueInfo.getVenueName().equals(clobName)){
				location = ReloadDeployedVenues.INSTANCE.tenantLocation.get(vsVenueInfo);
				break;
			}
		}
		return location;
	}
	
	/*private ArrayList<GateWayDetails> getTopFiveSortedOrders(ArrayList<GateWayDetails> gateWayDetailsList){
		ArrayList<GateWayDetails> sortedGateWayDetailsList = new ArrayList<GateWayDetails>();
		ArrayList<GateWayDetails> buyOrdersList = new ArrayList<GateWayDetails>();
		ArrayList<GateWayDetails> sellOrdersList = new ArrayList<GateWayDetails>();
		for(GateWayDetails gateWayDetails : gateWayDetailsList){
			if("0".equals(gateWayDetails.getSide())){
				buyOrdersList.add(gateWayDetails);
			}else if("1".equals(gateWayDetails.getSide())){
				sellOrdersList.add(gateWayDetails);
			}
		}
		
		Collections.sort(buyOrdersList);
		Collections.sort(sellOrdersList);
		
		int count = 0;
		for(GateWayDetails buyOrderDetails : buyOrdersList){
			sortedGateWayDetailsList.add(buyOrderDetails);
			count++;
			if(count >= 5){
				break;
			}
		}
		
		count = 0;
		for(GateWayDetails sellOrderDetails : sellOrdersList){
			sortedGateWayDetailsList.add(sellOrderDetails);
			count++;
			if(count >= 5){
				break;
			}
		}
		return sortedGateWayDetailsList;
	}*/
}
