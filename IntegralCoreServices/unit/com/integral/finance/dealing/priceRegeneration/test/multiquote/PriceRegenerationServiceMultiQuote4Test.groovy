package com.integral.finance.dealing.priceRegeneration.test.multiquote

import com.integral.finance.dealing.DealingFactory
import com.integral.finance.dealing.Quote
import com.integral.finance.dealing.Request
import com.integral.finance.dealing.RequestC
import com.integral.finance.dealing.priceRegeneration.*
import com.integral.finance.dealing.priceRegeneration.configuration.PriceRegenerationConfiguration
import com.integral.finance.dealing.priceRegeneration.configuration.PriceRegenerationConfigurationFactory
import com.integral.finance.dealing.priceRegeneration.configuration.PriceRegenerationConfigurationMBean
import com.integral.finance.dealing.priceRegeneration.fx.PriceRegenerationEventCacheC
import com.integral.finance.dealing.priceRegeneration.test.PriceRegenerationServiceBaseTestCase
import com.integral.finance.dealing.priceRegeneration.test.TestPriceRegenerationHandler
import com.integral.finance.fx.FXSingleLeg
import com.integral.finance.trade.Trade
import com.integral.math.MathUtil
import com.integral.message.MessageHandler
import com.integral.system.configuration.ConfigurationProperty

/**
 * Tests the price regeneration service workflows.
 *
 * <AUTHOR> Development Corp.
 */
public class PriceRegenerationServiceMultiQuote4Test extends PriceRegenerationServiceBaseTestCase
{

    public void testMultiQuotePriceRegenerationTradingLimitChangeWithRoundingDuringReplenishment()
    {
        init();
        PriceRegenerationParameters prcRegenParam;
        setNumberOfTiers( 1 );
        try
        {
            log( "nettingReplenishParams size : " + nettingReplenishParams.size() );
            for ( int index = 0; index < nettingReplenishParams.size(); index++ )
            {
                prcRegenParam = nettingReplenishParams.get( index );
                log( "prcRegenParam : " + prcRegenParam.getDisplayKey() );
                String ccyPair = currencyPairs[index];
                org1.setPriceRegenerationParameters( prcRegenParam );

                double tierLimit = 5123434;
                int roundingFactor = PriceRegenerationConfigurationFactory.getPriceRegenerationConfigurationMBean().getTierLimitRoundingFactor();
                double roundedTier = Math.round( tierLimit / MathUtil.pow10( roundingFactor ) ) * MathUtil.pow10( roundingFactor );
                log( "RoundedTier = " + roundedTier );
                double tierLimitChange = tierLimit - roundedTier - 1000;
                double newTierLimit = tierLimit + tierLimitChange;
                Quote newQuote = prepareQuote( org1, ccyPair, tierLimit, tierLimit );
                svc.applyPriceRegenerationRules( newQuote, org1Tp1, isMultiTier() );

                TestPriceRegenerationHandler testHandler = new TestPriceRegenerationHandler();
                testHandler.setPriceRegenerationParameters( prcRegenParam );

                // now start taking trades.
                boolean isBid = index % 2 == 0;
                Trade trade = prepareTrade( tierLimit / 2, isBid, ccyPair, org1, org1Tp1 );
                boolean result1 = svc.startPriceRegeneration( trade, testHandler );
                PriceRegeneration prcRegen1 = testHandler.getPriceRegeneration();
                log( "After trade, prcRegen : " + prcRegen1 + " isBid : " + isBid );
                assertEquals( "TakenTradeLimit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result1, true );

                log( "new quote tier = " + newTierLimit );
                Quote newQuote1 = prepareQuote( org1, ccyPair, newTierLimit, newTierLimit );
                svc.applyPriceRegenerationRules( newQuote1, org1Tp1, isMultiTier() );
                PriceRegenerationEvent event = PriceRegenerationEventCacheC.getInstance().getPriceRegenerationEvent( org1.getShortName(), org1Tp1.getShortName(), ccyPair );
                log( "New quote limits within rounding limits event : " + event );
                assertEquals( "Event limits should be unchanged.", tierLimit, event.getTradingLimits().getBidTierLimit( 0 ) );

                double newTierLimit2 = tierLimit + MathUtil.pow10( roundingFactor );
                log( "new quote tier = " + newTierLimit2 );
                Quote newQuote2 = prepareQuote( org1, ccyPair, newTierLimit2, newTierLimit2 );
                svc.applyPriceRegenerationRules( newQuote2, org1Tp1, isMultiTier() );
                PriceRegenerationEvent event2 = PriceRegenerationEventCacheC.getInstance().getPriceRegenerationEvent( org1.getShortName(), org1Tp1.getShortName(), ccyPair );
                log( "New quote limits within rounding limits event : " + event2 );
                assertEquals( "Event limits should changed.", newTierLimit2, event2.getTradingLimits().getBidTierLimit( 0 ) );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "inside testMultiQuotePriceRegenerationTradingLimitChangeWithRoundingDuringReplenishment" );
        }
        setNumberOfTiers( TIER_COUNT );
    }

    public void testMultiQuotePriceRegenerationFrequentTradingLimitChangeWithReplenishment()
    {
        init();
        PriceRegenerationParameters prcRegenParam;
        try
        {
            prcRegenParam = preparePriceRegenerationParameters( "replenishLowestNoNettingAmt", replenish, lowest, noNetting, 0, frequency, 1000000 );
            log( "prcRegenParam : " + prcRegenParam.getDisplayKey() );
            String ccyPair = currencyPairs[0];
            org1.setPriceRegenerationParameters( prcRegenParam );

            double tierLimit = 5123434;
            Quote newQuote = prepareQuote( org1, ccyPair, tierLimit, tierLimit );
            svc.applyPriceRegenerationRules( newQuote, org1Tp1, isMultiTier() );

            TestPriceRegenerationHandler testHandler = new TestPriceRegenerationHandler();
            testHandler.setPriceRegenerationParameters( prcRegenParam );

            // now start taking trades.
            Trade trade = prepareTrade( tierLimit / 2, true, ccyPair, org1, org1Tp1 );
            boolean result1 = svc.startPriceRegeneration( trade, testHandler );
            PriceRegeneration prcRegen1 = testHandler.getPriceRegeneration();
            log( "After trade, prcRegen : " + prcRegen1 );
            assertEquals( "TakenTradeLimit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result1, true );
            int roudingFactor = PriceRegenerationConfigurationFactory.getPriceRegenerationConfigurationMBean().getTierLimitRoundingFactor();
            double newTierLimit = tierLimit + MathUtil.pow10( roudingFactor );

            for ( int i = 0; i < 10; i++ )
            {
                sleepFor( 300 );
                log( "new quote tier = " + newTierLimit );
                Quote newQuote1 = prepareQuote( org1, ccyPair, newTierLimit, newTierLimit );
                svc.applyPriceRegenerationRules( newQuote1, org1Tp1, isMultiTier() );
                PriceRegenerationEvent event = PriceRegenerationEventCacheC.getInstance().getPriceRegenerationEvent( org1.getShortName(), org1Tp1.getShortName(), ccyPair );
                log( "New quote limits within rounding limits event : " + event );
                assertEquals( "Event limits should changed.", newTierLimit, event.getTradingLimits().getBidTierLimit( 0 ) );
                newTierLimit += MathUtil.pow10( roudingFactor );
            }

            // check to see if price regeneration was carried out in the mean time by checking the handler invocations.
            assertEquals( "Handler invocations." + testHandler.getHandlerInvocations(), testHandler.getHandlerInvocations() > 1, true );

        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "inside testMultiQuotePriceRegenerationFrequentTradingLimitChangeWithReplenishment" );
        }
    }

    public void testMultiQuotePriceRegenerationUnsupportedTradingLimitChangeWithReplenishment()
    {
        init();
        PriceRegenerationParameters prcRegenParam;
        try
        {
            prcRegenParam = preparePriceRegenerationParameters( "replenishLowestNoNettingAmt", replenish, lowest, noNetting, 0, frequency, 1000000 );
            log( "prcRegenParam : " + prcRegenParam.getDisplayKey() );
            String ccyPair = currencyPairs[0];
            org1.setPriceRegenerationParameters( prcRegenParam );

            double tierLimit = 5123434;
            Quote newQuote = prepareQuote( org1, ccyPair, tierLimit, tierLimit );
            svc.applyPriceRegenerationRules( newQuote, org1Tp1, isMultiTier() );

            TestPriceRegenerationHandler testHandler = new TestPriceRegenerationHandler();
            testHandler.setPriceRegenerationParameters( prcRegenParam );

            // now start taking trades.
            Trade trade = prepareTrade( tierLimit / 2, true, ccyPair, org1, org1Tp1 );
            boolean result1 = svc.startPriceRegeneration( trade, testHandler );
            PriceRegeneration prcRegen1 = testHandler.getPriceRegeneration();
            log( "After trade, prcRegen : " + prcRegen1 );
            log( "handler.invocations after trade =" + testHandler.getHandlerInvocations() );
            assertEquals( "TakenTradeLimit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result1, true );

            // now send an unsupported rate.
            Quote newQuote1 = prepareQuote( org1, ccyPair, 0.0, 0.0 );
            svc.applyPriceRegenerationRules( newQuote1, org1Tp1, isMultiTier() );

            sleepFor( 300 );
            PriceRegenerationEvent event = PriceRegenerationEventCacheC.getInstance().getPriceRegenerationEvent( org1.getShortName(), org1Tp1.getShortName(), ccyPair );
            log( "New quote limits with unsupported quote : " + event );
            assertEquals( "Event limits should changed.", 0.0, event.getTradingLimits().getBidTierLimit( 0 ) );
            log( "handler.invocations after unsupported rates=" + testHandler.getHandlerInvocations() );

            // check to see if price regeneration was carried out in the mean time by checking the handler invocations.
            assertEquals( "Handler invocations." + testHandler.getHandlerInvocations(), testHandler.getHandlerInvocations() == 1, true );

            // now send another valid quote
            Quote newQuote2 = prepareQuote( org1, ccyPair, tierLimit, tierLimit );
            svc.applyPriceRegenerationRules( newQuote2, org1Tp1, isMultiTier() );
            event = PriceRegenerationEventCacheC.getInstance().getPriceRegenerationEvent( org1.getShortName(), org1Tp1.getShortName(), ccyPair );
            log( "New quote limits with supported quote : " + event );
            assertEquals( "Event limits should changed.", tierLimit, event.getTradingLimits().getBidTierLimit( 0 ) );
            log( "handler.invocations after supported rates=" + testHandler.getHandlerInvocations() );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "inside testMultiQuotePriceRegenerationUnsupportedTradingLimitChangeWithReplenishment" );
        }
    }


    public void testMultiQuoteAcceptanceTolerance()
    {
        init();
        PriceRegenerationParameters prcRegenParam;
        try
        {
            log( "testMultiQuoteAcceptanceTolerance - regenParams size : " + regenParams.size() );
            for ( int index = 0; index < regenParams.size(); index++ )
            {
                prcRegenParam = ( PriceRegenerationParameters ) regenParams.toArray()[index];
                if ( PriceRegenerationParameters.NO_REGENERATION == prcRegenParam.getType() )
                {
                    continue;
                }

                log( "testMultiQuoteAcceptanceTolerance - prcRegenParam : " + prcRegenParam.getDisplayKey() );
                String ccyPair = currencyPairs[index];
                org1.setPriceRegenerationParameters( prcRegenParam );

                Quote newQuote = prepareQuote( org1, ccyPair, getBidTierLimit(), getOfferTierLimit() );
                svc.applyPriceRegenerationRules( newQuote, org1Tp1, isMultiTier() );

                TestPriceRegenerationHandler testHandler = new TestPriceRegenerationHandler();
                testHandler.setPriceRegenerationParameters( prcRegenParam );

                // now start taking trades. First with an amount which is greater than the acceptance tolerance amount.
                Trade trade = prepareTrade( getTradingLimit() + acceptanceToleranceAmount + 10, true, ccyPair, org1, org1Tp1 );
                boolean result1 = svc.startPriceRegeneration( trade, testHandler );
                PriceRegeneration prcRegen1 = testHandler.getPriceRegeneration();
                log( "After bid trade, prcRegen : " + prcRegen1 );
                assertEquals( "TakenTradeLimit should fail. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result1, false );

                boolean result2 = svc.startPriceRegeneration( prepareTrade( getTradingLimit() + acceptanceToleranceAmount - 10, false, ccyPair, org1, org1Tp1 ), testHandler );
                PriceRegeneration prcRegen2 = testHandler.getPriceRegeneration();
                log( "After offer trade, prcRegen : " + prcRegen2 );
                assertEquals( "TakenTradeLimit should succeed. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result2, true );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "inside testMultiQuoteAcceptanceTolerance" );
        }
    }

    public void testMultiQuotePriceRegenerationWithRapidFireTakingTradingLimitOnSameOrder()
    {
        init();
        PriceRegenerationParameters prcRegenParam;
        try
        {
			Set<PriceRegenerationParameters> regenParams = new HashSet<PriceRegenerationParameters>();
			regenParams.addAll(replenishRegenParams);
			regenParams.addAll(noReplenishRegenParams);
			
            for ( int index = 0; index < regenParams.size(); index++ )
            {
                int rapidFirePeriod = 1000;
                prcRegenParam = ( PriceRegenerationParameters ) regenParams.toArray()[index];
                prcRegenParam.setRapidFireEnabled( true );
                prcRegenParam.setRapidFirePeriod( rapidFirePeriod );
                String ccyPair = currencyPairs[index];
                org1.setPriceRegenerationParameters( prcRegenParam );

                Quote quote0 = prepareQuote( org1, ccyPair, 5000, 5000 );
                quote0.setRateId( "G100" );
                svc.applyPriceRegenerationRules( quote0, org1Tp1, isMultiTier() );

                TestPriceRegenerationHandler testHandler = new TestPriceRegenerationHandler();
                testHandler.setPriceRegenerationParameters( prcRegenParam );

                // now start taking trades.
                Trade trade = prepareTrade( 4000, true, ccyPair, org1, org1Tp1 );
                Request request = new RequestC();
                request.setAcceptedQuote( quote0 );
                request.getToOrganizations().add( org1 );
                request.setOrderId( "1000" );
                request.setTrade( trade );
                trade.setRequest( request );
                Request order = new RequestC();
                order.addChildRequest( request );
                request.setParentOCORequest( order );
                order.setOrderId( "1000" );
                boolean result1 = svc.startPriceRegeneration( trade, testHandler );
                PriceRegeneration prcRegen1 = testHandler.getPriceRegeneration();
                log( "Taken Trading limit. prcRegen=" + prcRegen1 + " prcRegenParam = " + prcRegenParam.getDisplayKey() );
                assertEquals( "TakenTradeLimit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result1, true );
                assertEquals( "PriceRegen should show in rapid fire period. " + prcRegen1.isRapidFirePeriod(), prcRegen1.isRapidFirePeriod(), true );
                assertEquals( "PriceRegen service show in rapid fire period. " + svc.isInRapidFirePeriod( org1, org1Tp1, ccyPair ), svc.isInRapidFirePeriod( org1, org1Tp1, ccyPair ), true );
                assertEquals( "Handler invocations should not be more than 1. " + testHandler.getHandlerInvocations(), testHandler.getHandlerInvocations() < 2, true );

                // now create another trade with the same order id.
                Trade trade1 = prepareTrade( 4000, true, ccyPair, org1, org1Tp1 );
                Request request1 = new RequestC();
                request1.setAcceptedQuote( quote0 );
                request1.getToOrganizations().add( org1 );
                request1.setOrderId( "1000" );
                request1.setTrade( trade1 );
                trade1.setRequest( request1 );
                order.addChildRequest( request1 );
                request1.setParentRequest( order );
                boolean result2 = svc.startPriceRegeneration( trade1, testHandler );
                PriceRegeneration prcRegenx = testHandler.getPriceRegeneration();
                log( "Taken trading limit. prcRegen=" + prcRegenx );
                assertEquals( "Should allow the trade. result2=" + result2, result2, true );
                assertEquals( "TakenTradeLimit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result2, true );
                assertEquals( "PriceRegen should show in rapid fire period. " + prcRegenx.isRapidFirePeriod(), prcRegenx.isRapidFirePeriod(), true );
                assertEquals( "PriceRegen service show in rapid fire period. " + svc.isInRapidFirePeriod( org1, org1Tp1, ccyPair ), svc.isInRapidFirePeriod( org1, org1Tp1, ccyPair ), true );
                assertEquals( "Handler invocations should not be more than 2. " + testHandler.getHandlerInvocations(), testHandler.getHandlerInvocations() < 3, true );

                // now create another trade which does not originate from the same order. this should get rejected.
                Trade differentTrade = prepareTrade( 4000, true, ccyPair, org1, org1Tp1 );
                Request differentRequest = new RequestC();
                differentRequest.setAcceptedQuote( quote0 );
                differentRequest.getToOrganizations().add( org1 );
                differentRequest.setOrderId( "1001" );
                differentRequest.setTrade( differentTrade );
                differentTrade.setRequest( differentRequest );
                Request differentOrder = new RequestC();
                differentOrder.addChildRequest( differentRequest );
                differentRequest.setParentOCORequest( differentOrder );
                differentOrder.setOrderId( "1001" );
                boolean result3 = svc.startPriceRegeneration( differentTrade, testHandler );
                PriceRegeneration prcRegeny = testHandler.getPriceRegeneration();
                log( "Taken trading limit. prcRegen=" + prcRegeny );
                assertEquals( "Should not allow the trade. result3=" + result3, result3, false );
                assertEquals( "TakenTradeLimit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result3, false );
                assertEquals( "PriceRegen should show in rapid fire period. " + prcRegeny.isRapidFirePeriod(), prcRegeny.isRapidFirePeriod(), true );
                assertEquals( "PriceRegen service show in rapid fire period. " + svc.isInRapidFirePeriod( org1, org1Tp1, ccyPair ), svc.isInRapidFirePeriod( org1, org1Tp1, ccyPair ), true );
                assertEquals( "Handler invocations should not be more than 2. " + testHandler.getHandlerInvocations(), testHandler.getHandlerInvocations() < 3, true );

                sleepFor( 200 + ( rapidFirePeriod / 2 ) );
                assertEquals( "Handler invocations should not be more than 1. " + testHandler.getHandlerInvocations(), testHandler.getHandlerInvocations() < 3, true );
                sleepFor( 200 + ( rapidFirePeriod / 2 ) );
                PriceRegeneration prcRegen2 = testHandler.getPriceRegeneration();
                assertEquals( "PriceRegen should not show in rapid fire period. " + prcRegen2.isRapidFirePeriod(), prcRegen2.isRapidFirePeriod(), false );
                assertEquals( "PriceRegen service not show in rapid fire period. " + svc.isInRapidFirePeriod( org1, org1Tp1, ccyPair ), svc.isInRapidFirePeriod( org1, org1Tp1, ccyPair ), false );
                assertEquals( "Handler invocations should be 3. " + testHandler.getHandlerInvocations(), testHandler.getHandlerInvocations() == 3, true );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "inside testMultiQuotePriceRegenerationWithRapidFireTakingTradingLimitOnSameOrder" );
        }
    }

    public void testMultiQuotePriceRegenerationUndoTakingTradeLimitsWithTradeParametersChange()
    {
        init();
        PriceRegenerationParameters prcRegenParam;
        try
        {
            for ( int index = 0; index < regenParams.size(); index++ )
            {
                prcRegenParam = ( PriceRegenerationParameters ) regenParams.toArray()[index];

                if ( PriceRegenerationParameters.NO_REGENERATION == prcRegenParam.getType() )
                {
                    continue;
                }

                String ccyPair = currencyPairs[index];
                org1.setPriceRegenerationParameters( prcRegenParam );

                Quote newQuote = prepareQuote( org1, ccyPair, getBidTierLimit(), getOfferTierLimit() );
                svc.applyPriceRegenerationRules( newQuote, org1Tp1, isMultiTier() );

                TestPriceRegenerationHandler testHandler = new TestPriceRegenerationHandler();
                testHandler.setPriceRegenerationParameters( prcRegenParam );

                // now start taking trades.
                Trade trade = prepareTrade( getTradingLimit(), true, ccyPair, org1, org1Tp1 );
                boolean result1 = svc.startPriceRegeneration( trade, testHandler );
                assertEquals( "TakenTradeLimit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result1, true );

                // now change the trade parameters.
                trade.setCounterpartyB( org2Tp1 );
                trade.setCounterpartyA( org2Tp1 );
                trade.setOrganization( org2 );
                org2.setPriceRegenerationParameters( prcRegenParam );

                svc.stopPriceRegeneration( trade, testHandler );
                assertEquals( "IsInPriceRegen. PrcRegenParam : " + prcRegenParam.getDisplayKey(), svc.isInPriceRegeneration( org1, org1Tp1, ccyPair ), false );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "inside testMultiQuotePriceRegenerationUndoTakingTradeLimits" );
        }
    }

    public void testMultiQuotePriceRegenerationWithRapidFireTakingTradingLimitWithSuspendedTimer()
    {
        init();
        PriceRegenerationParameters prcRegenParam;
        try
        {
			Set<PriceRegenerationParameters> regenParams = new HashSet<PriceRegenerationParameters>();
			regenParams.addAll(replenishRegenParams);
			regenParams.addAll(noReplenishRegenParams);
			
            for ( int index = 0; index < regenParams.size(); index++ )
            {
                int rapidFirePeriod = 1000;
                prcRegenParam = ( PriceRegenerationParameters ) regenParams.toArray()[index];
                prcRegenParam.setRapidFireEnabled( true );
                prcRegenParam.setRapidFirePeriod( rapidFirePeriod );
                String ccyPair = currencyPairs[index];
                org1.setPriceRegenerationParameters( prcRegenParam );

                Quote quote0 = prepareQuote( org1, ccyPair, 5000, 5000 );
                svc.applyPriceRegenerationRules( quote0, org1Tp1, isMultiTier() );

                TestPriceRegenerationHandler testHandler = new TestPriceRegenerationHandler();
                testHandler.setPriceRegenerationParameters( prcRegenParam );

                // now start taking trades.
                Trade trade = prepareTrade( 4000, true, ccyPair, org1, org1Tp1 );
                boolean result1 = svc.startPriceRegeneration( trade, testHandler );
                PriceRegeneration prcRegen1 = testHandler.getPriceRegeneration();
                log( "Taken Trading limit. prcRegen=" + prcRegen1 + " prcRegenParam = " + prcRegenParam.getDisplayKey() );
                assertEquals( "TakenTradeLimit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result1, true );
                assertEquals( "PriceRegen should show in rapid fire period. " + prcRegen1.isRapidFirePeriod(), prcRegen1.isRapidFirePeriod(), true );
                assertEquals( "PriceRegen service show in rapid fire period. " + svc.isInRapidFirePeriod( org1, org1Tp1, ccyPair ), svc.isInRapidFirePeriod( org1, org1Tp1, ccyPair ), true );
                assertEquals( "Handler invocations should not be more than 1. " + testHandler.getHandlerInvocations(), testHandler.getHandlerInvocations() < 2, true );

                sleepFor( 200 + ( rapidFirePeriod / 2 ) );
                assertEquals( "Handler invocations should not be more than 1. " + testHandler.getHandlerInvocations(), testHandler.getHandlerInvocations() < 2, true );

                // now suspend the timer for rapid fire period.
                PriceRegenerationEvent prcRegenEvent = PriceRegenerationEventCacheC.getInstance().getPriceRegenerationEvent( org1.getShortName(), org1Tp1.getShortName(), ccyPair );
                if ( prcRegenEvent.getBidPriceRegenerationTimerTask() != null )
                {
                    prcRegenEvent.getBidPriceRegenerationTimerTask().cancel();
                }
                if ( prcRegenEvent.getOfferPriceRegenerationTimerTask() != null )
                {
                    prcRegenEvent.getOfferPriceRegenerationTimerTask().cancel();
                }
                if ( prcRegenEvent.getRapidFireTimerTask() != null )
                {
                    prcRegenEvent.getRapidFireTimerTask().cancel();
                }

                // now sleep for more time than rapid fire period
                sleepFor( 200 + ( rapidFirePeriod ) );
                assertEquals( "PriceRegen should be still in rapid fire period. " + prcRegenEvent.isRapidFirePeriod(), prcRegenEvent.isRapidFirePeriod(), true );

                Trade trade1 = prepareTrade( 4000, true, ccyPair, org1, org1Tp1 );
                boolean result2 = svc.startPriceRegeneration( trade1, testHandler );

                PriceRegeneration prcRegen2 = testHandler.getPriceRegeneration();
                assertEquals( "PriceRegen should be in rapid fire period. " + prcRegen2.isRapidFirePeriod(), prcRegen2.isRapidFirePeriod(), true );
                assertEquals( "PriceRegen service should be in rapid fire period. " + svc.isInRapidFirePeriod( org1, org1Tp1, ccyPair ), svc.isInRapidFirePeriod( org1, org1Tp1, ccyPair ), true );
                assertEquals( "Trade should not be rejected.", result2, true );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "inside testMultiQuotePriceRegenerationWithRapidFireTakingTradingLimitWithSuspendedTimer" );
        }
    }

    public void testMultiQuotePriceRegenerationRapidFireWithMonitoringThread()
    {
        init();
        PriceRegenerationParameters prcRegenParam;
        try
        {
			Set<PriceRegenerationParameters> regenParams = new HashSet<PriceRegenerationParameters>();
			regenParams.addAll(replenishRegenParams);
			regenParams.addAll(noReplenishRegenParams);
			
            for ( int index = 0; index < regenParams.size(); index++ )
            {
                int rapidFirePeriod = 1000;
                prcRegenParam = ( PriceRegenerationParameters ) regenParams.toArray()[index];
                prcRegenParam.setRapidFireEnabled( true );
                prcRegenParam.setRapidFirePeriod( rapidFirePeriod );
                String ccyPair = currencyPairs[index];
                org1.setPriceRegenerationParameters( prcRegenParam );

                Quote quote0 = prepareQuote( org1, ccyPair, 5000, 5000 );
                svc.applyPriceRegenerationRules( quote0, org1Tp1, isMultiTier() );

                TestPriceRegenerationHandler testHandler = new TestPriceRegenerationHandler();
                testHandler.setPriceRegenerationParameters( prcRegenParam );

                // now start taking trades.
                Trade trade = prepareTrade( 4000, true, ccyPair, org1, org1Tp1 );
                boolean result1 = svc.startPriceRegeneration( trade, testHandler );
                PriceRegeneration prcRegen1 = testHandler.getPriceRegeneration();
                log( "Taken Trading limit. prcRegen=" + prcRegen1 + " prcRegenParam = " + prcRegenParam.getDisplayKey() );
                assertEquals( "TakenTradeLimit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result1, true );
                assertEquals( "PriceRegen should show in rapid fire period. " + prcRegen1.isRapidFirePeriod(), prcRegen1.isRapidFirePeriod(), true );
                assertEquals( "PriceRegen service show in rapid fire period. " + svc.isInRapidFirePeriod( org1, org1Tp1, ccyPair ), svc.isInRapidFirePeriod( org1, org1Tp1, ccyPair ), true );
                assertEquals( "Handler invocations should not be more than 1. " + testHandler.getHandlerInvocations(), testHandler.getHandlerInvocations() < 2, true );

                sleepFor( 200 + ( rapidFirePeriod / 2 ) );
                assertEquals( "Handler invocations should not be more than 1. " + testHandler.getHandlerInvocations(), testHandler.getHandlerInvocations() < 2, true );

                // now suspend the timer for rapid fire period.
                PriceRegenerationEvent prcRegenEvent = PriceRegenerationEventCacheC.getInstance().getPriceRegenerationEvent( org1.getShortName(), org1Tp1.getShortName(), ccyPair );
                if ( prcRegenEvent.getBidPriceRegenerationTimerTask() != null )
                {
                    prcRegenEvent.getBidPriceRegenerationTimerTask().cancel();
                }
                if ( prcRegenEvent.getOfferPriceRegenerationTimerTask() != null )
                {
                    prcRegenEvent.getOfferPriceRegenerationTimerTask().cancel();
                }
                if ( prcRegenEvent.getRapidFireTimerTask() != null )
                {
                    prcRegenEvent.getRapidFireTimerTask().cancel();
                }

                // now sleep for more time than rapid fire period
                sleepFor( 200 + ( rapidFirePeriod ) );
                assertEquals( "PriceRegen should be still in rapid fire period. " + prcRegenEvent.isRapidFirePeriod(), prcRegenEvent.isRapidFirePeriod(), true );

                // now sleep for the period of monitoring thread.
                long sleepDelay = PriceRegenerationConfigurationFactory.getPriceRegenerationConfigurationMBean().getPriceRegenerationEventMonitoringInterval() * 2;
                sleepFor( sleepDelay + 1000 );

                PriceRegeneration prcRegen2 = testHandler.getPriceRegeneration();
                assertEquals( "PriceRegen should not be in rapid fire period. " + prcRegen2.isRapidFirePeriod(), prcRegen2.isRapidFirePeriod(), false );
                assertEquals( "PriceRegen service should be in rapid fire period. " + svc.isInRapidFirePeriod( org1, org1Tp1, ccyPair ), svc.isInRapidFirePeriod( org1, org1Tp1, ccyPair ), false );
                break;
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "inside testMultiQuotePriceRegenerationRapidFireWithMonitoringThread" );
        }
    }

    public void testMultiQuotePriceRegenerationTakingTradeLimitsForSpecificLegalEntity()
    {
        init();
        PriceRegenerationParameters prcRegenParam;
        try
        {
            for ( int index = 0; index < regenParams.size(); index++ )
            {
                prcRegenParam = ( PriceRegenerationParameters ) regenParams.toArray()[index];

                if ( PriceRegenerationParameters.NO_REGENERATION == prcRegenParam.getType() )
                {
                    continue;
                }

                String ccyPair = currencyPairs[index];
                org1.setPriceRegenerationParameters( prcRegenParam );
                Quote newQuote = prepareQuote( org1, ccyPair, getBidTierLimit(), getOfferTierLimit() );
                svc.applyPriceRegenerationRules( newQuote, org1Tp2, isMultiTier() );

                TestPriceRegenerationHandler testHandler = new TestPriceRegenerationHandler();
                testHandler.setPriceRegenerationParameters( prcRegenParam );

                // now start taking trades.
                boolean result1 = svc.startPriceRegeneration( prepareTrade( getTradingLimit(), true, ccyPair, org1, org1Tp2 ), testHandler, org1Tp2 );
                assertEquals( "TakenTradeLimit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result1, true );

                if ( !getPriceRegenerationParametersFacade( prcRegenParam ).isNoPriceRegeneration() )
                {
                    boolean result2 = svc.startPriceRegeneration( prepareTrade( getBidLimit() + acceptanceToleranceAmount + 2000, true, ccyPair, org1, org1Tp2 ), testHandler, org1Tp2 );
                    assertEquals( "FailedToTakeTradingLimit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result2, false );
                }
            }

        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "inside testMultiQuotePriceRegenerationTakingTradeLimitsForSpecificLegalEntity" );
        }
    }

    public void testMultiQuotePriceRegenerationUndoTakingTradeLimitsForSpecificLegalEntity()
    {
        init();
        PriceRegenerationParameters prcRegenParam;
        try
        {
            for ( int index = 0; index < regenParams.size(); index++ )
            {
                prcRegenParam = ( PriceRegenerationParameters ) regenParams.toArray()[index];

                if ( PriceRegenerationParameters.NO_REGENERATION == prcRegenParam.getType() )
                {
                    continue;
                }

                String ccyPair = currencyPairs[index];
                org1.setPriceRegenerationParameters( prcRegenParam );

                Quote newQuote = prepareQuote( org1, ccyPair, getBidTierLimit(), getOfferTierLimit() );
                svc.applyPriceRegenerationRules( newQuote, org1Tp2, isMultiTier() );

                TestPriceRegenerationHandler testHandler = new TestPriceRegenerationHandler();
                testHandler.setPriceRegenerationParameters( prcRegenParam );

                // now start taking trades.
                Trade trade = prepareTrade( getTradingLimit(), true, ccyPair, org1, org1Tp2 );
                boolean result1 = svc.startPriceRegeneration( trade, testHandler );
                assertEquals( "TakenTradeLimit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result1, true );

                svc.stopPriceRegeneration( trade, testHandler );
                assertEquals( "IsInPriceRegen. PrcRegenParam : " + prcRegenParam.getDisplayKey(), svc.isInPriceRegeneration( org1, org1Tp2, ccyPair ), false );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "inside testMultiQuotePriceRegenerationUndoTakingTradeLimitsForSpecificLegalEntity" );
        }
    }

    /**
     * This methods tests all combinations of price regeneration parameters without any replenishment. First, trading limit
     * for tiers registered and some part of the trading limit it taken by a trade. Then it is tested to see whether any subsequent trade
     * fails even with a small amount until next quote. Then, next quote arrives and replenishes the
     * trading limits. Then a trade is tested with the maximum trading limit and see whether it is taken successfully. Finally,
     * handlers are inspected to see whether any of them are invoked more than once.
     */
    public void testMultiQuotePriceRegenWithoutReplenishmentAndBlackoutUntilNextQuoteEnabled()
    {
        init();
        PriceRegenerationParameters prcRegenParam;
        List<MessageHandler> handlers = new ArrayList<MessageHandler>( 15 );
        try
        {
            for ( int index = 0; index < noReplenishRegenParams.size(); index++ )
            {
                prcRegenParam = ( PriceRegenerationParameters ) noReplenishRegenParams.toArray()[index];
                prcRegenParam.setAllowOnlyOneMatchPerQuote( true );
                String ccyPair = currencyPairs[index];
                org1.setPriceRegenerationParameters( prcRegenParam );

                Quote newQuote = prepareQuote( org1, ccyPair, getBidTierLimit(), getOfferTierLimit() );
                String provRateId = "Q100";
                newQuote.setRateId( provRateId );
                svc.applyPriceRegenerationRules( newQuote, org1Tp1, isMultiTier() );
                assertFalse( svc.isInBlackoutUntilNextQuote( org1, org1Tp1, ccyPair ) );

                TestPriceRegenerationHandler testHandler = new TestPriceRegenerationHandler();
                testHandler.setPriceRegenerationParameters( prcRegenParam );
                handlers.add( testHandler );

                // now start taking trade with small portion of the available.
                double amt = getTradingLimit() / 10;
                Trade trade = prepareTrade( amt, true, ccyPair, org1, org1Tp1 );
                Request req = DealingFactory.newRequest();
                req.setAcceptedQuote( newQuote );
                trade.setRequest( req );
                boolean result = svc.startPriceRegeneration( trade, testHandler );
                assertEquals( "TakenTradeLimit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result, true );
                assertTrue( testHandler.getPriceRegeneration().isBlackoutUntilNextQuote() );
                assertTrue( svc.isInBlackoutUntilNextQuote( org1, org1Tp1, ccyPair ) );

                sleepFor( 200 );

                // now create a new trade with same provider rate id. This trade should fail now.
                Trade trade1 = prepareTrade( amt, true, ccyPair, org1, org1Tp1 );
                Request req1 = DealingFactory.newRequest();
                req1.setAcceptedQuote( newQuote );
                trade1.setRequest( req1 );
                boolean result1 = svc.startPriceRegeneration( trade1, testHandler );
                assertEquals( "Failed due to blackout until next quote. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result1, false );
                assertTrue( testHandler.getPriceRegeneration().isBlackoutUntilNextQuote() );
                assertTrue( svc.isInBlackoutUntilNextQuote( org1, org1Tp1, ccyPair ) );

                // create another trade with a different provider rate id. This trade should go through
                Quote newQuote1 = prepareQuote( org1, ccyPair, getBidTierLimit(), getOfferTierLimit() );
                String provRateId1 = "Q101";
                newQuote1.setRateId( provRateId1 );
                Trade trade2 = prepareTrade( amt, true, ccyPair, org1, org1Tp1 );
                Request req2 = DealingFactory.newRequest();
                req2.setAcceptedQuote( newQuote1 );
                trade2.setRequest( req2 );

                boolean result2 = svc.startPriceRegeneration( trade2, testHandler );
                assertEquals( "trade success due to a different accepted quote id. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result2, true );
                assertTrue( testHandler.getPriceRegeneration().isBlackoutUntilNextQuote() );
                assertTrue( svc.isInBlackoutUntilNextQuote( org1, org1Tp1, ccyPair ) );

                // new send a new quote and new trade with new provider rate id should go through.
                Quote newQuote2 = prepareQuote( org1, ccyPair, getBidTierLimit(), getOfferTierLimit() );
                String provRateId2 = "Q102";
                newQuote2.setRateId( provRateId2 );
                svc.applyPriceRegenerationRules( newQuote2, org1Tp1, isMultiTier() );
                assertFalse( svc.isInBlackoutUntilNextQuote( org1, org1Tp1, ccyPair ) );

                Trade trade3 = prepareTrade( amt, true, ccyPair, org1, org1Tp1 );
                Request req3 = DealingFactory.newRequest();
                req3.setAcceptedQuote( newQuote2 );
                trade3.setRequest( req3 );

                boolean result3 = svc.startPriceRegeneration( trade3, testHandler );
                assertEquals( "trade success due to newer quote. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result3, true );
                assertTrue( testHandler.getPriceRegeneration().isBlackoutUntilNextQuote() );
                assertTrue( svc.isInBlackoutUntilNextQuote( org1, org1Tp1, ccyPair ) );

                Trade trade4 = prepareTrade( amt, true, ccyPair, org1, org1Tp1 );
                Request req4 = DealingFactory.newRequest();
                req4.setAcceptedQuote( newQuote1 );
                trade4.setRequest( req4 );

                boolean result4 = svc.startPriceRegeneration( trade4, testHandler );
                assertEquals( "trade failed due to re-attempt on already accepted quote. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result4, false );
                assertTrue( testHandler.getPriceRegeneration().isBlackoutUntilNextQuote() );
                assertTrue( svc.isInBlackoutUntilNextQuote( org1, org1Tp1, ccyPair ) );
            }

            sleepFor( 2000 );

            for ( MessageHandler handler1 : handlers )
            {
                TestPriceRegenerationHandler hndler = ( TestPriceRegenerationHandler ) handler1;
                log( "TestPriceRegenerationHandler invocations : " + hndler.getHandlerInvocations() + " PrcRegenParam : " + hndler.getPriceRegenerationParameters().getDisplayKey() + " PrcRegen : " + hndler.getPriceRegeneration() );
                assertEquals( "Handler Invocation should be greater than 2. PrcRegenParam : " + hndler.getPriceRegenerationParameters().getDisplayKey(), hndler.getHandlerInvocations() > 0 && hndler.getHandlerInvocations() > 2, true );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "inside testMultiQuotePriceRegenWithoutReplenishmentAndBlackoutUntilNextQuoteEnabled" );
        }
        finally
        {
            // reset the black out
            for ( int index = 0; index < noReplenishRegenParams.size(); index++ )
            {
                prcRegenParam = ( PriceRegenerationParameters ) noReplenishRegenParams.toArray()[index];
                prcRegenParam.setAllowOnlyOneMatchPerQuote( false );
            }
        }
    }

    /**
     * This methods tests all combinations of price regeneration parameters without any replenishment and tier regen model reducing liquidity from lowest tier T1.
     */
    public void testMultiQuotePriceRegenWithoutReplenishmentTierLevelLiquidityHighToLowBid()
    {
        init();
        PriceRegenerationParameters prcRegenParam;
        List<MessageHandler> handlers = new ArrayList<MessageHandler>( 15 );
        try
        {
            for ( int index = 0; index < noReplenishRegenParams.size(); index++ )
            {
                prcRegenParam = ( PriceRegenerationParameters ) noReplenishRegenParams.toArray()[index];
                if ( prcRegenParam.getRegenerationModel() != PriceRegenerationParameters.HIGHEST_TO_LOWEST_REGENERATION )
                {
                    continue;
                }
                String ccyPair = currencyPairs[index];
                org1.setPriceRegenerationParameters( prcRegenParam );
                Quote newQuote = prepareQuote( org1, ccyPair, getBidTierLimit(), getOfferTierLimit() );
                String provRateId = "Q100";
                newQuote.setRateId( provRateId );
                svc.applyPriceRegenerationRules( newQuote, org1Tp1, isMultiTier() );

                TestPriceRegenerationHandler testHandler = new TestPriceRegenerationHandler();
                testHandler.setPriceRegenerationParameters( prcRegenParam );
                handlers.add( testHandler );

                // now take first tier liquidity
                double amt = getBidTierLimit();
                Trade trade = prepareTrade( amt, true, ccyPair, org1, org1Tp1 );
                trade.setAcceptedQuoteTier( 0 );
                Request req = DealingFactory.newRequest();
                req.setAcceptedQuote( newQuote );
                trade.setRequest( req );
                boolean result = svc.startPriceRegeneration( trade, testHandler );
                assertEquals( "TakenTradeLimit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result, true );

                sleepFor( 2000 );

                // now create a new trade with same tier info. This trade should fail now.
                Trade trade1 = prepareTrade( amt, true, ccyPair, org1, org1Tp1 );
                trade1.setAcceptedQuoteTier( 0 );
                Request req1 = DealingFactory.newRequest();
                req1.setAcceptedQuote( newQuote );
                trade1.setRequest( req1 );
                boolean result1 = svc.startPriceRegeneration( trade1, testHandler );
                assertEquals( "Failed due to insufficient liquidity at tier level. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result1, false );

                // an offer trade should go through
                Trade tradeOffer = prepareTrade( amt, false, ccyPair, org1, org1Tp1 );
                tradeOffer.setAcceptedQuoteTier( 0 );
                Request reqOffer = DealingFactory.newRequest();
                reqOffer.setAcceptedQuote( newQuote );
                tradeOffer.setRequest( reqOffer );
                boolean resultOffer = svc.startPriceRegeneration( tradeOffer, testHandler );
                assertEquals( "offer trade should be successful. PrcRegenParm : " + prcRegenParam.getDisplayKey(), resultOffer, true );

                // now attempt a new trade on a different tier.
                Trade trade2 = prepareTrade( amt, true, ccyPair, org1, org1Tp1 );
                trade2.setAcceptedQuoteTier( 1 );
                Request req2 = DealingFactory.newRequest();
                req2.setAcceptedQuote( newQuote );
                trade2.setRequest( req2 );
                boolean result2 = svc.startPriceRegeneration( trade2, testHandler );
                assertEquals( "Taken trade limit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result2, true );

                // now attempt a new trade on a tier for total limit on tier2
                Trade trade3 = prepareTrade( amt * 2, true, ccyPair, org1, org1Tp1 );
                trade3.setAcceptedQuoteTier( 1 );
                Request req3 = DealingFactory.newRequest();
                req3.setAcceptedQuote( newQuote );
                trade3.setRequest( req3 );
                boolean result3 = svc.startPriceRegeneration( trade3, testHandler );
                assertEquals( "Should fail. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result3, false );

                // create another trade after a new quote.
                Quote newQuote1 = prepareQuote( org1, ccyPair, getBidTierLimit(), getOfferTierLimit() );
                String provRateId1 = "Q101";
                newQuote1.setRateId( provRateId1 );
                svc.applyPriceRegenerationRules( newQuote1, org1Tp1, isMultiTier() );

                Trade trade4 = prepareTrade( amt, true, ccyPair, org1, org1Tp1 );
                trade4.setAcceptedQuoteTier( 0 );
                Request req4 = DealingFactory.newRequest();
                req4.setAcceptedQuote( newQuote1 );
                trade4.setRequest( req4 );

                boolean result4 = svc.startPriceRegeneration( trade4, testHandler );
                assertEquals( "trade success due to a newer quote. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result4, true );
            }

            sleepFor( 2000 );

            for ( MessageHandler handler1 : handlers )
            {
                TestPriceRegenerationHandler hndler = ( TestPriceRegenerationHandler ) handler1;
                log( "TestPriceRegenerationHandler invocations : " + hndler.getHandlerInvocations() + " PrcRegenParam : " + hndler.getPriceRegenerationParameters().getDisplayKey() + " PrcRegen : " + hndler.getPriceRegeneration() );
                assertEquals( "Handler Invocation should be greater than 2. PrcRegenParam : " + hndler.getPriceRegenerationParameters().getDisplayKey(), hndler.getHandlerInvocations() > 0 && hndler.getHandlerInvocations() > 2, true );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "inside testMultiQuotePriceRegenWithoutReplenishmentTierLevelLiquidityHighToLowBid" );
        }
    }

    /**
     * This methods tests all combinations of price regeneration parameters without any replenishment and tier regen model reducing liquidity from highest tier T3.
     */
    public void testMultiQuotePriceRegenWithoutReplenishmentTierLevelLiquidityLowToHighBid()
    {
        init();
        PriceRegenerationParameters prcRegenParam;
        List<MessageHandler> handlers = new ArrayList<MessageHandler>( 15 );
        try
        {
            for ( int index = 0; index < noReplenishRegenParams.size(); index++ )
            {
                prcRegenParam = ( PriceRegenerationParameters ) noReplenishRegenParams.toArray()[index];
                if ( prcRegenParam.getRegenerationModel() == PriceRegenerationParameters.HIGHEST_TO_LOWEST_REGENERATION )
                {
                    continue;
                }
                String ccyPair = currencyPairs[index];
                org1.setPriceRegenerationParameters( prcRegenParam );
                Quote newQuote = prepareQuote( org1, ccyPair, getBidTierLimit(), getOfferTierLimit() );
                String provRateId = "Q100";
                newQuote.setRateId( provRateId );
                svc.applyPriceRegenerationRules( newQuote, org1Tp1, isMultiTier() );

                TestPriceRegenerationHandler testHandler = new TestPriceRegenerationHandler();
                testHandler.setPriceRegenerationParameters( prcRegenParam );
                handlers.add( testHandler );

                // take only first tier limit.
                double amt = getBidTierLimit();
                Trade trade = prepareTrade( amt, true, ccyPair, org1, org1Tp1 );
                trade.setAcceptedQuoteTier( 0 );
                Request req = DealingFactory.newRequest();
                req.setAcceptedQuote( newQuote );
                trade.setRequest( req );
                boolean result = svc.startPriceRegeneration( trade, testHandler );
                assertEquals( "TakenTradeLimit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result, true );

                sleepFor( 2000 );

                // now create a new trade with same tier info. This trade should not fail.
                Trade trade1 = prepareTrade( amt, true, ccyPair, org1, org1Tp1 );
                trade1.setAcceptedQuoteTier( 0 );
                Request req1 = DealingFactory.newRequest();
                req1.setAcceptedQuote( newQuote );
                trade1.setRequest( req1 );
                boolean result1 = svc.startPriceRegeneration( trade1, testHandler );
                assertEquals( "Taken trade limit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result1, true );

                // an offer trade should go through
                Trade tradeOffer = prepareTrade( amt, false, ccyPair, org1, org1Tp1 );
                tradeOffer.setAcceptedQuoteTier( 0 );
                Request reqOffer = DealingFactory.newRequest();
                reqOffer.setAcceptedQuote( newQuote );
                tradeOffer.setRequest( reqOffer );
                boolean resultOffer = svc.startPriceRegeneration( tradeOffer, testHandler );
                assertEquals( "offer trade should be successful. PrcRegenParm : " + prcRegenParam.getDisplayKey(), resultOffer, true );

                // now attempt a new trade on a different tier.
                Trade trade2 = prepareTrade( getBidTierLimit(), true, ccyPair, org1, org1Tp1 );
                trade2.setAcceptedQuoteTier( 1 );
                Request req2 = DealingFactory.newRequest();
                req2.setAcceptedQuote( newQuote );
                trade2.setRequest( req2 );
                boolean result2 = svc.startPriceRegeneration( trade2, testHandler );
                assertEquals( "Taken trade limit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result2, true );

                // now attempt a new trade on a different tier.
                Trade trade3 = prepareTrade( getBidTierLimit() * 2, true, ccyPair, org1, org1Tp1 );
                trade3.setAcceptedQuoteTier( 1 );
                Request req3 = DealingFactory.newRequest();
                req3.setAcceptedQuote( newQuote );
                trade3.setRequest( req3 );
                boolean result3 = svc.startPriceRegeneration( trade3, testHandler );
                assertEquals( "should be successful. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result3, true );

                // create another trade after a new quote.
                Quote newQuote1 = prepareQuote( org1, ccyPair, getBidTierLimit(), getOfferTierLimit() );
                String provRateId1 = "Q101";
                newQuote1.setRateId( provRateId1 );
                svc.applyPriceRegenerationRules( newQuote1, org1Tp1, isMultiTier() );

                Trade trade4 = prepareTrade( getBidLimit(), true, ccyPair, org1, org1Tp1 );
                trade4.setAcceptedQuoteTier( 0 );
                Request req4 = DealingFactory.newRequest();
                req4.setAcceptedQuote( newQuote1 );
                trade4.setRequest( req4 );

                boolean result4 = svc.startPriceRegeneration( trade4, testHandler );
                assertEquals( "trade success due to a newer quote. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result4, true );
            }

            sleepFor( 2000 );

            for ( MessageHandler handler1 : handlers )
            {
                TestPriceRegenerationHandler hndler = ( TestPriceRegenerationHandler ) handler1;
                log( "TestPriceRegenerationHandler invocations : " + hndler.getHandlerInvocations() + " PrcRegenParam : " + hndler.getPriceRegenerationParameters().getDisplayKey() + " PrcRegen : " + hndler.getPriceRegeneration() );
                assertEquals( "Handler Invocation should be greater than 2. PrcRegenParam : " + hndler.getPriceRegenerationParameters().getDisplayKey(), hndler.getHandlerInvocations() > 0 && hndler.getHandlerInvocations() > 2, true );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "inside testMultiQuotePriceRegenWithoutReplenishmentTierLevelLiquidityLowToHighBid" );
        }
    }

    /**
     * This methods tests all combinations of price regeneration parameters without any replenishment and tier regen model reducing liquidity from highest tier T3.
     */
    public void testMultiQuotePriceRegenWithoutReplenishmentTierLevelLiquidityHighToLowBidWithTierLevelTrackingDisabled()
    {
        init();
        PriceRegenerationParameters prcRegenParam;
        List<MessageHandler> handlers = new ArrayList<MessageHandler>( 15 );
        PriceRegenerationConfiguration config = ( PriceRegenerationConfiguration ) PriceRegenerationConfigurationFactory.getPriceRegenerationConfigurationMBean();
        try
        {
            config.setProperty( PriceRegenerationConfigurationMBean.PRICE_REGENERATION_TIER_LIQUIDITY_VALIDATION_ENABLED, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            for ( int index = 0; index < noReplenishRegenParams.size(); index++ )
            {
                prcRegenParam = ( PriceRegenerationParameters ) noReplenishRegenParams.toArray()[index];
                String ccyPair = currencyPairs[index];
                org1.setPriceRegenerationParameters( prcRegenParam );
                Quote newQuote = prepareQuote( org1, ccyPair, getBidTierLimit(), getOfferTierLimit() );
                String provRateId = "Q100";
                newQuote.setRateId( provRateId );
                svc.applyPriceRegenerationRules( newQuote, org1Tp1, isMultiTier() );

                TestPriceRegenerationHandler testHandler = new TestPriceRegenerationHandler();
                testHandler.setPriceRegenerationParameters( prcRegenParam );
                handlers.add( testHandler );

                // take only first tier limit.
                double amt = getBidTierLimit();
                Trade trade = prepareTrade( amt, true, ccyPair, org1, org1Tp1 );
                trade.setAcceptedQuoteTier( 0 );
                Request req = DealingFactory.newRequest();
                req.setAcceptedQuote( newQuote );
                trade.setRequest( req );
                boolean result = svc.startPriceRegeneration( trade, testHandler );
                assertEquals( "TakenTradeLimit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result, true );

                sleepFor( 10 );

                // now create a new trade with same tier info. This trade should not fail.
                Trade trade1 = prepareTrade( amt, true, ccyPair, org1, org1Tp1 );
                trade1.setAcceptedQuoteTier( 0 );
                Request req1 = DealingFactory.newRequest();
                req1.setAcceptedQuote( newQuote );
                trade1.setRequest( req1 );
                boolean result1 = svc.startPriceRegeneration( trade1, testHandler );
                assertEquals( "Taken trade limit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result1, true );

                // an offer trade should go through
                Trade tradeOffer = prepareTrade( amt, false, ccyPair, org1, org1Tp1 );
                tradeOffer.setAcceptedQuoteTier( 0 );
                Request reqOffer = DealingFactory.newRequest();
                reqOffer.setAcceptedQuote( newQuote );
                tradeOffer.setRequest( reqOffer );
                boolean resultOffer = svc.startPriceRegeneration( tradeOffer, testHandler );
                assertEquals( "offer trade should be successful. PrcRegenParm : " + prcRegenParam.getDisplayKey(), resultOffer, true );

                // now attempt a new trade on a different tier.
                Trade trade2 = prepareTrade( getBidTierLimit(), true, ccyPair, org1, org1Tp1 );
                trade2.setAcceptedQuoteTier( 1 );
                Request req2 = DealingFactory.newRequest();
                req2.setAcceptedQuote( newQuote );
                trade2.setRequest( req2 );
                boolean result2 = svc.startPriceRegeneration( trade2, testHandler );
                assertEquals( "Taken trade limit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result2, true );

                // now attempt a new trade on a different tier.
                Trade trade3 = prepareTrade( getBidTierLimit() * 2, true, ccyPair, org1, org1Tp1 );
                trade3.setAcceptedQuoteTier( 1 );
                Request req3 = DealingFactory.newRequest();
                req3.setAcceptedQuote( newQuote );
                trade3.setRequest( req3 );
                boolean result3 = svc.startPriceRegeneration( trade3, testHandler );
                assertEquals( "should be successful. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result3, true );

                // create another trade after a new quote.
                Quote newQuote1 = prepareQuote( org1, ccyPair, getBidTierLimit(), getOfferTierLimit() );
                String provRateId1 = "Q101";
                newQuote1.setRateId( provRateId1 );
                svc.applyPriceRegenerationRules( newQuote1, org1Tp1, isMultiTier() );

                Trade trade4 = prepareTrade( getBidLimit(), true, ccyPair, org1, org1Tp1 );
                trade4.setAcceptedQuoteTier( 0 );
                Request req4 = DealingFactory.newRequest();
                req4.setAcceptedQuote( newQuote1 );
                trade4.setRequest( req4 );

                boolean result4 = svc.startPriceRegeneration( trade4, testHandler );
                assertEquals( "trade success due to a newer quote. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result4, true );
            }

            sleepFor( 2000 );

            for ( MessageHandler handler1 : handlers )
            {
                TestPriceRegenerationHandler hndler = ( TestPriceRegenerationHandler ) handler1;
                log( "TestPriceRegenerationHandler invocations : " + hndler.getHandlerInvocations() + " PrcRegenParam : " + hndler.getPriceRegenerationParameters().getDisplayKey() + " PrcRegen : " + hndler.getPriceRegeneration() );
                assertEquals( "Handler Invocation should be greater than 2. PrcRegenParam : " + hndler.getPriceRegenerationParameters().getDisplayKey(), hndler.getHandlerInvocations() > 0 && hndler.getHandlerInvocations() > 2, true );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "inside testMultiQuotePriceRegenWithoutReplenishmentTierLevelLiquidityHighToLowBidWithTierLevelTrackingDisabled" );
        }
        finally
        {
            config.setProperty( PriceRegenerationConfigurationMBean.PRICE_REGENERATION_TIER_LIQUIDITY_VALIDATION_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
        }
    }

    /**
     * This methods tests all combinations of price regeneration parameters without any replenishment and tier regen model reducing liquidity from lowest tier T1.
     */
    public void testMultiQuotePriceRegenWithoutReplenishmentTierLevelLiquidityHighToLowOffer()
    {
        init();
        PriceRegenerationParameters prcRegenParam;
        List<MessageHandler> handlers = new ArrayList<MessageHandler>( 15 );
        try
        {
            for ( int index = 0; index < noReplenishRegenParams.size(); index++ )
            {
                prcRegenParam = ( PriceRegenerationParameters ) noReplenishRegenParams.toArray()[index];
                if ( prcRegenParam.getRegenerationModel() != PriceRegenerationParameters.HIGHEST_TO_LOWEST_REGENERATION )
                {
                    continue;
                }
                String ccyPair = currencyPairs[index];
                org1.setPriceRegenerationParameters( prcRegenParam );
                Quote newQuote = prepareQuote( org1, ccyPair, getBidTierLimit(), getOfferTierLimit() );
                String provRateId = "Q100";
                newQuote.setRateId( provRateId );
                svc.applyPriceRegenerationRules( newQuote, org1Tp1, isMultiTier() );

                TestPriceRegenerationHandler testHandler = new TestPriceRegenerationHandler();
                testHandler.setPriceRegenerationParameters( prcRegenParam );
                handlers.add( testHandler );

                // now take first tier liquidity
                double amt = getBidTierLimit();
                Trade trade = prepareTrade( amt, false, ccyPair, org1, org1Tp1 );
                trade.setAcceptedQuoteTier( 0 );
                Request req = DealingFactory.newRequest();
                req.setAcceptedQuote( newQuote );
                trade.setRequest( req );
                boolean result = svc.startPriceRegeneration( trade, testHandler );
                assertEquals( "TakenTradeLimit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result, true );

                sleepFor( 10 );

                // now create a new trade with same tier info. This trade should fail now.
                Trade trade1 = prepareTrade( amt, false, ccyPair, org1, org1Tp1 );
                trade1.setAcceptedQuoteTier( 0 );
                Request req1 = DealingFactory.newRequest();
                req1.setAcceptedQuote( newQuote );
                trade1.setRequest( req1 );
                boolean result1 = svc.startPriceRegeneration( trade1, testHandler );
                assertEquals( "Failed due to insufficient liquidity at tier level. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result1, false );

                // an offer trade should go through
                Trade tradeBid = prepareTrade( amt, true, ccyPair, org1, org1Tp1 );
                tradeBid.setAcceptedQuoteTier( 0 );
                Request reqBid = DealingFactory.newRequest();
                reqBid.setAcceptedQuote( newQuote );
                tradeBid.setRequest( reqBid );
                boolean resultBid = svc.startPriceRegeneration( tradeBid, testHandler );
                assertEquals( "bid trade should be successful. PrcRegenParm : " + prcRegenParam.getDisplayKey(), resultBid, true );

                // now attempt a new trade on a different tier.
                Trade trade2 = prepareTrade( amt, false, ccyPair, org1, org1Tp1 );
                trade2.setAcceptedQuoteTier( 1 );
                Request req2 = DealingFactory.newRequest();
                req2.setAcceptedQuote( newQuote );
                trade2.setRequest( req2 );
                boolean result2 = svc.startPriceRegeneration( trade2, testHandler );
                assertEquals( "Taken trade limit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result2, true );

                // now attempt a new trade on a tier for total limit on tier2
                Trade trade3 = prepareTrade( amt * 2, false, ccyPair, org1, org1Tp1 );
                trade3.setAcceptedQuoteTier( 1 );
                Request req3 = DealingFactory.newRequest();
                req3.setAcceptedQuote( newQuote );
                trade3.setRequest( req3 );
                boolean result3 = svc.startPriceRegeneration( trade3, testHandler );
                assertEquals( "Should fail. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result3, false );

                // create another trade after a new quote.
                Quote newQuote1 = prepareQuote( org1, ccyPair, getBidTierLimit(), getOfferTierLimit() );
                String provRateId1 = "Q101";
                newQuote1.setRateId( provRateId1 );
                svc.applyPriceRegenerationRules( newQuote1, org1Tp1, isMultiTier() );

                Trade trade4 = prepareTrade( amt, false, ccyPair, org1, org1Tp1 );
                trade4.setAcceptedQuoteTier( 0 );
                Request req4 = DealingFactory.newRequest();
                req4.setAcceptedQuote( newQuote1 );
                trade4.setRequest( req4 );

                boolean result4 = svc.startPriceRegeneration( trade4, testHandler );
                assertEquals( "trade success due to a newer quote. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result4, true );
            }

            sleepFor( 2000 );

            for ( MessageHandler handler1 : handlers )
            {
                TestPriceRegenerationHandler hndler = ( TestPriceRegenerationHandler ) handler1;
                log( "TestPriceRegenerationHandler invocations : " + hndler.getHandlerInvocations() + " PrcRegenParam : " + hndler.getPriceRegenerationParameters().getDisplayKey() + " PrcRegen : " + hndler.getPriceRegeneration() );
                assertEquals( "Handler Invocation should be greater than 2. PrcRegenParam : " + hndler.getPriceRegenerationParameters().getDisplayKey(), hndler.getHandlerInvocations() > 0 && hndler.getHandlerInvocations() > 2, true );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "inside testMultiQuotePriceRegenWithoutReplenishmentTierLevelLiquidityHighToLowOffer" );
        }
    }

    /**
     * This methods tests all combinations of price regeneration parameters without any replenishment and tier regen model reducing liquidity from highest tier T3.
     */
    public void testMultiQuotePriceRegenWithoutReplenishmentTierLevelLiquidityLowToHighOffer()
    {
        init();
        PriceRegenerationParameters prcRegenParam;
        List<MessageHandler> handlers = new ArrayList<MessageHandler>( 15 );
        try
        {
            for ( int index = 0; index < noReplenishRegenParams.size(); index++ )
            {
                prcRegenParam = ( PriceRegenerationParameters ) noReplenishRegenParams.toArray()[index];
                if ( prcRegenParam.getRegenerationModel() == PriceRegenerationParameters.HIGHEST_TO_LOWEST_REGENERATION )
                {
                    continue;
                }
                String ccyPair = currencyPairs[index];
                org1.setPriceRegenerationParameters( prcRegenParam );
                Quote newQuote = prepareQuote( org1, ccyPair, getBidTierLimit(), getOfferTierLimit() );
                String provRateId = "Q100";
                newQuote.setRateId( provRateId );
                svc.applyPriceRegenerationRules( newQuote, org1Tp1, isMultiTier() );

                TestPriceRegenerationHandler testHandler = new TestPriceRegenerationHandler();
                testHandler.setPriceRegenerationParameters( prcRegenParam );
                handlers.add( testHandler );

                // take only first tier limit.
                double amt = getBidTierLimit();
                Trade trade = prepareTrade( amt, false, ccyPair, org1, org1Tp1 );
                trade.setAcceptedQuoteTier( 0 );
                Request req = DealingFactory.newRequest();
                req.setAcceptedQuote( newQuote );
                trade.setRequest( req );
                boolean result = svc.startPriceRegeneration( trade, testHandler );
                assertEquals( "TakenTradeLimit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result, true );

                sleepFor( 10 );

                // now create a new trade with same tier info. This trade should not fail.
                Trade trade1 = prepareTrade( amt, false, ccyPair, org1, org1Tp1 );
                trade1.setAcceptedQuoteTier( 0 );
                Request req1 = DealingFactory.newRequest();
                req1.setAcceptedQuote( newQuote );
                trade1.setRequest( req1 );
                boolean result1 = svc.startPriceRegeneration( trade1, testHandler );
                assertEquals( "Taken trade limit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result1, true );

                // an offer trade should go through
                Trade tradeBid = prepareTrade( amt, true, ccyPair, org1, org1Tp1 );
                tradeBid.setAcceptedQuoteTier( 0 );
                Request reqBid = DealingFactory.newRequest();
                reqBid.setAcceptedQuote( newQuote );
                tradeBid.setRequest( reqBid );
                boolean resultBid = svc.startPriceRegeneration( tradeBid, testHandler );
                assertEquals( "bid trade should be successful. PrcRegenParm : " + prcRegenParam.getDisplayKey(), resultBid, true );

                // now attempt a new trade on a different tier.
                Trade trade2 = prepareTrade( getBidTierLimit(), false, ccyPair, org1, org1Tp1 );
                trade2.setAcceptedQuoteTier( 1 );
                Request req2 = DealingFactory.newRequest();
                req2.setAcceptedQuote( newQuote );
                trade2.setRequest( req2 );
                boolean result2 = svc.startPriceRegeneration( trade2, testHandler );
                assertEquals( "Taken trade limit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result2, true );

                // now attempt a new trade on a different tier.
                Trade trade3 = prepareTrade( getBidTierLimit() * 2, false, ccyPair, org1, org1Tp1 );
                trade3.setAcceptedQuoteTier( 1 );
                Request req3 = DealingFactory.newRequest();
                req3.setAcceptedQuote( newQuote );
                trade3.setRequest( req3 );
                boolean result3 = svc.startPriceRegeneration( trade3, testHandler );
                assertEquals( "should be successful. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result3, true );

                // create another trade after a new quote.
                Quote newQuote1 = prepareQuote( org1, ccyPair, getBidTierLimit(), getOfferTierLimit() );
                String provRateId1 = "Q101";
                newQuote1.setRateId( provRateId1 );
                svc.applyPriceRegenerationRules( newQuote1, org1Tp1, isMultiTier() );

                Trade trade4 = prepareTrade( getBidLimit(), false, ccyPair, org1, org1Tp1 );
                trade4.setAcceptedQuoteTier( 0 );
                Request req4 = DealingFactory.newRequest();
                req4.setAcceptedQuote( newQuote1 );
                trade4.setRequest( req4 );

                boolean result4 = svc.startPriceRegeneration( trade4, testHandler );
                assertEquals( "trade success due to a newer quote. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result4, true );
            }

            sleepFor( 2000 );

            for ( MessageHandler handler1 : handlers )
            {
                TestPriceRegenerationHandler hndler = ( TestPriceRegenerationHandler ) handler1;
                log( "TestPriceRegenerationHandler invocations : " + hndler.getHandlerInvocations() + " PrcRegenParam : " + hndler.getPriceRegenerationParameters().getDisplayKey() + " PrcRegen : " + hndler.getPriceRegeneration() );
                assertEquals( "Handler Invocation should be greater than 2. PrcRegenParam : " + hndler.getPriceRegenerationParameters().getDisplayKey(), hndler.getHandlerInvocations() > 0 && hndler.getHandlerInvocations() > 2, true );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "inside testMultiQuotePriceRegenWithoutReplenishmentTierLevelLiquidityLowToHighOffer" );
        }
    }

    /**
     * This methods tests all combinations of price regeneration parameters without any replenishment and tier regen model reducing liquidity from highest tier T3.
     */
    public void testMultiQuotePriceRegenWithoutReplenishmentTierLevelLiquidityHighToLowOfferWithTierLevelTrackingDisabled()
    {
        init();
        PriceRegenerationParameters prcRegenParam;
        List<MessageHandler> handlers = new ArrayList<MessageHandler>( 15 );
        PriceRegenerationConfiguration config = ( PriceRegenerationConfiguration ) PriceRegenerationConfigurationFactory.getPriceRegenerationConfigurationMBean();
        try
        {
            config.setProperty( PriceRegenerationConfigurationMBean.PRICE_REGENERATION_TIER_LIQUIDITY_VALIDATION_ENABLED, "false", ConfigurationProperty.DYNAMIC_SCOPE, null );
            for ( int index = 0; index < noReplenishRegenParams.size(); index++ )
            {
                prcRegenParam = ( PriceRegenerationParameters ) noReplenishRegenParams.toArray()[index];
                String ccyPair = currencyPairs[index];
                org1.setPriceRegenerationParameters( prcRegenParam );
                Quote newQuote = prepareQuote( org1, ccyPair, getBidTierLimit(), getOfferTierLimit() );
                String provRateId = "Q100";
                newQuote.setRateId( provRateId );
                svc.applyPriceRegenerationRules( newQuote, org1Tp1, isMultiTier() );

                TestPriceRegenerationHandler testHandler = new TestPriceRegenerationHandler();
                testHandler.setPriceRegenerationParameters( prcRegenParam );
                handlers.add( testHandler );

                // take only first tier limit.
                double amt = getBidTierLimit();
                Trade trade = prepareTrade( amt, false, ccyPair, org1, org1Tp1 );
                trade.setAcceptedQuoteTier( 0 );
                Request req = DealingFactory.newRequest();
                req.setAcceptedQuote( newQuote );
                trade.setRequest( req );
                boolean result = svc.startPriceRegeneration( trade, testHandler );
                assertEquals( "TakenTradeLimit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result, true );

                sleepFor( 10 );

                // now create a new trade with same tier info. This trade should not fail.
                Trade trade1 = prepareTrade( amt, false, ccyPair, org1, org1Tp1 );
                trade1.setAcceptedQuoteTier( 0 );
                Request req1 = DealingFactory.newRequest();
                req1.setAcceptedQuote( newQuote );
                trade1.setRequest( req1 );
                boolean result1 = svc.startPriceRegeneration( trade1, testHandler );
                assertEquals( "Taken trade limit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result1, true );

                // an offer trade should go through
                Trade tradeBid = prepareTrade( amt, true, ccyPair, org1, org1Tp1 );
                tradeBid.setAcceptedQuoteTier( 0 );
                Request reqBid = DealingFactory.newRequest();
                reqBid.setAcceptedQuote( newQuote );
                tradeBid.setRequest( reqBid );
                boolean resultBid = svc.startPriceRegeneration( tradeBid, testHandler );
                assertEquals( "bid trade should be successful. PrcRegenParm : " + prcRegenParam.getDisplayKey(), resultBid, true );

                // now attempt a new trade on a different tier.
                Trade trade2 = prepareTrade( getBidTierLimit(), false, ccyPair, org1, org1Tp1 );
                trade2.setAcceptedQuoteTier( 1 );
                Request req2 = DealingFactory.newRequest();
                req2.setAcceptedQuote( newQuote );
                trade2.setRequest( req2 );
                boolean result2 = svc.startPriceRegeneration( trade2, testHandler );
                assertEquals( "Taken trade limit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result2, true );

                // now attempt a new trade on a different tier.
                Trade trade3 = prepareTrade( getBidTierLimit() * 2, false, ccyPair, org1, org1Tp1 );
                trade3.setAcceptedQuoteTier( 1 );
                Request req3 = DealingFactory.newRequest();
                req3.setAcceptedQuote( newQuote );
                trade3.setRequest( req3 );
                boolean result3 = svc.startPriceRegeneration( trade3, testHandler );
                assertEquals( "should be successful. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result3, true );

                // create another trade after a new quote.
                Quote newQuote1 = prepareQuote( org1, ccyPair, getBidTierLimit(), getOfferTierLimit() );
                String provRateId1 = "Q101";
                newQuote1.setRateId( provRateId1 );
                svc.applyPriceRegenerationRules( newQuote1, org1Tp1, isMultiTier() );

                Trade trade4 = prepareTrade( getBidLimit(), false, ccyPair, org1, org1Tp1 );
                trade4.setAcceptedQuoteTier( 0 );
                Request req4 = DealingFactory.newRequest();
                req4.setAcceptedQuote( newQuote1 );
                trade4.setRequest( req4 );

                boolean result4 = svc.startPriceRegeneration( trade4, testHandler );
                assertEquals( "trade success due to a newer quote. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result4, true );
            }

            sleepFor( 2000 );

            for ( MessageHandler handler1 : handlers )
            {
                TestPriceRegenerationHandler hndler = ( TestPriceRegenerationHandler ) handler1;
                log( "TestPriceRegenerationHandler invocations : " + hndler.getHandlerInvocations() + " PrcRegenParam : " + hndler.getPriceRegenerationParameters().getDisplayKey() + " PrcRegen : " + hndler.getPriceRegeneration() );
                assertEquals( "Handler Invocation should be greater than 2. PrcRegenParam : " + hndler.getPriceRegenerationParameters().getDisplayKey(), hndler.getHandlerInvocations() > 0 && hndler.getHandlerInvocations() > 2, true );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "inside testMultiQuotePriceRegenWithoutReplenishmentTierLevelLiquidityHighToLowOfferWithTierLevelTrackingDisabled" );
        }
        finally
        {
            config.setProperty( PriceRegenerationConfigurationMBean.PRICE_REGENERATION_TIER_LIQUIDITY_VALIDATION_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE, null );
        }
    }


	/**
	 * Tests the following for Highest to Lowest params
	 * 1. PriceRegenWrapperCache has one and only one copy for each LP-FI-Ccy pair combination
	 * 2. Functionality of PriceRegenWrapper (hasSufficientLiquidity())
	 */
	public void testMultiQuotePriceRegenerationPriceRegenWrapperHighestToLowest()
	{
		init();
		PriceRegenerationParameters prcRegenParam;
		int numberOfTiers = 2;
		setNumberOfTiers(numberOfTiers);
		try
		{
			Set<PriceRegenerationParameters> regenParams = new HashSet<PriceRegenerationParameters>();
			regenParams.addAll(highestParams);
			
			for ( int index = 0; index < regenParams.size(); index++ )
			{
				prcRegenParam = ( PriceRegenerationParameters ) regenParams.toArray()[index];
				String ccyPair = currencyPairs[index];
				org1.setPriceRegenerationParameters( prcRegenParam );

				PriceRegenerationWrapper wrapper1 = svc.getPriceRegenWrapper(org1.getShortName(), org1Tp1.getShortName(), ccyPair);
				double tierLimit = 5000;
				Quote quote0 = prepareQuote( org1, ccyPair, tierLimit, tierLimit);
				svc.applyPriceRegenerationRules( quote0, org1Tp1, isMultiTier() );
				PriceRegenerationWrapper wrapper2 = svc.getPriceRegenWrapper(org1.getShortName(), org1Tp1.getShortName(), ccyPair);
				assertEquals( "Same PriceRegenWrapper must be returned" , true, wrapper1==wrapper2);
				assertEquals( "Wrapper should have sufficient liquidity" , true, wrapper2.hasSufficientLiquidity(tierLimit-1, 0, true));
				assertEquals( "Wrapper should not have sufficient liquidity" , false, wrapper2.hasSufficientLiquidity(tierLimit+1, 0, true));
				assertEquals( "Wrapper should have sufficient liquidity in the next tier" , true, wrapper2.hasSufficientLiquidity(tierLimit+1, 1, true));
				
				
				TestPriceRegenerationHandler testHandler = new TestPriceRegenerationHandler();
				testHandler.setPriceRegenerationParameters( prcRegenParam );
				// now start taking trades.
				double tradeAmt = 4000;
				Trade trade = prepareTrade( tradeAmt, true, ccyPair, org1, org1Tp1 );
				boolean result1 = svc.startPriceRegeneration( trade, testHandler );
				PriceRegeneration prcRegen1 = testHandler.getPriceRegeneration();
				log( "Taken Trading limit. prcRegen=" + prcRegen1 + " prcRegenParam = " + prcRegenParam.getDisplayKey() );
				PriceRegenerationWrapper wrapper3 = svc.getPriceRegenWrapper(org1.getShortName(), org1Tp1.getShortName(), ccyPair);
				assertEquals( "Same PriceRegenWrapper must be returned" , true, wrapper3==wrapper2);
				assertEquals( "Wrapper should have sufficient liquidity" , true, wrapper3.hasSufficientLiquidity(tierLimit-tradeAmt-1, 0, true));
				assertEquals( "Wrapper should not have sufficient liquidity", false,  wrapper3.hasSufficientLiquidity(tierLimit-tradeAmt+1, 0, true));
				
				break;
			}
		}
		catch ( Exception e )
		{
			e.printStackTrace();
			fail( "inside testMultiQuotePriceRegenerationWithRapidFireTakingTradingLimit" );
		}
	}

	
	/**
	 * Tests the following for Lowest to Highest params
	 * 1. PriceRegenWrapperCache has one and only one copy for each LP-FI-Ccy pair combination
	 * 2. Functionality of PriceRegenWrapper (hasSufficientLiquidity())
	 */
	public void testMultiQuotePriceRegenerationPriceRegenWrapperLowestToHighest()
	{
		init();
		PriceRegenerationParameters prcRegenParam;
		int numberOfTiers = 2;
		setNumberOfTiers(numberOfTiers);
		try
		{
			Set<PriceRegenerationParameters> regenParams = new HashSet<PriceRegenerationParameters>();
			regenParams.addAll(lowestParams);
			
			for ( int index = 0; index < regenParams.size(); index++ )
			{
				prcRegenParam = ( PriceRegenerationParameters ) regenParams.toArray()[index];
				String ccyPair = currencyPairs[index];
				org1.setPriceRegenerationParameters( prcRegenParam );

				PriceRegenerationWrapper wrapper1 = svc.getPriceRegenWrapper(org1.getShortName(), org1Tp1.getShortName(), ccyPair);
				double tierLimit = 5000;
				Quote quote0 = prepareQuote( org1, ccyPair, tierLimit, tierLimit);
				svc.applyPriceRegenerationRules( quote0, org1Tp1, isMultiTier() );
				PriceRegenerationWrapper wrapper2 = svc.getPriceRegenWrapper(org1.getShortName(), org1Tp1.getShortName(), ccyPair);
				assertEquals( "Same PriceRegenWrapper must be returned" , true, wrapper1==wrapper2);
				assertEquals( "Wrapper should have sufficient liquidity" , true, wrapper2.hasSufficientLiquidity(tierLimit-1, 0, true));
				assertEquals( "Wrapper should not have sufficient liquidity" , false, wrapper2.hasSufficientLiquidity(tierLimit+1, 0, true));
				assertEquals( "Wrapper should have sufficient liquidity in the next tier" , true, wrapper2.hasSufficientLiquidity(tierLimit+1, 1, true));
				
				
				TestPriceRegenerationHandler testHandler = new TestPriceRegenerationHandler();
				testHandler.setPriceRegenerationParameters( prcRegenParam );
				// now start taking trades.
				double tradeAmt = 9000;
				Trade trade = prepareTrade( tradeAmt, true, ccyPair, org1, org1Tp1 );
				boolean result1 = svc.startPriceRegeneration( trade, testHandler );
				PriceRegeneration prcRegen1 = testHandler.getPriceRegeneration();
				log( "Taken Trading limit. prcRegen=" + prcRegen1 + " prcRegenParam = " + prcRegenParam.getDisplayKey() );
				PriceRegenerationWrapper wrapper3 = svc.getPriceRegenWrapper(org1.getShortName(), org1Tp1.getShortName(), ccyPair);
				assertEquals( "Same PriceRegenWrapper must be returned" , true, wrapper3==wrapper2);
				assertEquals( "Wrapper should have sufficient liquidity" , true, wrapper3.hasSufficientLiquidity(999, 1, true));
				assertEquals( "Wrapper should not have sufficient liquidity", false,  wrapper3.hasSufficientLiquidity(1001, 1, true));
				
				break;
			}
		}
		catch ( Exception e )
		{
			e.printStackTrace();
			fail( "inside testMultiQuotePriceRegenerationWithRapidFireTakingTradingLimit" );
		}
	}

    public void testMultiQuoteWithRapidFireTakingTradingLimitOnSameOrderWithSweep()
    {
        init();
        PriceRegenerationParameters prcRegenParam;
        try
        {
            Set<PriceRegenerationParameters> regenParams = new HashSet<PriceRegenerationParameters>();
            regenParams.addAll(replenishRegenParams);
            regenParams.addAll(noReplenishRegenParams);

            for ( int index = 0; index < regenParams.size(); index++ )
            {
                int rapidFirePeriod = 1000;
                prcRegenParam = ( PriceRegenerationParameters ) regenParams.toArray()[index];
                prcRegenParam.setRapidFireEnabled( true );
                prcRegenParam.setRapidFirePeriod( rapidFirePeriod );
                String ccyPair = currencyPairs[index];
                org1.setPriceRegenerationParameters( prcRegenParam );

                Quote quote0 = prepareQuote( org1, ccyPair, 5000, 5000 );
                quote0.setRateId( "G100" );
                svc.applyPriceRegenerationRules( quote0, org1Tp1, isMultiTier() );

                TestPriceRegenerationHandler testHandler = new TestPriceRegenerationHandler();
                testHandler.setPriceRegenerationParameters( prcRegenParam );

                // now start taking trades.
                Trade trade = prepareTrade( 4000, true, ccyPair, org1, org1Tp1 );
                Request request = new RequestC();
                request.setAcceptedQuote( quote0 );
                request.getToOrganizations().add( org1 );
                request.setOrderId( "1000" );
                request.setTrade( trade );
                trade.setRequest( request );
                Request order = new RequestC();
                order.addChildRequest( request );
                request.setParentOCORequest( order );
                order.setOrderId( "1000" );
                trade.setSweepNumber( 1 );
                boolean result1 = svc.startPriceRegeneration( trade, testHandler );
                PriceRegeneration prcRegen1 = testHandler.getPriceRegeneration();
                log( "Taken Trading limit. prcRegen=" + prcRegen1 + " prcRegenParam = " + prcRegenParam.getDisplayKey() );
                assertEquals( "TakenTradeLimit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result1, true );
                assertEquals( "PriceRegen should show in rapid fire period. " + prcRegen1.isRapidFirePeriod(), prcRegen1.isRapidFirePeriod(), true );
                assertEquals( "PriceRegen service show in rapid fire period. " + svc.isInRapidFirePeriod( org1, org1Tp1, ccyPair ), svc.isInRapidFirePeriod( org1, org1Tp1, ccyPair ), true );
                assertEquals( "Handler invocations should not be more than 1. " + testHandler.getHandlerInvocations(), testHandler.getHandlerInvocations() < 2, true );

                // now create another trade with the same order id.
                Trade trade1 = prepareTrade( 4000, true, ccyPair, org1, org1Tp1 );
                Request request1 = new RequestC();
                request1.setAcceptedQuote( quote0 );
                request1.getToOrganizations().add( org1 );
                request1.setOrderId( "1000" );
                request1.setTrade( trade1 );
                trade1.setRequest( request1 );
                order.addChildRequest( request1 );
                request1.setParentRequest( order );
                trade1.setSweepNumber( 1 );
                boolean result2 = svc.startPriceRegeneration( trade1, testHandler );
                PriceRegeneration prcRegenx = testHandler.getPriceRegeneration();
                log( "Taken trading limit. prcRegen=" + prcRegenx );
                assertEquals( "Should allow the trade. result2=" + result2, result2, true );
                assertEquals( "TakenTradeLimit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result2, true );
                assertEquals( "PriceRegen should show in rapid fire period. " + prcRegenx.isRapidFirePeriod(), prcRegenx.isRapidFirePeriod(), true );
                assertEquals( "PriceRegen service show in rapid fire period. " + svc.isInRapidFirePeriod( org1, org1Tp1, ccyPair ), svc.isInRapidFirePeriod( org1, org1Tp1, ccyPair ), true );
                assertEquals( "Handler invocations should not be more than 2. " + testHandler.getHandlerInvocations(), testHandler.getHandlerInvocations() < 3, true );

                // now create another trade with the same order id, but different sweep number
                Trade trade2 = prepareTrade( 4000, true, ccyPair, org1, org1Tp1 );
                Request request2 = new RequestC();
                request2.setAcceptedQuote( quote0 );
                request2.getToOrganizations().add( org1 );
                request2.setOrderId( "1000" );
                request2.setTrade( trade2 );
                trade2.setRequest( request1 );
                order.addChildRequest( request2 );
                request2.setParentRequest( order );
                trade2.setSweepNumber( 2 );
                boolean result3x = svc.startPriceRegeneration( trade2, testHandler );
                PriceRegeneration prcRegenx1 = testHandler.getPriceRegeneration();
                log( "Taken trading limit. prcRegen=" + prcRegenx1 );
                assertEquals( "Should not allow the trade. result2=" + result3x, result3x, false );
                assertEquals( "TakenTradeLimit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result3x, false );
                assertEquals( "PriceRegen should show in rapid fire period. " + prcRegenx.isRapidFirePeriod(), prcRegenx.isRapidFirePeriod(), true );
                assertEquals( "PriceRegen service show in rapid fire period. " + svc.isInRapidFirePeriod( org1, org1Tp1, ccyPair ), svc.isInRapidFirePeriod( org1, org1Tp1, ccyPair ), true );
                assertEquals( "Handler invocations should not be more than 2. " + testHandler.getHandlerInvocations(), testHandler.getHandlerInvocations() < 3, true );

                // now create another trade which does not originate from the same order. this should get rejected.
                Trade differentTrade = prepareTrade( 4000, true, ccyPair, org1, org1Tp1 );
                Request differentRequest = new RequestC();
                differentRequest.setAcceptedQuote( quote0 );
                differentRequest.getToOrganizations().add( org1 );
                differentRequest.setOrderId( "1001" );
                differentRequest.setTrade( differentTrade );
                differentTrade.setRequest( differentRequest );
                Request differentOrder = new RequestC();
                differentOrder.addChildRequest( differentRequest );
                differentRequest.setParentOCORequest( differentOrder );
                differentOrder.setOrderId( "1001" );
                differentTrade.setSweepNumber( 1 );
                boolean result3 = svc.startPriceRegeneration( differentTrade, testHandler );
                PriceRegeneration prcRegeny = testHandler.getPriceRegeneration();
                log( "Taken trading limit. prcRegen=" + prcRegeny );
                assertEquals( "Should not allow the trade. result3=" + result3, result3, false );
                assertEquals( "TakenTradeLimit. PrcRegenParm : " + prcRegenParam.getDisplayKey(), result3, false );
                assertEquals( "PriceRegen should show in rapid fire period. " + prcRegeny.isRapidFirePeriod(), prcRegeny.isRapidFirePeriod(), true );
                assertEquals( "PriceRegen service show in rapid fire period. " + svc.isInRapidFirePeriod( org1, org1Tp1, ccyPair ), svc.isInRapidFirePeriod( org1, org1Tp1, ccyPair ), true );
                assertEquals( "Handler invocations should not be more than 2. " + testHandler.getHandlerInvocations(), testHandler.getHandlerInvocations() < 3, true );

                sleepFor( 200 + ( rapidFirePeriod / 2 ) );
                assertEquals( "Handler invocations should not be more than 1. " + testHandler.getHandlerInvocations(), testHandler.getHandlerInvocations() < 3, true );
                sleepFor( 200 + ( rapidFirePeriod / 2 ) );
                PriceRegeneration prcRegen2 = testHandler.getPriceRegeneration();
                assertEquals( "PriceRegen should not show in rapid fire period. " + prcRegen2.isRapidFirePeriod(), prcRegen2.isRapidFirePeriod(), false );
                assertEquals( "PriceRegen service not show in rapid fire period. " + svc.isInRapidFirePeriod( org1, org1Tp1, ccyPair ), svc.isInRapidFirePeriod( org1, org1Tp1, ccyPair ), false );
                assertEquals( "Handler invocations should be 3. " + testHandler.getHandlerInvocations(), testHandler.getHandlerInvocations() == 3, true );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail( "inside testMultiQuoteWithRapidFireTakingTradingLimitOnSameOrderWithSweep" );
        }
    }

    private double getBidTierLimit()
    {
        return 5000000;
    }

    private double getOfferTierLimit()
    {
        return 6000000;
    }

    private double getBidLimit()
    {
        return getLimit( getBidTierLimit() );
    }

    private double getOfferLimit()
    {
        return getLimit( getOfferTierLimit() );
    }

    private double getLimit( double tierLimit )
    {
        double limit = 0.0;
        for ( int index = 0; index < numberOfTiers; index++ )
        {
            limit += ( index + 1 ) * tierLimit;
        }
        return limit;
    }

    private double getTradingLimit()
    {
        return getBidLimit() < getOfferLimit() ? getBidLimit() : getOfferLimit();
    }
}
