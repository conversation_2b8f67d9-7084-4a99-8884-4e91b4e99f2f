package com.integral.finance.creditLimit.test

import com.integral.finance.businessCenter.EndOfDayServiceFactory
import com.integral.finance.config.FinanceConfigurationFactory
import com.integral.finance.config.FinanceMBean
import com.integral.finance.counterparty.LegalEntity
import com.integral.finance.creditLimit.*
import com.integral.finance.creditLimit.configuration.CreditConfigurationMBean
import com.integral.finance.creditLimit.handler.CreditUtilizationCacheSynchronizationHandlerC
import com.integral.finance.currency.Currency
import com.integral.finance.currency.CurrencyFactory
import com.integral.finance.currency.CurrencyPair
import com.integral.finance.dealing.DealingPrice
import com.integral.finance.dealing.Request
import com.integral.finance.fx.FXPaymentParameters
import com.integral.finance.fx.FXSingleLeg
import com.integral.finance.fx.FXSwap
import com.integral.finance.instrument.AmountOfInstrument
import com.integral.finance.marketData.fx.FXMarketDataSet
import com.integral.finance.price.fx.FXPrice
import com.integral.finance.trade.Tenor
import com.integral.finance.trade.Trade
import com.integral.is.ISCommonConstants
import com.integral.message.MessageStatus
import com.integral.persistence.cache.QueryServiceMock
import com.integral.persistence.cache.ReferenceDataCacheC
import com.integral.query.QueryFactory
import com.integral.query.QueryService
import com.integral.rule.Rule
import com.integral.startup.WatchPropertyC
import com.integral.system.configuration.ConfigurationProperty
import com.integral.time.DateTimeFactory
import com.integral.time.IdcDate
import com.integral.time.IdcDateTime
import com.integral.user.Organization
import com.integral.util.Tuple
import com.integral.workflow.dealing.DealingLimit
import com.integral.workflow.dealing.DealingLimitCollection
import com.integral.workflow.dealing.fx.FXDealingLimit
import com.integral.workflow.dealing.fx.FXDealingLimitCollection
import com.integral.workflow.dealing.fx.FXWorkflowFactory
import com.integral.xml.mapping.XMLMappingLoaderC
import org.apache.commons.beanutils.PropertyUtils

import java.sql.Time

// Copyright (c) 2005 Integral Development Corporation.  All Rights Reserved.

/**
 * Tests the credit utilization management operations such as creating new credit utilizations and removing obsolete
 * credit utilizations and also credit regeneration.
 *
 * <AUTHOR> Development Corp.
 */
public class CreditUtilizationManagementTest extends CreditLimitServiceBaseTestCase
{
    public void testSingleRuleCreditRevaluationWithoutNetting()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            FXMarketDataSet fxMds = ( FXMarketDataSet ) CreditUtilC.getMarketDataSet( lpOrg );
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 100000 );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 100000 );

            // first do a trade to buy 1k EUR/GBP
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate );
            log( "Credit limit before first bid trade take Credit : " + limit1 );
            double tradeAmt = 1000;
            Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURGBP, lpOrg, lpTpForDd, bidRates[0], spotDate );
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd );
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate );
            log( "Credit limit after first bid trade take Credit : " + limit2 + " success : " + cwm1.getStatus() );

            // in this case net receivable would be GBP 1k
            double usedAmt1 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "GBP", tradeAmt, true );
            assertEquals( "Trade amount taken is usedAmt=" + usedAmt1 + ",limit1=" + limit1 + ",limit2=" + limit2, true, Math.abs( limit1 - limit2 - usedAmt1 ) < MINIMUM );
            assertEquals( "Message Status=" + cwm1.getStatusName(), cwm1.getStatus().equals( MessageStatus.SUCCESS ), true );
            validateCreditUtilizationEvents( cwm1 );

            // do a trade to buy 1k CHF/JPY
            double limit3 = getAvailableCreditLimit( lpTpForDd, spotDate );
            log( "Credit limit before second bid trade take Credit : " + limit3 );
            Trade trade2 = prepareSingleLegTrade( tradeAmt, true, false, "CHF/JPY", lpOrg, lpTpForDd, bidRates[4], spotDate );
            CreditWorkflowMessage cwm2 = creditMgr.takeCredit( trade2, lpLe, ddOrg, lpTpForDd );
            double limit4 = getAvailableCreditLimit( lpTpForDd, spotDate );

            // in this case, there will be two net receivables EUR 1k and JPY 1k.
            double usedAmt2 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "JPY", tradeAmt, true );
            log( "Credit limit after second bid trade take Credit : " + limit4 + " success : " + cwm2.getStatus() + "usedAmt2=" + usedAmt2 );
            assertEquals( "Trade amount taken is ", true, Math.abs( limit3 - limit4 - usedAmt2 ) < 0.1 );
            assertEquals( "Message Status=" + cwm2.getStatusName(), cwm2.getStatus().equals( MessageStatus.SUCCESS ), true );
            CreditUtilization cu = cwm2.getCreditUtilizationEvents().iterator().next().getCreditUtilization();
            double limitAmt = cu.getAdjustedLimit();
            double ccyPosAmt = cu.getCurrencyPositions().getNetReceivableAmount( cu.getCurrency(), fxMds, new HashMap<String, FXPrice>(), false, false );
            log( "currency position net amount = " + ccyPosAmt + ",cu limit amount=" + limitAmt + ",availableLimit=" + limit4 );
            assertEquals( "ccyAmt pos amt should be used amount", Math.abs( limitAmt - ccyPosAmt - limit4 ) < MINIMUM, true );
            validateCreditUtilizationEvents( cwm2 );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testCreditLimitSubscriptions()
    {
        try
        {
            init( lpUser );
            // subscribe some currency pairs.
            Collection<String> ccyPairs = new ArrayList<String>();
            ccyPairs.add( "EUR/USD" );
            ccyPairs.add( "EUR/GBP" );
            ccyPairs.add( "USD/CHF" );
            ccyPairs.add( "CAD/JPY" );
            ccyPairs.add( "USD/CHF" );

            Collection ccyPairSet = new HashSet<String>( ccyPairs );

            CreditUtilizationManagerC.getInstance().subscribe( lpLe, lpTpForDd, ddOrg, ccyPairs );
            DealingLimitCollection subscriptions = CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().getCreditLimitSubscriptions( lpLe, lpTpForDd );
            log( "subscriptions=" + subscriptions );
            assertEquals( "set should equal to subscriptions.", ccyPairSet.size() == subscriptions.getDealingLimits().size(), true );

            int size = subscriptions.getDealingLimits().size();
            CreditUtilizationManagerC.getInstance().subscribe( lpLe, lpTpForDd, ddOrg, CurrencyFactory.getCurrency( "AED" ), CurrencyFactory.getCurrency( "AUD" ) );
            subscriptions = CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().getCreditLimitSubscriptions( lpLe, lpTpForDd );
            log( "subscriptions=" + subscriptions );
            assertEquals( "Now it should be added by one.", subscriptions.getDealingLimits().size() == size + 1, true );

            // now remove the currency pair
            CreditUtilizationManagerC.getInstance().unsubscribe( lpLe, lpTpForDd, ddOrg, CurrencyFactory.getCurrency( "AED" ), CurrencyFactory.getCurrency( "AUD" ) );
            subscriptions = CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().getCreditLimitSubscriptions( lpLe, lpTpForDd );
            log( "subscriptions=" + subscriptions );
            assertEquals( "Now it should be less by one.", subscriptions.getDealingLimits().size() == size, true );

            // now remove some currency pairs.
            Collection<String> ccyPairsRemoved = new ArrayList<String>();
            ccyPairsRemoved.add( "EUR/USD" );
            ccyPairsRemoved.add( "EUR/GBP" );
            ccyPairsRemoved.add( "BHD/INR" );
            CreditUtilizationManagerC.getInstance().unsubscribe( lpLe, lpTpForDd, ddOrg, ccyPairsRemoved );
            subscriptions = CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().getCreditLimitSubscriptions( lpLe, lpTpForDd );
            log( "subscriptions=" + subscriptions );
            assertEquals( "Now it should be less by 2.", subscriptions.getDealingLimits().size() == size - 2, true );

            // now remove all currency pairs.
            CreditUtilizationManagerC.getInstance().unsubscribe( lpLe, lpTpForDd, ddOrg, null );
            subscriptions = CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().getCreditLimitSubscriptions( lpLe, lpTpForDd );
            log( "subscriptions=" + subscriptions );
            assertEquals( "subscriptions should be null.", subscriptions == null, true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
        finally
        {
            removeCreditLimitSubscriptions( lpLe, lpTpForDd );
        }
    }

    public void testBilateralCreditLimitSubscriptions()
    {
        try
        {
            init( lpUser );
            // subscribe some currency pairs.
            Collection<String> ccyPairs = new ArrayList<String>();
            ccyPairs.add( "EUR/USD" );
            ccyPairs.add( "EUR/GBP" );
            ccyPairs.add( "USD/CHF" );
            ccyPairs.add( "CAD/JPY" );
            ccyPairs.add( "USD/CHF" );

            Collection ccyPairSet = new HashSet<String>( ccyPairs );

            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate( lpLe, fiLe, ccyPairs );
            DealingLimitCollection subscriptions = CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().getCreditLimitSubscriptions( lpLe, lpTpForFi );
            log( "subscriptions=" + subscriptions );
            assertEquals( "set should equal to subscriptions.", ccyPairSet.size() == subscriptions.getDealingLimits().size(), true );
            DealingLimitCollection subscriptions2 = CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().getCreditLimitSubscriptions( fiLe, fiTpForLp );
            log( "subscriptions2=" + subscriptions2 );
            assertEquals( "set should equal to subscriptions2.", ccyPairSet.size() == subscriptions2.getDealingLimits().size(), true );

            int size = subscriptions.getDealingLimits().size();
            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate( lpLe, fiLe, CurrencyFactory.getCurrency( "AED" ), CurrencyFactory.getCurrency( "AUD" ) );
            subscriptions = CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().getCreditLimitSubscriptions( lpLe, lpTpForFi );
            log( "subscriptions=" + subscriptions );
            assertEquals( "Now it should be added by one.", subscriptions.getDealingLimits().size() == size + 1, true );
            subscriptions2 = CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().getCreditLimitSubscriptions( fiLe, fiTpForLp );
            log( "subscriptions2=" + subscriptions2 );
            assertEquals( "Now it should be added by one.", subscriptions2.getDealingLimits().size() == size + 1, true );

            // now remove the currency pair
            CreditUtilizationManagerC.getInstance().unsubscribeBilateralCreditLimitUpdate( lpLe, fiLe, CurrencyFactory.getCurrency( "AED" ), CurrencyFactory.getCurrency( "AUD" ) );
            subscriptions = CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().getCreditLimitSubscriptions( lpLe, lpTpForFi );
            log( "subscriptions=" + subscriptions );
            assertEquals( "Now it should be less by one.", subscriptions.getDealingLimits().size() == size, true );
            subscriptions2 = CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().getCreditLimitSubscriptions( fiLe, fiTpForLp );
            log( "subscriptions2=" + subscriptions2 );
            assertEquals( "Now it should be less by one.", subscriptions2.getDealingLimits().size() == size, true );

            // now remove some currency pairs.
            Collection<String> ccyPairsRemoved = new ArrayList<String>();
            ccyPairsRemoved.add( "EUR/USD" );
            ccyPairsRemoved.add( "EUR/GBP" );
            ccyPairsRemoved.add( "BHD/INR" );
            CreditUtilizationManagerC.getInstance().unsubscribeBilateralCreditLimitUpdate( lpLe, fiLe, ccyPairsRemoved );
            subscriptions = CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().getCreditLimitSubscriptions( lpLe, lpTpForFi );
            log( "subscriptions=" + subscriptions );
            assertEquals( "Now it should be less by 2.", subscriptions.getDealingLimits().size() == size - 2, true );
            subscriptions2 = CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().getCreditLimitSubscriptions( fiLe, fiTpForLp );
            log( "subscriptions2=" + subscriptions2 );
            assertEquals( "Now it should be less by 2.", subscriptions2.getDealingLimits().size() == size - 2, true );

            // now remove all currency pairs.
            CreditUtilizationManagerC.getInstance().unsubscribeBilateralCreditLimitUpdate( lpLe, fiLe, null );
            subscriptions = CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().getCreditLimitSubscriptions( lpLe, lpTpForFi );
            log( "subscriptions=" + subscriptions );
            assertEquals( "subscriptions should be null.", subscriptions == null, true );
            subscriptions2 = CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().getCreditLimitSubscriptions( fiLe, fiTpForLp );
            log( "subscriptions2=" + subscriptions2 );
            assertEquals( "subscriptions2 should be null.", subscriptions2 == null, true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
        finally
        {
            removeCreditLimitSubscriptions( lpLe, lpTpForFi );
            removeCreditLimitSubscriptions( fiLe, fiTpForLp );
        }
    }

    public void testBilateralCreditLimitSubscriptionsWithOneOrgCreditNotSetup()
    {
        try
        {
            init( lpUser );
            // subscribe some currency pairs.
            Collection<String> ccyPairs = new ArrayList<String>();
            ccyPairs.add( "EUR/USD" );
            ccyPairs.add( "EUR/GBP" );
            ccyPairs.add( "USD/CHF" );
            ccyPairs.add( "CAD/JPY" );
            ccyPairs.add( "USD/CHF" );

            Collection ccyPairSet = new HashSet<String>( ccyPairs );

            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate( lpLe, ddLe, ccyPairs );
            DealingLimitCollection subscriptions = CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().getCreditLimitSubscriptions( lpLe, lpTpForDd );
            log( "subscriptions=" + subscriptions );
            assertEquals( "set should equal to subscriptions.", ccyPairSet.size() == subscriptions.getDealingLimits().size(), true );
            DealingLimitCollection subscriptions2 = CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().getCreditLimitSubscriptions( ddLe, ddTpForLp );
            log( "subscriptions2=" + subscriptions2 );
            assertEquals( "set should equal to subscriptions2.", ccyPairSet.size() == subscriptions2.getDealingLimits().size(), true );

            int size = subscriptions.getDealingLimits().size();
            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate( lpLe, ddLe, CurrencyFactory.getCurrency( "AED" ), CurrencyFactory.getCurrency( "AUD" ) );
            subscriptions = CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().getCreditLimitSubscriptions( lpLe, lpTpForDd );
            log( "subscriptions=" + subscriptions );
            assertEquals( "Now it should be added by one.", subscriptions.getDealingLimits().size() == size + 1, true );
            subscriptions2 = CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().getCreditLimitSubscriptions( ddLe, ddTpForLp );
            log( "subscriptions2=" + subscriptions2 );
            assertEquals( "Now it should be added by one.", subscriptions2.getDealingLimits().size() == size + 1, true );

            // now remove the currency pair
            CreditUtilizationManagerC.getInstance().unsubscribeBilateralCreditLimitUpdate( lpLe, ddLe, CurrencyFactory.getCurrency( "AED" ), CurrencyFactory.getCurrency( "AUD" ) );
            subscriptions = CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().getCreditLimitSubscriptions( lpLe, lpTpForDd );
            log( "subscriptions=" + subscriptions );
            assertEquals( "Now it should be less by one.", subscriptions.getDealingLimits().size() == size, true );
            subscriptions2 = CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().getCreditLimitSubscriptions( ddLe, ddTpForLp );
            log( "subscriptions2=" + subscriptions2 );
            assertEquals( "Now it should be less by one.", subscriptions2.getDealingLimits().size() == size, true );

            // now remove some currency pairs.
            Collection<String> ccyPairsRemoved = new ArrayList<String>();
            ccyPairsRemoved.add( "EUR/USD" );
            ccyPairsRemoved.add( "EUR/GBP" );
            ccyPairsRemoved.add( "BHD/INR" );
            CreditUtilizationManagerC.getInstance().unsubscribeBilateralCreditLimitUpdate( lpLe, ddLe, ccyPairsRemoved );
            subscriptions = CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().getCreditLimitSubscriptions( lpLe, lpTpForDd );
            log( "subscriptions=" + subscriptions );
            assertEquals( "Now it should be less by 2.", subscriptions.getDealingLimits().size() == size - 2, true );
            subscriptions2 = CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().getCreditLimitSubscriptions( ddLe, ddTpForLp );
            log( "subscriptions2=" + subscriptions2 );
            assertEquals( "Now it should be less by 2.", subscriptions2.getDealingLimits().size() == size - 2, true );

            // now remove all currency pairs.
            CreditUtilizationManagerC.getInstance().unsubscribeBilateralCreditLimitUpdate( lpLe, ddLe, null );
            subscriptions = CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().getCreditLimitSubscriptions( lpLe, lpTpForDd );
            log( "subscriptions=" + subscriptions );
            assertEquals( "subscriptions should be null.", subscriptions == null, true );
            subscriptions2 = CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().getCreditLimitSubscriptions( ddLe, ddTpForLp );
            log( "subscriptions2=" + subscriptions2 );
            assertEquals( "subscriptions2 should be null.", subscriptions2, null );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
        finally
        {
            removeCreditLimitSubscriptions( lpLe, lpTpForDd );
        }
    }

    public void testBilateralCreditLimitSubscriptionsWithOneOrgDisabled()
    {
        try
        {
            init( lpUser );
            // subscribe some currency pairs.

            // disable credit for lp org
            creditAdminSvc.setCreditEnabled( lpOrg, false );
            creditAdminSvc.setCreditEnabled( fiOrg, true );
            creditAdminSvc.setCreditEnabled( fiOrg, fiTpForLp, true );

            Collection<String> ccyPairs = new ArrayList<String>();
            ccyPairs.add( "EUR/USD" );
            ccyPairs.add( "EUR/GBP" );
            ccyPairs.add( "USD/CHF" );
            ccyPairs.add( "CAD/JPY" );
            ccyPairs.add( "USD/CHF" );

            Collection ccyPairSet = new HashSet<String>( ccyPairs );

            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate( lpLe, fiLe, ccyPairs );
            DealingLimitCollection subscriptions = CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().getCreditLimitSubscriptions( lpLe, lpTpForFi );
            log( "subscriptions=" + subscriptions );
            assertEquals( "set should equal to subscriptions.", ccyPairSet.size() == subscriptions.getDealingLimits().size(), true );
            DealingLimitCollection subscriptions2 = CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().getCreditLimitSubscriptions( fiLe, fiTpForLp );
            log( "subscriptions2=" + subscriptions2 );
            assertEquals( "set should equal to subscriptions2.", ccyPairSet.size() == subscriptions2.getDealingLimits().size(), true );

            int size = subscriptions.getDealingLimits().size();
            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate( lpLe, fiLe, CurrencyFactory.getCurrency( "AED" ), CurrencyFactory.getCurrency( "AUD" ) );
            subscriptions = CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().getCreditLimitSubscriptions( lpLe, lpTpForFi );
            log( "subscriptions=" + subscriptions );
            assertEquals( "Now it should be added by one.", subscriptions.getDealingLimits().size() == size + 1, true );
            subscriptions2 = CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().getCreditLimitSubscriptions( fiLe, fiTpForLp );
            log( "subscriptions2=" + subscriptions2 );
            assertEquals( "Now it should be added by one.", subscriptions2.getDealingLimits().size() == size + 1, true );

            // now remove the currency pair
            CreditUtilizationManagerC.getInstance().unsubscribeBilateralCreditLimitUpdate( lpLe, fiLe, CurrencyFactory.getCurrency( "AED" ), CurrencyFactory.getCurrency( "AUD" ) );
            subscriptions = CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().getCreditLimitSubscriptions( lpLe, lpTpForFi );
            log( "subscriptions=" + subscriptions );
            assertEquals( "Now it should be less by one.", subscriptions.getDealingLimits().size() == size, true );
            subscriptions2 = CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().getCreditLimitSubscriptions( fiLe, fiTpForLp );
            log( "subscriptions2=" + subscriptions2 );
            assertEquals( "Now it should be less by one.", subscriptions2.getDealingLimits().size() == size, true );

            // now remove some currency pairs.
            Collection<String> ccyPairsRemoved = new ArrayList<String>();
            ccyPairsRemoved.add( "EUR/USD" );
            ccyPairsRemoved.add( "EUR/GBP" );
            ccyPairsRemoved.add( "BHD/INR" );
            CreditUtilizationManagerC.getInstance().unsubscribeBilateralCreditLimitUpdate( lpLe, fiLe, ccyPairsRemoved );
            subscriptions = CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().getCreditLimitSubscriptions( lpLe, lpTpForFi );
            log( "subscriptions=" + subscriptions );
            assertEquals( "Now it should be less by 2.", subscriptions.getDealingLimits().size() == size - 2, true );
            subscriptions2 = CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().getCreditLimitSubscriptions( fiLe, fiTpForLp );
            log( "subscriptions2=" + subscriptions2 );
            assertEquals( "Now it should be less by 2.", subscriptions2.getDealingLimits().size() == size - 2, true );

            // now remove all currency pairs.
            CreditUtilizationManagerC.getInstance().unsubscribeBilateralCreditLimitUpdate( lpLe, fiLe, null );
            subscriptions = CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().getCreditLimitSubscriptions( lpLe, lpTpForFi );
            log( "subscriptions=" + subscriptions );
            assertEquals( "subscriptions should be null.", subscriptions == null, true );
            subscriptions2 = CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().getCreditLimitSubscriptions( fiLe, fiTpForLp );
            log( "subscriptions2=" + subscriptions2 );
            assertEquals( "subscriptions2 should be null.", subscriptions2 == null, true );

            creditAdminSvc.setCreditEnabled( lpOrg, true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
        finally
        {
            creditAdminSvc.setCreditEnabled( lpOrg, true );
            removeCreditLimitSubscriptions( lpLe, lpTpForFi );
            removeCreditLimitSubscriptions( fiLe, fiTpForLp );
        }
    }

    public void testCreditMapping()
    {
        QueryService qs = QueryFactory.getQueryService();
        QueryFactory._setQueryService( new QueryServiceMock() );
        new XMLMappingLoaderC().preload();

        try
        {
            init( lpUser );
            // subscribe some currency pairs.
            Collection<String> ccyPairs = new ArrayList<String>();
            ccyPairs.add( "EUR/USD" );
            ccyPairs.add( "EUR/GBP" );
            ccyPairs.add( "USD/CHF" );
            ccyPairs.add( "CAD/JPY" );
            ccyPairs.add( "USD/CHF" );

            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate( lpLe, ddLe, ccyPairs );
            DealingLimitCollection subscriptions = CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().getCreditLimitSubscriptions( lpLe, lpTpForDd );
            log( "subscriptions=" + subscriptions );
            Trade trade = prepareSingleLegTrade( 1000, true, true, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd );

            cwm.addError( CreditLimit.ERROR_INSUFFICIENT_RESERVE, null );
            String xml = convertToXML( cwm, CreditLimit.CREDIT_MAPPING );
            log( "xml output=" + xml );

            CreditWorkflowMessage wm = ( CreditWorkflowMessage ) convertFromXML( xml, CreditLimit.CREDIT_MAPPING );
            log( "converted object from xml message=" + wm );

            double bidLimit = 1000;
            double offerLimit = 2000;
            FXDealingLimitCollection fxDlc = FXWorkflowFactory.newFXDealingLimitCollection();
            FXDealingLimit fxDl = FXWorkflowFactory.newFXDealingLimit();
            fxDl.setBaseCurrency( CurrencyFactory.getCurrency( "USD" ) );
            fxDl.setVariableCurrency( CurrencyFactory.getCurrency( "CHF" ) );
            fxDl.setBidLimit( bidLimit );
            fxDl.setOfferLimit( offerLimit );
            fxDlc.addDealingLimit( fxDl );

            // convert to xml
            String dlcStr = convertToXML( fxDlc, CreditLimit.CREDIT_MAPPING );
            log( "xml output for dlc=" + dlcStr );

            // convert xml string to object.
            FXDealingLimitCollection dlc1 = ( FXDealingLimitCollection ) convertFromXML( dlcStr, CreditLimit.CREDIT_MAPPING );
            log( "converted object=" + dlc1 + ",dlc1=" + dlc1 );
            assertEquals( "converted dealing limit collection should not be null", dlc1 != null, true );
            FXDealingLimit fxDl1 = dlc1.getDealingLimit( CurrencyFactory.getCurrency( "USD" ), CurrencyFactory.getCurrency( "CHF" ) );
            assertEquals( "dealing limit for currency pair check", fxDl1 != null, true );
            assertEquals( "bid limit check.", fxDl1.getBidLimit(), bidLimit );
            assertEquals( "offer limit check.", fxDl1.getOfferLimit(), offerLimit );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
        finally
        {
            QueryFactory._setQueryService( qs );
            removeCreditLimitSubscriptions( lpLe, lpTpForDd );
        }
    }

    public void testAvailableCreditLimitAmountOfInstrumentUponDisablingBothMethodologies()
    {
        try
        {
            init( lpUser );
            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, null, 10000 );
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, null, 10000 );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, null, 10000 );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, null, 10000 );
            creditAdminSvc.setCreditEnabled( lpOrg, true );
            creditAdminSvc.setCreditEnabled( lpOrg, lpTpForFi, true );
            creditAdminSvc.setCreditEnabled( fiOrg, true );
            creditAdminSvc.setCreditEnabled( fiOrg, fiTpForLp, true );
            AmountOfInstrument aoi = CreditUtilizationManagerC.getInstance().getAvailableBilateralCreditLimitAmountOfInstrument( lpLe, fiLe, spotDate );
            log( "limit aoi=" + aoi );
            assertEquals( "limit aoi should be null", aoi == null, true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
        finally
        {
            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 10000 );
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 10000 );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 );
        }
    }

    public void testBilateralCreditEnabled()
    {
        try
        {
            init( lpUser );
            creditAdminSvc.setCreditEnabled( lpOrg, true );
            creditAdminSvc.setCreditEnabled( fiOrg, true );
            boolean enabled1 = CreditUtilizationManagerC.getInstance().isCreditEnabled( lpLe, fiLe );
            log( "enabled1=" + enabled1 );
            assertEquals( "enabled1 should be true. enabled1=" + enabled1, enabled1, true );

            // now set one of the relationship to false.
            creditAdminSvc.setCreditEnabled( lpOrg, false );
            boolean enabled2 = CreditUtilizationManagerC.getInstance().isCreditEnabled( lpLe, fiLe );
            log( "enabled2=" + enabled2 );
            assertEquals( "enabled2 should be true. enabled2=" + enabled2, enabled2, true );

            // now set both of the relationships to false.
            creditAdminSvc.setCreditEnabled( fiOrg, false );
            boolean enabled3 = CreditUtilizationManagerC.getInstance().isCreditEnabled( lpLe, fiLe );
            log( "enabled3=" + enabled3 );
            assertEquals( "enabled3 should be false. enabled3=" + enabled3, enabled3, false );
            creditAdminSvc.setCreditEnabled( lpOrg, true );
            creditAdminSvc.setCreditEnabled( fiOrg, true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
        finally
        {
            creditAdminSvc.setCreditEnabled( lpOrg, true );
            creditAdminSvc.setCreditEnabled( fiOrg, true );
        }
    }

    public void testAggregateCreditUtilizationQuery()
    {
        try
        {
            init( lpUser );
            creditAdminSvc.setCreditEnabled( lpOrg, true );
            creditAdminSvc.setCreditEnabled( lpOrg, lpTpForFi, true );
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            Collection<CreditUtilization> creditUtils = CreditUtilC.getAggregateCreditUtilizations( lpOrg );
            log( "creditUtils for lpOrg=" + creditUtils );
            assertEquals( "credit utils collection is not empty. size=" + creditUtils.size(), creditUtils.size() > 0, true );
            for ( CreditUtilization cu : creditUtils )
            {
                CreditLimitRule clr = cu.getCreditLimitRule();
                CounterpartyCreditLimitRule cclr = ( CounterpartyCreditLimitRule ) clr.getParentRule();
                assertEquals( "cu date should be null.", cu instanceof DailyCreditUtilization, false );
                assertEquals( "cu status is active", cu.isActive(), true );
                assertEquals( "credit limit rule active", clr.isActive(), true );
                assertEquals( "credit limit rule calc should not be null.", clr.getCreditUtilizationCalculator() != null, true );
                assertEquals( "cclr should be active.", cclr.isActive(), true );
                assertEquals( "cclr.tpOrg should be active.", cclr.getTradingPartyOrganization().isActive(), true );
            }

            CreditUtilizationCacheEntry cce = CreditUtilizationManagerC.getInstance().getAggregateCreditUtililzations( lpOrg );
            log( "cce=" + cce );
            assertEquals( "cce should be aggregate type.", cce.isAggregateCreditUtilizationCollectionCacheEntry(), true );
            assertEquals( "cce.cu size should be equal.", cce.getCreditUtilizations().size() == creditUtils.size(), true );

            // now disable credit and see the credit utilization cache entry.
            creditAdminSvc.setCreditEnabled( lpOrg, false );
            CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().removeProviderOrganizationCreditUtilizations( lpOrg );
            CreditUtilizationCacheEntry cce1 = CreditUtilizationManagerC.getInstance().getAggregateCreditUtililzations( lpOrg );
            log( "cce1=" + cce1 );
            assertEquals( "cce1 should be aggregate type.", cce1.isAggregateCreditUtilizationCollectionCacheEntry(), true );
            assertEquals( "cce1.cu size should be zero.", cce1.getCreditUtilizations().size() == 0, true );

            // now after enabling credit, add a settlement date level query.
            creditAdminSvc.setCreditEnabled( lpOrg, true );
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().removeProviderOrganizationCreditUtilizations( lpOrg );
            CreditUtilizationManagerC.getInstance().getCreditUtilizations( lpLe, fiOrg, lpTpForFi, spotDate, SUBSCRIBE_EVENT );
            Collection<CreditUtilization> providerCus = CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().getAllAggregateCreditUtilizations( true );
            Collection<CreditUtilization> allAggregates = CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().getAllAggregateCreditUtilizations( false );
            log( "providerCus.size=" + providerCus.size() + ",allAggregates.size=" + allAggregates.size() );
            assertEquals( "providerCus should be one less entry than allAggregateCus.", allAggregates.size() - providerCus.size() == 1, true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
        finally
        {
            creditAdminSvc.setCreditEnabled( lpOrg, true );
            creditAdminSvc.setCreditEnabled( lpOrg, lpTpForDd, true );
        }
    }

    public void testAggregateCreditUtilizationCacheEntry()
    {
        try
        {
            init( lpUser );
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 );
            Collection<CreditUtilization> creditUtils = CreditUtilC.getAggregateCreditUtilizations( lpOrg );
            log( "creditUtils for lpOrg=" + creditUtils );
            assertEquals( "credit utils collection is not empty. size=" + creditUtils.size(), creditUtils.size() > 0, true );
            CreditUtilizationCacheEntry cce = CreditUtilizationManagerC.getInstance().getAggregateCreditUtililzations( lpOrg );
            long createdTime = cce.getCreatedTime();
            Collection<CreditUtilization> creditUtilsFromCache = cce.getCreditUtilizations();
            log( "creditUtils from cache for lpOrg=" + creditUtilsFromCache + ",createdTime=" + createdTime );

            // get the cache entry again.
            CreditUtilizationCacheEntry cce0 = CreditUtilizationManagerC.getInstance().getAggregateCreditUtililzations( lpOrg );
            assertEquals( "cce0 should be same.", cce0.getCreatedTime() == createdTime, true );

            // now change the exposure from org level to counterparty for an org
            boolean isOrg = CreditUtilC.isOrgLevelCredit( lpOrg, fiOrg );
            if ( !isOrg )
            {
                creditAdminSvc.setOrganizationExposureLevel( lpOrg, fiOrg );
            }
            else
            {
                creditAdminSvc.setLegalEntityExposureLevel( lpOrg, fiOrg );
            }
            CreditUtilizationCacheEntry cce1 = CreditUtilizationManagerC.getInstance().getAggregateCreditUtililzations( lpOrg );

            //check the created time from the latest cache entry. This should be a totally new cache entry.
            log( "After admin action, creditUtils from cache for lpOrg=" + cce1.getCreditUtilizations() + ",createdTime=" + cce1.getCreatedTime() + ",modified=" + cce1.getModifiedTime() );
            assertEquals( "cce1 should be new.", cce1.getCreatedTime() > createdTime, true );

            // now change the credit limit
            double currentLimit = creditAdminSvc.getCreditLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION );
            Currency limitCcy = creditAdminSvc.getCreditLimitCurrency( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION );
            creditAdminSvc.setCreditLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, currentLimit + 100, limitCcy );
            CreditUtilizationCacheEntry cce2 = CreditUtilizationManagerC.getInstance().getAggregateCreditUtililzations( lpOrg );

            //check the modified time from the cache entry. It should be the same as before.
            log( "After admin limit setting, creditUtils from cache for lpOrg=" + cce2.getCreditUtilizations() + ",createdTime=" + cce2.getCreatedTime() + ",modified=" + cce2.getModifiedTime() );
            long lastModifiedTime = cce2.getModifiedTime();
            assertEquals( "cce2 should be modified.", lastModifiedTime == cce2.getCreatedTime(), true );

            // now change the credit limit on a date. it should be the same as before.
            double currentLimit1 = creditAdminSvc.getCreditLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, tradeDate );
            creditAdminSvc.setCreditLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, currentLimit1 + 100, limitCcy, tradeDate );
            CreditUtilizationCacheEntry cce3 = CreditUtilizationManagerC.getInstance().getAggregateCreditUtililzations( lpOrg );

            //check the modified time from the latest cache entry. It should be same cache entry before.
            log( "After admin limit on date setting, creditUtils from cache for lpOrg=" + cce3.getCreditUtilizations() + ",createdTime=" + cce3.getCreatedTime() + ",modified=" + cce3.getModifiedTime() );
            assertEquals( "cce3 should be new.", cce3.getModifiedTime() == lastModifiedTime, true );
            lastModifiedTime = cce3.getModifiedTime();

            // now change the suspension percentage at org level. It should be same cache entry before.
            double suspensionPercent = CreditUtilC.getCreditLimitRuleSet( lpOrg ).getSuspensionPercentage();
            creditAdminSvc.setSuspensionPercentage( lpOrg, suspensionPercent + 0.05 );
            CreditUtilizationCacheEntry cce4 = CreditUtilizationManagerC.getInstance().getAggregateCreditUtililzations( lpOrg );

            //check the modified time from the latest cache entry.
            log( "After admin org level suspension percentage setting, creditUtils from cache for lpOrg=" + cce4.getCreditUtilizations() + ",createdTime=" + cce4.getCreatedTime() + ",modified=" + cce4.getModifiedTime() );
            assertEquals( "cce4 should be new.", cce4.getModifiedTime() == lastModifiedTime, true );
            lastModifiedTime = cce4.getModifiedTime();

            // now change the suspension percentage at cpty level
            Double suspensionPercent1 = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTpForDd ).getSuspensionPercentage();
            creditAdminSvc.setSuspensionPercentage( lpOrg, lpTpForDd, ( suspensionPercent1 != null ? suspensionPercent1 : 90 ) + 0.05 );
            CreditUtilizationCacheEntry cce5 = CreditUtilizationManagerC.getInstance().getAggregateCreditUtililzations( lpOrg );

            //check the created time from the latest cache entry. It should be same cache entry before.
            log( "After admin cpty level suspension percentage setting, creditUtils from cache for lpOrg=" + cce5.getCreditUtilizations() + ",createdTime=" + cce5.getCreatedTime() + ",modified=" + cce5.getModifiedTime() );
            assertEquals( "cce5 should be new.", cce5.getModifiedTime() == lastModifiedTime, true );
            lastModifiedTime = cce5.getModifiedTime();
            sleepFor( 100L );

            // now change the leverage factor at org level. cache entry should be modified.
            Double leverage = creditAdminSvc.getDefaultLeverageFactor( lpOrg );
            creditAdminSvc.setDefaultLeverageFactor( lpOrg, ( leverage != null ? leverage : 90 ) + 0.05 );
            CreditUtilizationCacheEntry cce6 = CreditUtilizationManagerC.getInstance().getAggregateCreditUtililzations( lpOrg );

            //check the modified time from the latest cache entry.
            log( "After admin org level leverage factor setting, creditUtils from cache for lpOrg=" + cce6.getCreditUtilizations() + ",createdTime=" + cce6.getCreatedTime() + ",modified=" + cce6.getModifiedTime() );
            assertEquals( "cce6 should be new.", cce6.getModifiedTime() > lastModifiedTime, true );
            lastModifiedTime = cce6.getModifiedTime();
            sleepFor( 100L );

            // now change the leverage factor at cpty level
            Double leverage1 = creditAdminSvc.getLeverageFactor( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION );
            creditAdminSvc.setLeverageFactor( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, ( leverage1 != null ? leverage1 : 90 ) + 0.05 );
            CreditUtilizationCacheEntry cce7 = CreditUtilizationManagerC.getInstance().getAggregateCreditUtililzations( lpOrg );

            //check the modified time from the latest cache entry. Cache entry should be modified.
            sleepFor( 100L );
            log( "After admin cpty level leverage factor setting, creditUtils from cache for lpOrg=" + cce7.getCreditUtilizations() + ",createdTime=" + cce7.getCreatedTime() + ",modified=" + cce7.getModifiedTime() );
            assertEquals( "cce7 should be new.", cce7.getModifiedTime() > lastModifiedTime, true );
            lastModifiedTime = cce7.getModifiedTime();
            sleepFor( 100L );

            // now change the applyPandL at org level
            Boolean applyPandL = creditAdminSvc.isDefaultApplyPandL( lpOrg );
            creditAdminSvc.setDefaultApplyPandL( lpOrg, ( applyPandL != null ? !applyPandL : true ) );
            CreditUtilizationCacheEntry cce8 = CreditUtilizationManagerC.getInstance().getAggregateCreditUtililzations( lpOrg );

            //check the modified time from the latest cache entry. Cache entry should be modified.
            assertEquals( "cce8 should be new.", cce8.getModifiedTime() > lastModifiedTime, true );
            lastModifiedTime = cce8.getModifiedTime();
            sleepFor( 100L );

            // now change the applyPandL at cpty level. Cache entry should be modified.
            Boolean applyPandL1 = creditAdminSvc.isApplyPandL( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION );
            creditAdminSvc.setApplyPandL( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, ( applyPandL1 != null ? !applyPandL1 : true ) );
            CreditUtilizationCacheEntry cce9 = CreditUtilizationManagerC.getInstance().getAggregateCreditUtililzations( lpOrg );

            //check the modified time from the latest cache entry.
            log( "After admin cpty level applyPandL setting, creditUtils from cache for lpOrg=" + cce9.getCreditUtilizations() + ",createdTime=" + cce9.getCreatedTime() + ",modified=" + cce9.getModifiedTime() );
            assertEquals( "cce9 should be new.", cce9.getModifiedTime() > lastModifiedTime, true );
            lastModifiedTime = cce9.getModifiedTime();
            sleepFor( 100L );

            // now change the ignoreValueDate at org level. Cache entry should be modified.
            Boolean ignoreCurrDatePos = creditAdminSvc.isDefaultIgnoreCurrDatePositions( lpOrg );
            creditAdminSvc.setDefaultIgnoreCurrDatePositions( lpOrg, ( ignoreCurrDatePos != null ? !ignoreCurrDatePos : true ) );
            CreditUtilizationCacheEntry cce12 = CreditUtilizationManagerC.getInstance().getAggregateCreditUtililzations( lpOrg );

            //check the modified time from the latest cache entry.
            log( "After admin org level ignoreValueDate setting, creditUtils from cache for lpOrg=" + cce8.getCreditUtilizations() + ",createdTime=" + cce12.getCreatedTime() + ",modified=" + cce12.getModifiedTime() );
            assertEquals( "cce12 should be new.", cce12.getModifiedTime() > lastModifiedTime, true );
            lastModifiedTime = cce12.getModifiedTime();
            sleepFor( 100L );

            // now change the ignoreValueDate at cpty level. Cache entry should be modified.
            Boolean ignoreCurrDatePos1 = creditAdminSvc.isIgnoreCurrDatePositions( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION );
            creditAdminSvc.setIgnoreCurrDatePositions( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, ( ignoreCurrDatePos1 != null ? !ignoreCurrDatePos1 : true ) );
            CreditUtilizationCacheEntry cce13 = CreditUtilizationManagerC.getInstance().getAggregateCreditUtililzations( lpOrg );

            //check the modified time from the latest cache entry.
            log( "After admin cpty level ignoreValueDate setting, creditUtils from cache for lpOrg=" + cce13.getCreditUtilizations() + ",createdTime=" + cce13.getCreatedTime() + ",modified=" + cce13.getModifiedTime() );
            assertEquals( "cce13 should be new.", cce13.getModifiedTime() > lastModifiedTime, true );
            lastModifiedTime = cce13.getModifiedTime();
            sleepFor( 100L );

            // now change the enabled at org level. Cache entry should be same.
            boolean enabled = CreditUtilC.getCreditLimitRuleSet( lpOrg ).isEnabled();
            creditAdminSvc.setCreditEnabled( lpOrg, !enabled );
            CreditUtilizationCacheEntry cce10 = CreditUtilizationManagerC.getInstance().getAggregateCreditUtililzations( lpOrg );

            //now change the enabled at cpty level. Cache entry should be modified.
            log( "After admin org level enabled setting, creditUtils from cache for lpOrg=" + cce10.getCreditUtilizations() + ",createdTime=" + cce10.getCreatedTime() + ",modified=" + cce10.getModifiedTime() );
            assertEquals( "cce10 should be new.", cce10.getModifiedTime() > lastModifiedTime, true );
            lastModifiedTime = cce10.getModifiedTime();
            sleepFor( 100L );

            // now change credit enabled flag. Cache entry should be same.
            boolean enabled1 = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTpForDd ).isEnabled();
            creditAdminSvc.setCreditEnabled( lpOrg, lpTpForDd, !enabled1 );
            CreditUtilizationCacheEntry cce11 = CreditUtilizationManagerC.getInstance().getAggregateCreditUtililzations( lpOrg );

            //check the modified time from the latest cache entry.
            log( "After admin cpty level enabled setting, creditUtils from cache for lpOrg=" + cce11.getCreditUtilizations() + ",createdTime=" + cce11.getCreatedTime() + ",modified=" + cce11.getModifiedTime() );
            assertEquals( "cce11 should be new.", cce11.getModifiedTime() > lastModifiedTime, true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
        finally
        {
            creditAdminSvc.setCreditEnabled( lpOrg, true );
            creditAdminSvc.setCreditEnabled( lpOrg, lpTpForDd, true );
        }
    }

    public void testRevaluationPerformance()
    {
        try
        {
            Organization[] orgs = ( Collection<Organization> ) ReferenceDataCacheC.getInstance().getOrgs();
            int orgSize = 0;
            for ( Organization org : orgs )
            {
                if ( CreditUtilC.getCreditLimitRuleSet( org ) != null )
                {
                    orgSize++;
                    CreditUtilizationManagerC.getInstance().getAggregateCreditUtililzations( org );
                }
            }
            sleepFor( 6000 );
            log( "credit provider org size=" + orgSize );
            sleepFor( 30 );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testCreditUtilizationRealtime()
    {
        try
        {
            Collection<CreditUtilization> creditUtils = CreditUtilizationManagerC.getInstance().getCreditUtilizations( lpLe, ddOrg, lpTpForDd, spotDate, REVAL_CREDIT_EVENT );
            CreditUtilization cu = creditUtils.iterator().next();
            RealtimeCreditUtilization realtimeCu = cu.getRealtimeCreditUtilization();
            realtimeCu.recalculate();
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testSubscriptionKeyDelimiter()
    {
        String lpLeName = lpLe.getShortName();
        try
        {
            lpLe.setShortName( lpLe.getShortName() + "_XXX" );
            Currency eur = CurrencyFactory.getCurrency( "EUR" );
            Currency usd = CurrencyFactory.getCurrency( "USD" );
            CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().addCreditLimitSubscription( lpLe, lpTpForFi, fiOrg, eur, usd );
            new CreditUtilizationCacheFetchFromSubscriptionsC().fetchCreditUtilizations( lpOrg, fiOrg );
            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTpForFi );
            CreditUtilizationCacheEntry cce = CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().getCreditUtilizationCacheEntry( lpLe, fiOrg, cclr.getTradingParty(), CreditUtilC.getSpotDate( eur, usd ) );
            assertNotNull( cce );
        }
        catch ( Exception e )
        {
            fail( "testSubscriptionKeyDelimiter", e );
        }
        finally
        {
            lpLe.setShortName( lpLeName );
        }
    }

    public void testCreditUtilizationUpdatesOnUnilateralCreditTakeUndo()
    {
        try
        {
            init( fiUser );
            creditMgr.setSendCreditUtilizationUpdates( true );
            Collection<CreditUtilizationCalculator> aggCalcs = CreditUtilC.getSupportedNonAccountAggregateNettingMethodologies();
            Collection<CreditUtilizationCalculator> dailyCalcs = CreditUtilC.getSupportedDailyNettingMethodologies();

            for ( CreditUtilizationCalculator aggCalc : aggCalcs )
            {
                for ( CreditUtilizationCalculator dailyCalc : dailyCalcs )
                {
                    removeExistingCreditUtilizationEvents( lpOrg );
                    setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, ********* );
                    setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, ********* );

                    double tradeAmt = 1000;
                    Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURGBP, lpOrg, lpTpForDd, bidRates[0], spotDate );
                    trade1.setTransactionID( "FXI" + System.nanoTime() );
                    CreditWorkflowMessage cwm = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd, true, true, true, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
                    Collection<CreditUtilizationEvent> cues = cwm.getCreditUtilizationEvents();
                    Map<Long, Tuple<CurrencyPositionCollection, CurrencyPositionCollection>> cuCcyPosMap = new HashMap<Long, Tuple<CurrencyPositionCollection, CurrencyPositionCollection>>();
                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        CurrencyPositionCollection cps = cu.getCurrencyPositions();
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> cpcTuple = new Tuple<CurrencyPositionCollection, CurrencyPositionCollection>( cps, cps.copy( false ) );
                        cuCcyPosMap.put( cu.getObjectID(), cpcTuple );
                    }

                    sleepFor( 1000 );

                    CreditWorkflowMessage replyMsg = ( CreditWorkflowMessage ) cwm.getReplyMessage();
                    String eventSnapshot = ( String ) replyMsg.getParameterValue( CREDIT_UTIL_EVENT_SNAPSHOT_PROPERTY );
                    String event = replyMsg.getEventName();
                    IdcDate tradeDate = replyMsg.getTradeDate();

                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        ( ( CreditUtilizationC ) cu ).setCurrencyPositions( cuCcyPosMap.get( cu.getObjectID() ).second );
                    }

                    // now apply the events from the snapshot.
                    CreditUtilizationCacheSynchronizationHandlerC.applyUtilizationEvents( event, eventSnapshot, trade1.getTransactionID(), tradeDate, true );

                    // now verify that the newly applied currency positions and original trade applied currency positions should match.

                    for ( Long cuId : cuCcyPosMap.keySet() )
                    {
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> tpl = cuCcyPosMap.get( cuId );
                        assertTrue( tpl.first.isCurrencyPositionsMatch( tpl.second ) );
                    }

                    // now undo credit.
                    CreditWorkflowMessage cwm1 = creditMgr.undoCredit( trade1, lpLe, ddOrg, lpTpForDd );

                    sleepFor( 1000 );

                    CreditWorkflowMessage replyMsg1 = ( CreditWorkflowMessage ) cwm1.getReplyMessage();
                    String eventSnapshot1 = ( String ) replyMsg1.getParameterValue( CREDIT_UTIL_EVENT_SNAPSHOT_PROPERTY );
                    String event1 = replyMsg1.getEventName();
                    IdcDate tradeDate1 = replyMsg1.getTradeDate();

                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        ( ( CreditUtilizationC ) cu ).setCurrencyPositions( cuCcyPosMap.get( cu.getObjectID() ).first );
                    }

                    // now apply the events from the snapshot.
                    CreditUtilizationCacheSynchronizationHandlerC.applyUtilizationEvents( event1, eventSnapshot1, trade1.getTransactionID(), tradeDate1, true );

                    // now verify that the newly applied currency positions and original trade applied currency positions should match.

                    for ( Long cuId : cuCcyPosMap.keySet() )
                    {
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> tpl = cuCcyPosMap.get( cuId );
                        slog.warn( "tpl.first=" + tpl.first + ",tpl.second=" + tpl.second + ",dailyCalc=" + dailyCalc + ",aggCalc=" + aggCalc );
                        assertTrue( tpl.first.isCurrencyPositionsMatch( tpl.second ) );
                    }
                    CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().removeAllCreditUtilizations();
                    slog.warn( "Finished daily calc=" + dailyCalc + ",aggCalc=" + aggCalc );
                    sleepFor( 2000 );
                }
            }
        }
        catch ( Exception e )
        {
            fail( "testCreditUtilizationUpdatesOnUnilateralCreditTakeUndo", e );
        }
        finally
        {
            creditMgr.setSendCreditUtilizationUpdates( false );
        }
    }

    public void testCreditUtilizationUpdatesOnUnilateralCreditTakeUpdateAmount()
    {
        try
        {
            init( fiUser );
            creditMgr.setSendCreditUtilizationUpdates( true );
            Collection<CreditUtilizationCalculator> aggCalcs = CreditUtilC.getSupportedNonAccountAggregateNettingMethodologies();
            Collection<CreditUtilizationCalculator> dailyCalcs = CreditUtilC.getSupportedDailyNettingMethodologies();

            for ( CreditUtilizationCalculator aggCalc : aggCalcs )
            {
                for ( CreditUtilizationCalculator dailyCalc : dailyCalcs )
                {
                    removeExistingCreditUtilizationEvents( lpOrg );
                    CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().removeAllCreditUtilizations();
                    setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, ********* );
                    setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, ********* );

                    double tradeAmt = 1000;
                    Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURGBP, lpOrg, lpTpForDd, bidRates[0], spotDate );
                    trade1.setTransactionID( "FXI" + System.nanoTime() );
                    CreditWorkflowMessage cwm = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd, true, true, true, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
                    Collection<CreditUtilizationEvent> cues = cwm.getCreditUtilizationEvents();
                    Map<Long, Tuple<CurrencyPositionCollection, CurrencyPositionCollection>> cuCcyPosMap = new HashMap<Long, Tuple<CurrencyPositionCollection, CurrencyPositionCollection>>();
                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        CurrencyPositionCollection cps = cu.getCurrencyPositions();
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> cpcTuple = new Tuple<CurrencyPositionCollection, CurrencyPositionCollection>( cps, cps.copy( false ) );
                        cuCcyPosMap.put( cu.getObjectID(), cpcTuple );
                    }

                    sleepFor( 1000 );

                    CreditWorkflowMessage replyMsg = ( CreditWorkflowMessage ) cwm.getReplyMessage();
                    String eventSnapshot = ( String ) replyMsg.getParameterValue( CREDIT_UTIL_EVENT_SNAPSHOT_PROPERTY );
                    String event = replyMsg.getEventName();
                    IdcDate tradeDate = replyMsg.getTradeDate();

                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        ( ( CreditUtilizationC ) cu ).setCurrencyPositions( cuCcyPosMap.get( cu.getObjectID() ).second );
                    }

                    // now apply the events from the snapshot.
                    CreditUtilizationCacheSynchronizationHandlerC.applyUtilizationEvents( event, eventSnapshot, trade1.getTransactionID(), tradeDate, true );

                    // now verify that the newly applied currency positions and original trade applied currency positions should match.

                    for ( Long cuId : cuCcyPosMap.keySet() )
                    {
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> tpl = cuCcyPosMap.get( cuId );
                        slog.warn( "tpl.first=" + tpl.first + ",tpl.second=" + tpl.second + ",dailyCalc=" + dailyCalc + ",aggCalc=" + aggCalc );
                        assertTrue( tpl.first.isCurrencyPositionsMatch( tpl.second ) );
                    }

                    // now update credit.
                    FXPaymentParameters fxPmt = ( ( FXSingleLeg ) trade1 ).getFXLeg().getFXPayment();
                    fxPmt.setCurrency1Amount( fxPmt.getCurrency1Amount() / 2.0 );
                    fxPmt.setCurrency2Amount( fxPmt.getCurrency2Amount() / 2.0 );

                    CreditWorkflowMessage cwm1 = creditMgr.updateCreditUtilizationAmount( trade1, lpLe, lpTpForDd );

                    sleepFor( 1000 );

                    CreditWorkflowMessage replyMsg1 = ( CreditWorkflowMessage ) cwm1.getReplyMessage();
                    String eventSnapshot1 = ( String ) replyMsg1.getParameterValue( CREDIT_UTIL_EVENT_SNAPSHOT_PROPERTY );
                    String event1 = replyMsg1.getEventName();
                    IdcDate tradeDate1 = replyMsg1.getTradeDate();

                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        ( ( CreditUtilizationC ) cu ).setCurrencyPositions( cuCcyPosMap.get( cu.getObjectID() ).first );
                    }

                    // now apply the events from the snapshot.
                    CreditUtilizationCacheSynchronizationHandlerC.applyUtilizationEvents( event1, eventSnapshot1, trade1.getTransactionID(), tradeDate1, true );

                    // now verify that the newly applied currency positions and original trade applied currency positions should match.

                    for ( Long cuId : cuCcyPosMap.keySet() )
                    {
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> tpl = cuCcyPosMap.get( cuId );
                        slog.warn( "tpl.first=" + tpl.first + ",tpl.second=" + tpl.second + ",dailyCalc=" + dailyCalc + ",aggCalc=" + aggCalc );
                        assertTrue( tpl.first.isCurrencyPositionsMatch( tpl.second ) );
                    }
                    slog.warn( "Finished daily calc=" + dailyCalc + ",aggCalc=" + aggCalc );
                    sleepFor( 2000 );
                }
            }
        }
        catch ( Exception e )
        {
            fail( "testCreditUtilizationUpdatesOnUnilateralCreditTakeUpdateAmount", e );
        }
        finally
        {
            creditMgr.setSendCreditUtilizationUpdates( false );
        }
    }

    public void testCreditUtilizationUpdatesOnBilateralTakeUpdateAmount()
    {
        try
        {
            init( fiUser );
            creditMgr.setSendCreditUtilizationUpdates( true );
            Collection<CreditUtilizationCalculator> aggCalcs = CreditUtilC.getSupportedNonAccountAggregateNettingMethodologies();
            Collection<CreditUtilizationCalculator> dailyCalcs = CreditUtilC.getSupportedDailyNettingMethodologies();

            for ( CreditUtilizationCalculator aggCalc : aggCalcs )
            {
                for ( CreditUtilizationCalculator dailyCalc : dailyCalcs )
                {
                    removeExistingCreditUtilizationEvents( fiOrg );
                    removeExistingCreditUtilizationEvents( lpOrg );
                    CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().removeAllCreditUtilizations();
                    setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, ********* );
                    setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, ********* );
                    setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, ********* );
                    setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, ********* );

                    double tradeAmt = 1000;
                    Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURGBP, lpOrg, lpTpForFi, bidRates[0], spotDate );
                    trade1.setTransactionID( "FXI" + System.nanoTime() );
                    CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( lpLe, fiLe, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
                    Collection<CreditUtilizationEvent> cues = cwm.getCreditUtilizationEvents();
                    Map<Long, Tuple<CurrencyPositionCollection, CurrencyPositionCollection>> cuCcyPosMap = new HashMap<Long, Tuple<CurrencyPositionCollection, CurrencyPositionCollection>>();
                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        CurrencyPositionCollection cps = cu.getCurrencyPositions();
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> cpcTuple = new Tuple<CurrencyPositionCollection, CurrencyPositionCollection>( cps, cps.copy( false ) );
                        cuCcyPosMap.put( cu.getObjectID(), cpcTuple );
                    }

                    sleepFor( 1000 );

                    CreditWorkflowMessage replyMsg = ( CreditWorkflowMessage ) cwm.getReplyMessage();
                    String eventSnapshot = ( String ) replyMsg.getParameterValue( CREDIT_UTIL_EVENT_SNAPSHOT_PROPERTY );
                    String event = replyMsg.getEventName();
                    IdcDate tradeDate = replyMsg.getTradeDate();

                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        ( ( CreditUtilizationC ) cu ).setCurrencyPositions( cuCcyPosMap.get( cu.getObjectID() ).second );
                    }

                    // now apply the events from the snapshot.
                    CreditUtilizationCacheSynchronizationHandlerC.applyUtilizationEvents( event, eventSnapshot, trade1.getTransactionID(), tradeDate, true );

                    // now verify that the newly applied currency positions and original trade applied currency positions should match.

                    for ( Long cuId : cuCcyPosMap.keySet() )
                    {
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> tpl = cuCcyPosMap.get( cuId );
                        slog.warn( "tpl.first=" + tpl.first + ",tpl.second=" + tpl.second + ",dailyCalc=" + dailyCalc + ",aggCalc=" + aggCalc );
                        assertTrue( tpl.first.isCurrencyPositionsMatch( tpl.second ) );
                    }

                    // now update credit.
                    FXPaymentParameters fxPmt = ( ( FXSingleLeg ) trade1 ).getFXLeg().getFXPayment();
                    fxPmt.setCurrency1Amount( fxPmt.getCurrency1Amount() / 2.0 );
                    fxPmt.setCurrency2Amount( fxPmt.getCurrency2Amount() / 2.0 );

                    CreditWorkflowMessage cwm1 = creditMgr.updateBilateralCreditUtilizationAmount( lpLe, fiLe, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );

                    sleepFor( 1000 );

                    CreditWorkflowMessage replyMsg1 = ( CreditWorkflowMessage ) cwm1.getReplyMessage();
                    String eventSnapshot1 = ( String ) replyMsg1.getParameterValue( CREDIT_UTIL_EVENT_SNAPSHOT_PROPERTY );
                    String event1 = replyMsg1.getEventName();
                    IdcDate tradeDate1 = replyMsg1.getTradeDate();

                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        ( ( CreditUtilizationC ) cu ).setCurrencyPositions( cuCcyPosMap.get( cu.getObjectID() ).first );
                    }

                    // now apply the events from the snapshot.
                    CreditUtilizationCacheSynchronizationHandlerC.applyUtilizationEvents( event1, eventSnapshot1, trade1.getTransactionID(), tradeDate1, true );

                    // now verify that the newly applied currency positions and original trade applied currency positions should match.

                    for ( Long cuId : cuCcyPosMap.keySet() )
                    {
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> tpl = cuCcyPosMap.get( cuId );
                        slog.warn( "tpl.first=" + tpl.first + ",tpl.second=" + tpl.second + ",dailyCalc=" + dailyCalc + ",aggCalc=" + aggCalc );
                        //tod assertTrue( tpl.first.isCurrencyPositionsMatch( tpl.second ) );
                    }
                    slog.warn( "Finished daily calc=" + dailyCalc + ",aggCalc=" + aggCalc );
                    sleepFor( 2000 );
                }
            }
        }
        catch ( Exception e )
        {
            fail( "testCreditUtilizationUpdatesOnBilateralTakeUpdateAmount", e );
        }
        finally
        {
            creditMgr.setSendCreditUtilizationUpdates( false );
        }
    }

    public void testCreditUtilizationUpdatesOnBilateralTakeAmendCpty()
    {
        try
        {
            init( fiUser );
            creditMgr.setSendCreditUtilizationUpdates( true );

            Collection<CreditUtilizationCalculator> aggCalcs = CreditUtilC.getSupportedNonAccountAggregateNettingMethodologies();
            Collection<CreditUtilizationCalculator> dailyCalcs = CreditUtilC.getSupportedDailyNettingMethodologies();

            for ( CreditUtilizationCalculator aggCalc : aggCalcs )
            {
                for ( CreditUtilizationCalculator dailyCalc : dailyCalcs )
                {
                    removeExistingCreditUtilizationEvents( fiOrg );
                    removeExistingCreditUtilizationEvents( lpOrg );
                    CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().removeAllCreditUtilizations();
                    setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, ********* );
                    setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, ********* );
                    setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, ********* );
                    setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, ********* );

                    double tradeAmt = 1000;
                    Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURGBP, lpOrg, lpTpForFi, bidRates[0], spotDate );
                    trade1.setTransactionID( "FXI" + System.nanoTime() );
                    CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( lpLe, fiLe, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
                    Collection<CreditUtilizationEvent> cues = cwm.getCreditUtilizationEventsForNotification();
                    Map<Long, Tuple<CurrencyPositionCollection, CurrencyPositionCollection>> cuCcyPosMap = new HashMap<Long, Tuple<CurrencyPositionCollection, CurrencyPositionCollection>>();
                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        CurrencyPositionCollection cps = cu.getCurrencyPositions();
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> cpcTuple = new Tuple<CurrencyPositionCollection, CurrencyPositionCollection>( cps, cps.copy( false ) );
                        cuCcyPosMap.put( cu.getObjectID(), cpcTuple );
                    }

                    sleepFor( 1000 );

                    CreditWorkflowMessage replyMsg = ( CreditWorkflowMessage ) cwm.getReplyMessage();
                    String eventSnapshot = ( String ) replyMsg.getParameterValue( CREDIT_UTIL_EVENT_SNAPSHOT_PROPERTY );
                    String event = replyMsg.getEventName();
                    IdcDate tradeDate = replyMsg.getTradeDate();

                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        ( ( CreditUtilizationC ) cu ).setCurrencyPositions( cuCcyPosMap.get( cu.getObjectID() ).second );
                    }

                    // now apply the events from the snapshot.
                    CreditUtilizationCacheSynchronizationHandlerC.applyUtilizationEvents( event, eventSnapshot, trade1.getTransactionID(), tradeDate, true );

                    // now verify that the newly applied currency positions and original trade applied currency positions should match.

                    for ( Long cuId : cuCcyPosMap.keySet() )
                    {
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> tpl = cuCcyPosMap.get( cuId );
                        slog.warn( "tpl.first=" + tpl.first + ",tpl.second=" + tpl.second + ",dailyCalc=" + dailyCalc + ",aggCalc=" + aggCalc );
                        assertTrue( tpl.first.isCurrencyPositionsMatch( tpl.second ) );
                    }

                    // now undo credit.
                    CreditWorkflowMessage cwm1 = creditMgr.undoBilateralCredit( lpLe, fiLe, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );

                    sleepFor( 1000 );

                    CreditWorkflowMessage replyMsg1 = ( CreditWorkflowMessage ) cwm1.getReplyMessage();
                    String eventSnapshot1 = ( String ) replyMsg1.getParameterValue( CREDIT_UTIL_EVENT_SNAPSHOT_PROPERTY );
                    String event1 = replyMsg1.getEventName();
                    IdcDate tradeDate1 = replyMsg1.getTradeDate();

                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        ( ( CreditUtilizationC ) cu ).setCurrencyPositions( cuCcyPosMap.get( cu.getObjectID() ).first );
                    }

                    // now apply the events from the snapshot.
                    CreditUtilizationCacheSynchronizationHandlerC.applyUtilizationEvents( event1, eventSnapshot1, trade1.getTransactionID(), tradeDate1, true );

                    // now verify that the newly applied currency positions and original trade applied currency positions should match.

                    for ( Long cuId : cuCcyPosMap.keySet() )
                    {
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> tpl = cuCcyPosMap.get( cuId );
                        slog.warn( "tpl.first=" + tpl.first + ",tpl.second=" + tpl.second + ",dailyCalc=" + dailyCalc + ",aggCalc=" + aggCalc );
                        assertTrue( tpl.first.isCurrencyPositionsMatch( tpl.second ) );
                    }

                    // now book the trade in a different cpty
                    LegalEntity newLPLe = lpLe2;
                    trade1.setCounterpartyB( newLPLe );
                    CreditWorkflowMessage cwm2 = creditMgr.takeBilateralCredit( newLPLe, fiLe, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );

                    sleepFor( 1000 );

                    CreditWorkflowMessage replyMsg2 = ( CreditWorkflowMessage ) cwm2.getReplyMessage();
                    String eventSnapshot2 = ( String ) replyMsg2.getParameterValue( CREDIT_UTIL_EVENT_SNAPSHOT_PROPERTY );
                    String event2 = replyMsg2.getEventName();
                    IdcDate tradeDate2 = replyMsg2.getTradeDate();

                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        ( ( CreditUtilizationC ) cu ).setCurrencyPositions( cuCcyPosMap.get( cu.getObjectID() ).second );
                    }

                    // now apply the events from the snapshot.
                    CreditUtilizationCacheSynchronizationHandlerC.applyUtilizationEvents( event2, eventSnapshot2, trade1.getTransactionID(), tradeDate2, true );

                    // now verify that the newly applied currency positions and original trade applied currency positions should match.

                    for ( Long cuId : cuCcyPosMap.keySet() )
                    {
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> tpl = cuCcyPosMap.get( cuId );
                        slog.warn( "tpl.first=" + tpl.first + ",tpl.second=" + tpl.second + ",dailyCalc=" + dailyCalc + ",aggCalc=" + aggCalc );
                        assertTrue( tpl.first.isCurrencyPositionsMatch( tpl.second ) );
                    }

                    slog.warn( "Finished daily calc=" + dailyCalc + ",aggCalc=" + aggCalc );
                    sleepFor( 2000 );
                }
            }
        }
        catch ( Exception e )
        {
            fail( "testCreditUtilizationUpdatesOnBilateralTakeAmendCpty", e );
        }
        finally
        {
            creditMgr.setSendCreditUtilizationUpdates( false );
        }
    }

    public void testCreditUtilizationUpdatesOnBilateralTakeMultiFillFullFill()
    {
        try
        {
            init( fiUser );
            creditMgr.setSendCreditUtilizationUpdates( true );

            Collection<CreditUtilizationCalculator> aggCalcs = CreditUtilC.getSupportedNonAccountAggregateNettingMethodologies();
            Collection<CreditUtilizationCalculator> dailyCalcs = CreditUtilC.getSupportedDailyNettingMethodologies();

            for ( CreditUtilizationCalculator aggCalc : aggCalcs )
            {
                for ( CreditUtilizationCalculator dailyCalc : dailyCalcs )
                {
                    removeExistingCreditUtilizationEvents( fiOrg );
                    removeExistingCreditUtilizationEvents( lpOrg );
                    CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().removeAllCreditUtilizations();
                    setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, ********* );
                    setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, ********* );
                    setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, ********* );
                    setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, ********* );

                    double tradeAmt = 1000;
                    Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURGBP, lpOrg, lpTpForFi, bidRates[0], spotDate );
                    trade1.setTransactionID( "FXI" + System.nanoTime() );
                    CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( lpLe, fiLe, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
                    Collection<CreditUtilizationEvent> cues = cwm.getCreditUtilizationEventsForNotification();
                    Map<Long, Tuple<CurrencyPositionCollection, CurrencyPositionCollection>> cuCcyPosMap = new HashMap<Long, Tuple<CurrencyPositionCollection, CurrencyPositionCollection>>();
                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        CurrencyPositionCollection cps = cu.getCurrencyPositions();
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> cpcTuple = new Tuple<CurrencyPositionCollection, CurrencyPositionCollection>( cps, cps.copy( false ) );
                        cuCcyPosMap.put( cu.getObjectID(), cpcTuple );
                    }

                    sleepFor( 1000 );

                    CreditWorkflowMessage replyMsg = ( CreditWorkflowMessage ) cwm.getReplyMessage();
                    String eventSnapshot = ( String ) replyMsg.getParameterValue( CREDIT_UTIL_EVENT_SNAPSHOT_PROPERTY );
                    String event = replyMsg.getEventName();
                    IdcDate tradeDate = replyMsg.getTradeDate();

                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        ( ( CreditUtilizationC ) cu ).setCurrencyPositions( cuCcyPosMap.get( cu.getObjectID() ).second );
                    }

                    // now apply the events from the snapshot.
                    CreditUtilizationCacheSynchronizationHandlerC.applyUtilizationEvents( event, eventSnapshot, trade1.getTransactionID(), tradeDate, true );

                    // now verify that the newly applied currency positions and original trade applied currency positions should match.

                    for ( Long cuId : cuCcyPosMap.keySet() )
                    {
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> tpl = cuCcyPosMap.get( cuId );
                        slog.warn( "tpl.first=" + tpl.first + ",tpl.second=" + tpl.second + ",dailyCalc=" + dailyCalc + ",aggCalc=" + aggCalc );
                        assertTrue( tpl.first.isCurrencyPositionsMatch( tpl.second ) );
                    }

                    // create fill trades
                    double fillAmt1 = 500;
                    double fillAmt2 = 500;
                    Trade fillTrade1 = prepareSingleLegTrade( fillAmt1, true, false, EURGBP, lpOrg, lpTpForFi, bidRates[0], spotDate );
                    Trade fillTrade2 = prepareSingleLegTrade( fillAmt2, true, false, EURGBP, lpOrg, lpTpForFi, bidRates[0], spotDate );

                    Collection<Trade> fillTradeColl = new ArrayList<Trade>();
                    fillTradeColl.add( fillTrade1 );
                    fillTradeColl.add( fillTrade2 );

                    CreditWorkflowMessage cwm1 = creditMgr.updateBilateralMultiFillCredit( fiLe, lpLe, trade1, fillTradeColl, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );

                    sleepFor( 1000 );

                    CreditWorkflowMessage replyMsg1 = ( CreditWorkflowMessage ) cwm1.getReplyMessage();
                    String eventSnapshot1 = ( String ) replyMsg1.getParameterValue( CREDIT_UTIL_EVENT_SNAPSHOT_PROPERTY );
                    String event1 = replyMsg1.getEventName();
                    IdcDate tradeDate1 = replyMsg1.getTradeDate();

                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        ( ( CreditUtilizationC ) cu ).setCurrencyPositions( cuCcyPosMap.get( cu.getObjectID() ).first );
                    }

                    // now apply the events from the snapshot.
                    CreditUtilizationCacheSynchronizationHandlerC.applyUtilizationEvents( event1, eventSnapshot1, trade1.getTransactionID(), tradeDate1, true );

                    // now verify that the newly applied currency positions and original trade applied currency positions should match.

                    for ( Long cuId : cuCcyPosMap.keySet() )
                    {
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> tpl = cuCcyPosMap.get( cuId );
                        slog.warn( "tpl.first=" + tpl.first + ",tpl.second=" + tpl.second + ",dailyCalc=" + dailyCalc + ",aggCalc=" + aggCalc );
                        assertTrue( tpl.first.isCurrencyPositionsMatch( tpl.second ) );
                    }

                    slog.warn( "Finished daily calc=" + dailyCalc + ",aggCalc=" + aggCalc );
                    sleepFor( 2000 );
                }
            }
        }
        catch ( Exception e )
        {
            fail( "testCreditUtilizationUpdatesOnBilateralTakeMultiFillFullFill", e );
        }
        finally
        {
            creditMgr.setSendCreditUtilizationUpdates( false );
        }
    }

    public void testCreditUtilizationUpdatesOnBilateralTakeMultiFillFullFillWithSameTrade()
    {
        try
        {
            init( fiUser );
            creditMgr.setSendCreditUtilizationUpdates( true );

            Collection<CreditUtilizationCalculator> aggCalcs = CreditUtilC.getSupportedNonAccountAggregateNettingMethodologies();
            Collection<CreditUtilizationCalculator> dailyCalcs = CreditUtilC.getSupportedDailyNettingMethodologies();

            for ( CreditUtilizationCalculator aggCalc : aggCalcs )
            {
                for ( CreditUtilizationCalculator dailyCalc : dailyCalcs )
                {
                    removeExistingCreditUtilizationEvents( fiOrg );
                    removeExistingCreditUtilizationEvents( lpOrg );
                    CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().removeAllCreditUtilizations();
                    setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, ********* );
                    setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, ********* );
                    setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, ********* );
                    setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, ********* );

                    double tradeAmt = 1000;
                    Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURGBP, lpOrg, lpTpForFi, bidRates[0], spotDate );
                    trade1.setTransactionID( "FXI" + System.nanoTime() );
                    CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( lpLe, fiLe, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
                    Collection<CreditUtilizationEvent> cues = cwm.getCreditUtilizationEventsForNotification();
                    Map<Long, Tuple<CurrencyPositionCollection, CurrencyPositionCollection>> cuCcyPosMap = new HashMap<Long, Tuple<CurrencyPositionCollection, CurrencyPositionCollection>>();
                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        CurrencyPositionCollection cps = cu.getCurrencyPositions();
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> cpcTuple = new Tuple<CurrencyPositionCollection, CurrencyPositionCollection>( cps, cps.copy( false ) );
                        cuCcyPosMap.put( cu.getObjectID(), cpcTuple );
                    }

                    sleepFor( 1000 );

                    CreditWorkflowMessage replyMsg = ( CreditWorkflowMessage ) cwm.getReplyMessage();
                    String eventSnapshot = ( String ) replyMsg.getParameterValue( CREDIT_UTIL_EVENT_SNAPSHOT_PROPERTY );
                    String event = replyMsg.getEventName();
                    IdcDate tradeDate = replyMsg.getTradeDate();

                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        ( ( CreditUtilizationC ) cu ).setCurrencyPositions( cuCcyPosMap.get( cu.getObjectID() ).second );
                    }

                    // now apply the events from the snapshot.
                    CreditUtilizationCacheSynchronizationHandlerC.applyUtilizationEvents( event, eventSnapshot, trade1.getTransactionID(), tradeDate, true );

                    // now verify that the newly applied currency positions and original trade applied currency positions should match.

                    for ( Long cuId : cuCcyPosMap.keySet() )
                    {
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> tpl = cuCcyPosMap.get( cuId );
                        slog.warn( "tpl.first=" + tpl.first + ",tpl.second=" + tpl.second + ",dailyCalc=" + dailyCalc + ",aggCalc=" + aggCalc );
                        assertTrue( tpl.first.isCurrencyPositionsMatch( tpl.second ) );
                    }

                    // create fill trades

                    Collection<Trade> fillTradeColl = new ArrayList<Trade>();
                    fillTradeColl.add( trade1 );

                    CreditWorkflowMessage cwm1 = creditMgr.updateBilateralMultiFillCredit( fiLe, lpLe, trade1, fillTradeColl, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );

                    sleepFor( 1000 );

                    CreditWorkflowMessage replyMsg1 = ( CreditWorkflowMessage ) cwm1.getReplyMessage();
                    String eventSnapshot1 = ( String ) replyMsg1.getParameterValue( CREDIT_UTIL_EVENT_SNAPSHOT_PROPERTY );
                    String event1 = replyMsg1.getEventName();
                    IdcDate tradeDate1 = replyMsg1.getTradeDate();

                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        ( ( CreditUtilizationC ) cu ).setCurrencyPositions( cuCcyPosMap.get( cu.getObjectID() ).first );
                    }

                    // now apply the events from the snapshot.
                    CreditUtilizationCacheSynchronizationHandlerC.applyUtilizationEvents( event1, eventSnapshot1, trade1.getTransactionID(), tradeDate1, true );

                    // now verify that the newly applied currency positions and original trade applied currency positions should match.

                    for ( Long cuId : cuCcyPosMap.keySet() )
                    {
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> tpl = cuCcyPosMap.get( cuId );
                        slog.warn( "tpl.first=" + tpl.first + ",tpl.second=" + tpl.second + ",dailyCalc=" + dailyCalc + ",aggCalc=" + aggCalc );
                        assertTrue( tpl.first.isCurrencyPositionsMatch( tpl.second ) );
                    }

                    slog.warn( "Finished daily calc=" + dailyCalc + ",aggCalc=" + aggCalc );
                    sleepFor( 2000 );
                }
            }
        }
        catch ( Exception e )
        {
            fail( "testCreditUtilizationUpdatesOnBilateralTakeMultiFillFullFillWithSameTrade", e );
        }
        finally
        {
            creditMgr.setSendCreditUtilizationUpdates( false );
        }
    }

    public void testCreditUtilizationUpdatesOnBilateralTakeMultiFillPartialFill()
    {
        try
        {
            init( fiUser );
            creditMgr.setSendCreditUtilizationUpdates( true );

            Collection<CreditUtilizationCalculator> aggCalcs = CreditUtilC.getSupportedNonAccountAggregateNettingMethodologies();
            Collection<CreditUtilizationCalculator> dailyCalcs = CreditUtilC.getSupportedDailyNettingMethodologies();

            for ( CreditUtilizationCalculator aggCalc : aggCalcs )
            {
                for ( CreditUtilizationCalculator dailyCalc : dailyCalcs )
                {
                    removeExistingCreditUtilizationEvents( fiOrg );
                    removeExistingCreditUtilizationEvents( lpOrg );
                    CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().removeAllCreditUtilizations();
                    setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, ********* );
                    setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, ********* );
                    setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, dailyCalc, ********* );
                    setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, aggCalc, ********* );

                    double tradeAmt = 1000;
                    Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURGBP, lpOrg, lpTpForFi, bidRates[0], spotDate );
                    trade1.setTransactionID( "FXI" + System.nanoTime() );
                    CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( lpLe, fiLe, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
                    Collection<CreditUtilizationEvent> cues = cwm.getCreditUtilizationEventsForNotification();
                    Map<Long, Tuple<CurrencyPositionCollection, CurrencyPositionCollection>> cuCcyPosMap = new HashMap<Long, Tuple<CurrencyPositionCollection, CurrencyPositionCollection>>();
                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        CurrencyPositionCollection cps = cu.getCurrencyPositions();
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> cpcTuple = new Tuple<CurrencyPositionCollection, CurrencyPositionCollection>( cps, cps.copy( false ) );
                        cuCcyPosMap.put( cu.getObjectID(), cpcTuple );
                    }

                    sleepFor( 1000 );

                    CreditWorkflowMessage replyMsg = ( CreditWorkflowMessage ) cwm.getReplyMessage();
                    String eventSnapshot = ( String ) replyMsg.getParameterValue( CREDIT_UTIL_EVENT_SNAPSHOT_PROPERTY );
                    String event = replyMsg.getEventName();
                    IdcDate tradeDate = replyMsg.getTradeDate();

                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        ( ( CreditUtilizationC ) cu ).setCurrencyPositions( cuCcyPosMap.get( cu.getObjectID() ).second );
                    }

                    // now apply the events from the snapshot.
                    CreditUtilizationCacheSynchronizationHandlerC.applyUtilizationEvents( event, eventSnapshot, trade1.getTransactionID(), tradeDate, true );

                    // now verify that the newly applied currency positions and original trade applied currency positions should match.

                    for ( Long cuId : cuCcyPosMap.keySet() )
                    {
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> tpl = cuCcyPosMap.get( cuId );
                        slog.warn( "tpl.first=" + tpl.first + ",tpl.second=" + tpl.second + ",dailyCalc=" + dailyCalc + ",aggCalc=" + aggCalc );
                        assertTrue( tpl.first.isCurrencyPositionsMatch( tpl.second ) );
                    }

                    // create fill trades
                    double fillAmt1 = 100;
                    double fillAmt2 = 500;
                    Trade fillTrade1 = prepareSingleLegTrade( fillAmt1, true, false, EURGBP, lpOrg, lpTpForFi, bidRates[0], spotDate );
                    Trade fillTrade2 = prepareSingleLegTrade( fillAmt2, true, false, EURGBP, lpOrg, lpTpForFi, bidRates[0], spotDate );

                    Collection<Trade> fillTradeColl = new ArrayList<Trade>();
                    fillTradeColl.add( fillTrade1 );
                    fillTradeColl.add( fillTrade2 );

                    CreditWorkflowMessage cwm1 = creditMgr.updateBilateralMultiFillCredit( fiLe, lpLe, trade1, fillTradeColl, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );

                    sleepFor( 1000 );

                    CreditWorkflowMessage replyMsg1 = ( CreditWorkflowMessage ) cwm1.getReplyMessage();
                    String eventSnapshot1 = ( String ) replyMsg1.getParameterValue( CREDIT_UTIL_EVENT_SNAPSHOT_PROPERTY );
                    String event1 = replyMsg1.getEventName();
                    IdcDate tradeDate1 = replyMsg1.getTradeDate();

                    for ( CreditUtilizationEvent cue : cues )
                    {
                        CreditUtilization cu = cue.getCreditUtilization();
                        ( ( CreditUtilizationC ) cu ).setCurrencyPositions( cuCcyPosMap.get( cu.getObjectID() ).first );
                    }

                    // now apply the events from the snapshot.
                    CreditUtilizationCacheSynchronizationHandlerC.applyUtilizationEvents( event1, eventSnapshot1, trade1.getTransactionID(), tradeDate1, true );

                    // now verify that the newly applied currency positions and original trade applied currency positions should match.

                    for ( Long cuId : cuCcyPosMap.keySet() )
                    {
                        Tuple<CurrencyPositionCollection, CurrencyPositionCollection> tpl = cuCcyPosMap.get( cuId );
                        slog.warn( "tpl.first=" + tpl.first + ",tpl.second=" + tpl.second + ",dailyCalc=" + dailyCalc + ",aggCalc=" + aggCalc );
                        assertTrue( tpl.first.isCurrencyPositionsMatch( tpl.second ) );
                    }

                    slog.warn( "Finished daily calc=" + dailyCalc + ",aggCalc=" + aggCalc );
                    sleepFor( 2000 );
                }
            }
        }
        catch ( Exception e )
        {
            fail( "testCreditUtilizationUpdatesOnBilateralTakeMultiFillPartialFill", e );
        }
        finally
        {
            creditMgr.setSendCreditUtilizationUpdates( false );
        }
    }

    public void testQueryCounterpartyCreditLimitRulesForBothExposuresWithInactivatedLegalEntity()
    {
        try
        {
            init( lpUser );
            creditAdminSvc.setOrganizationExposureLevel( lpOrg, fiOrg );

            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTpForFi );
            assertTrue( cclr.isActive() );
            assertTrue( cclr.isOrgLevel() );

            // put the changes back.
            creditAdminSvc.setLegalEntityExposureLevel( lpOrg, fiOrg );
            cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTpForFi );
            assertTrue( cclr.isActive() );
            assertTrue( !cclr.isOrgLevel() );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
        finally
        {
            creditAdminSvc.setLegalEntityExposureLevel( lpOrg, fiOrg );
        }
    }

    public void testCreditRelationships()
    {
        try
        {
            Collection<CreditRelationship> relations1 = CreditUtilC.getCreditRelationships( takerLe, makerLe );
            System.out.println( "relations1=" + relations1 );
            Collection<CreditRelationship> relations2 = CreditUtilC.getCreditRelationships( makerLe, takerLe );
            System.out.println( "relations2=" + relations2 );
            Collection<CreditRelationship> relations3 = CreditUtilC.getCreditRelationships( lpLe, ddLe );
            System.out.println( "relations3=" + relations3 );
            Collection<CreditRelationship> relations4 = CreditUtilC.getCreditRelationships( ddLe, lpLe );
            System.out.println( "relations4=" + relations4 );
            Collection<CreditRelationship> relations5 = CreditUtilC.getCreditRelationships( lpLe, takerLe );
            System.out.println( "relations5=" + relations5 );
            Collection<CreditRelationship> relations6 = CreditUtilC.getCreditRelationships( takerLe, lpLe );
            System.out.println( "relations6=" + relations6 );
            Collection<CreditRelationship> relations7 = CreditUtilC.getCreditRelationships( lpLe, makerLe );
            System.out.println( "relations7=" + relations7 );
            Collection<CreditRelationship> relations8 = CreditUtilC.getCreditRelationships( makerLe, lpLe );
            System.out.println( "relations8=" + relations8 );
        }
        catch ( Exception e )
        {
            fail( "testCreditRelationships", e );
        }
    }

    public void testBilateralAvailableCreditLimits()
    {
        try
        {

            fiTpForLp.setPrimeBrokerageCreditUsed( false );
            lpTpForFi.setPrimeBrokerageCreditUsed( false );

            AmountOfInstrument lpCreditForFiAoi = CreditUtilizationManagerC.getInstance().getAvailableLimitAmountOfInstrument( lpLe, lpTpForFi, fiOrg, spotDate );
            log( "lp credit for fi. lpCreditForFiAoi=" + lpCreditForFiAoi );
            AmountOfInstrument fiCreditForLpAoi = CreditUtilizationManagerC.getInstance().getAvailableLimitAmountOfInstrument( fiLe, fiTpForLp, lpOrg, spotDate );
            log( "fi credit for lp. fiCreditForLpAoi=" + fiCreditForLpAoi );
            AmountOfInstrument bilateralCreditAoi = CreditUtilizationManagerC.getInstance().getAvailableBilateralCreditLimitAmountOfInstrument( lpLe, fiLe, spotDate );
            log( "bilateral credit between fi and lp. bilateralCreditAoi=" + bilateralCreditAoi );
            assertEquals( "BilateralCredit should be minimum of the two. BilateralCredit=" + bilateralCreditAoi, bilateralCreditAoi.getAmount(), fiCreditForLpAoi.getAmount() > lpCreditForFiAoi.getAmount() ? lpCreditForFiAoi.getAmount() : fiCreditForLpAoi.getAmount() );

            Currency eur = CurrencyFactory.getCurrency( "EUR" );
            Currency gbp = CurrencyFactory.getCurrency( "GBP" );
            DealingLimit lpCreditForFiDealingLimit = CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyCreditLimit( lpLe, lpTpForFi, fiOrg, spotDate, eur, gbp, true );
            log( "lp credit for fi. lpCreditForFiDealingLimit=" + lpCreditForFiDealingLimit );
            DealingLimit fiCreditForLpDealingLimit = CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyCreditLimit( fiLe, fiTpForLp, lpOrg, spotDate, eur, gbp, false );
            log( "fi credit for lp. fiCreditForLpDealingLimit=" + fiCreditForLpDealingLimit );
            DealingLimit bilateralCreditDealingLimit = CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( lpLe, fiLe, spotDate, eur, gbp, true );
            log( "bilateral credit between fi and lp. bilateralCreditDealingLimit=" + bilateralCreditDealingLimit );
            assertEquals( "BilateralCredit bid limit should be minimum of the two. BilateralCredit=" + bilateralCreditDealingLimit, bilateralCreditDealingLimit.getBidLimit(), fiCreditForLpDealingLimit.getBidLimit() > lpCreditForFiDealingLimit.getBidLimit() ? lpCreditForFiDealingLimit.getBidLimit() : fiCreditForLpDealingLimit.getBidLimit() );
            assertEquals( "BilateralCredit offer limit should be minimum of the two. BilateralCredit=" + bilateralCreditDealingLimit, bilateralCreditDealingLimit.getOfferLimit(), fiCreditForLpDealingLimit.getOfferLimit() > lpCreditForFiDealingLimit.getOfferLimit() ? lpCreditForFiDealingLimit.getOfferLimit() : fiCreditForLpDealingLimit.getOfferLimit() );


            AmountOfInstrument lpCreditForDdAoi = CreditUtilizationManagerC.getInstance().getAvailableLimitAmountOfInstrument( lpLe, lpTpForDd, ddOrg, spotDate );
            log( "lp credit for dd. lpCreditForDdAoi=" + lpCreditForDdAoi );
            AmountOfInstrument ddCreditForLpAoi = CreditUtilizationManagerC.getInstance().getAvailableLimitAmountOfInstrument( ddLe, ddTpForLp, lpOrg, spotDate );
            log( "dd credit for lp. ddCreditForLpAoi=" + ddCreditForLpAoi );
            AmountOfInstrument bilateralCreditDdLpAoi = CreditUtilizationManagerC.getInstance().getAvailableBilateralCreditLimitAmountOfInstrument( lpLe, ddLe, spotDate );
            log( "bilateral credit between dd and lp. bilateralCreditDdLpAoi=" + bilateralCreditDdLpAoi );
            assertEquals( "BilateralCredit should be just lp credit. BilateralCredit=" + bilateralCreditDdLpAoi, bilateralCreditDdLpAoi.getAmount(), lpCreditForDdAoi.getAmount() );
            assertEquals( "dd credit for lp should be null. ddCreditForLpAoi=" + ddCreditForLpAoi, ddCreditForLpAoi, null );

            DealingLimit lpCreditForDdDealingLimit = CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyCreditLimit( lpLe, lpTpForDd, ddOrg, spotDate, eur, gbp, true );
            log( "lp credit for dd. lpCreditForDdDealingLimit=" + lpCreditForDdDealingLimit );
            DealingLimit ddCreditForLpDealingLimit = CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyCreditLimit( ddLe, ddTpForLp, lpOrg, spotDate, eur, gbp, false );
            log( "dd credit for lp. ddCreditForLpDealingLimit=" + ddCreditForLpDealingLimit );
            DealingLimit bilateralCreditDdLpDealingLimit = CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( lpLe, ddLe, spotDate, eur, gbp, true );
            log( "bilateral credit between dd and lp. bilateralCreditDdLpDealingLimit=" + bilateralCreditDdLpDealingLimit );
            assertEquals( "dd credit for lp should be null. ddCreditForLpDealingLimit=" + ddCreditForLpDealingLimit, ddCreditForLpDealingLimit, null );
            assertEquals( "BilateralCredit bid limit should be just lp credit. BilateralCredit=" + bilateralCreditDdLpDealingLimit, bilateralCreditDdLpDealingLimit.getBidLimit(), lpCreditForDdDealingLimit.getBidLimit() );
            assertEquals( "BilateralCredit offer limit should bejust lp credit. BilateralCredit=" + bilateralCreditDdLpDealingLimit, bilateralCreditDdLpDealingLimit.getOfferLimit(), lpCreditForDdDealingLimit.getOfferLimit() );

            // test the currency of bilateral credit limit.
            AmountOfInstrument bilateralCreditAoiInFILimitCcy = CreditUtilizationManagerC.getInstance().getAvailableBilateralCreditLimitAmountOfInstrument( fiLe, lpLe, spotDate );
            Currency fiLimitCcy = CreditUtilC.getCounterpartyCreditLimitCurrency( fiOrg, lpOrg, fiTpForLp );
            log( "available limit=" + bilateralCreditAoiInFILimitCcy + ",limitCcy=" + fiLimitCcy );
            assertEquals( "available limit should be in FI limit currency.", fiLimitCcy, bilateralCreditAoiInFILimitCcy.getInstrument() );

            AmountOfInstrument bilateralCreditAoiInLPLimitCcy = CreditUtilizationManagerC.getInstance().getAvailableBilateralCreditLimitAmountOfInstrument( lpLe, fiLe, spotDate );
            Currency lpLimitCcy = CreditUtilC.getCounterpartyCreditLimitCurrency( lpOrg, fiOrg, lpTpForFi );
            log( "available limit=" + bilateralCreditAoiInLPLimitCcy + ",limitCcy=" + lpLimitCcy );
            assertEquals( "available limit should be in LP limit currency.", lpLimitCcy, bilateralCreditAoiInLPLimitCcy.getInstrument() );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
        finally
        {
            fiTpForLp.setPrimeBrokerageCreditUsed( false );
            lpTpForFi.setPrimeBrokerageCreditUsed( false );
        }

    }


    public void testBilateralAvailableMaxTenorExceeded()
    {
        try
        {
            fiTpForLp.setPrimeBrokerageCreditUsed( false );
            lpTpForFi.setPrimeBrokerageCreditUsed( false );

            creditAdminSvc.setMaximumTenor( lpOrg, lpTpForFi, new Tenor( "5D" ) );

            Currency eur = CurrencyFactory.getCurrency( "EUR" );
            Currency gbp = CurrencyFactory.getCurrency( "GBP" );
            DealingLimit lpCreditForFiDealingLimit = CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyCreditLimit( lpLe, lpTpForFi, fiOrg, spotDate.addDays( 15 ), eur, gbp, true );
            log( "lp credit for fi. lpCreditForFiDealingLimit=" + lpCreditForFiDealingLimit );
            assertEquals( "Bid limit should be zero due to tenor exceed. lpCreditForFiDealingLimit=" + lpCreditForFiDealingLimit, lpCreditForFiDealingLimit.getBidLimit(), 0.0 );
            assertEquals( "Offer limit should be zero due to tenor exceed. lpCreditForFiDealingLimit=" + lpCreditForFiDealingLimit, lpCreditForFiDealingLimit.getOfferLimit(), 0.0 );
            DealingLimit fiCreditForLpDealingLimit = CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyCreditLimit( fiLe, fiTpForLp, lpOrg, spotDate.addDays( 15 ), eur, gbp, false );
            log( "fi credit for lp. fiCreditForLpDealingLimit=" + fiCreditForLpDealingLimit );
            DealingLimit bilateralCreditDealingLimit = CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( lpLe, fiLe, spotDate.addDays( 15 ), eur, gbp, true );
            log( "bilateral credit between fi and lp. bilateralCreditDealingLimit=" + bilateralCreditDealingLimit );
            assertEquals( "BilateralCredit bid limit should be minimum of the two. BilateralCredit=" + bilateralCreditDealingLimit, bilateralCreditDealingLimit.getBidLimit(), fiCreditForLpDealingLimit.getBidLimit() > lpCreditForFiDealingLimit.getBidLimit() ? lpCreditForFiDealingLimit.getBidLimit() : fiCreditForLpDealingLimit.getBidLimit() );
            assertEquals( "BilateralCredit offer limit should be minimum of the two. BilateralCredit=" + bilateralCreditDealingLimit, bilateralCreditDealingLimit.getOfferLimit(), fiCreditForLpDealingLimit.getOfferLimit() > lpCreditForFiDealingLimit.getOfferLimit() ? lpCreditForFiDealingLimit.getOfferLimit() : fiCreditForLpDealingLimit.getOfferLimit() );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
        finally
        {
            creditAdminSvc.setMaximumTenor( lpOrg, lpTpForFi, null );
        }
    }

    public void testBilateralAvailableMinTenorExceeded()
    {
        try
        {
            fiTpForLp.setPrimeBrokerageCreditUsed( false );
            lpTpForFi.setPrimeBrokerageCreditUsed( false );

            creditAdminSvc.setMinimumTenor( lpOrg, lpTpForFi, new Tenor( "5D" ) );

            Currency eur = CurrencyFactory.getCurrency( "EUR" );
            Currency gbp = CurrencyFactory.getCurrency( "GBP" );
            DealingLimit lpCreditForFiDealingLimit = CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyCreditLimit( lpLe, lpTpForFi, fiOrg, spotDate, eur, gbp, true );
            log( "lp credit for fi. lpCreditForFiDealingLimit=" + lpCreditForFiDealingLimit );
            assertEquals( "Bid limit should be zero due to tenor exceed. lpCreditForFiDealingLimit=" + lpCreditForFiDealingLimit, lpCreditForFiDealingLimit.getBidLimit(), 0.0 );
            assertEquals( "Offer limit should be zero due to tenor exceed. lpCreditForFiDealingLimit=" + lpCreditForFiDealingLimit, lpCreditForFiDealingLimit.getOfferLimit(), 0.0 );
            DealingLimit fiCreditForLpDealingLimit = CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyCreditLimit( fiLe, fiTpForLp, lpOrg, spotDate, eur, gbp, false );
            log( "fi credit for lp. fiCreditForLpDealingLimit=" + fiCreditForLpDealingLimit );
            DealingLimit bilateralCreditDealingLimit = CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( lpLe, fiLe, spotDate, eur, gbp, true );
            log( "bilateral credit between fi and lp. bilateralCreditDealingLimit=" + bilateralCreditDealingLimit );
            assertEquals( "BilateralCredit bid limit should be minimum of the two. BilateralCredit=" + bilateralCreditDealingLimit, bilateralCreditDealingLimit.getBidLimit(), fiCreditForLpDealingLimit.getBidLimit() > lpCreditForFiDealingLimit.getBidLimit() ? lpCreditForFiDealingLimit.getBidLimit() : fiCreditForLpDealingLimit.getBidLimit() );
            assertEquals( "BilateralCredit offer limit should be minimum of the two. BilateralCredit=" + bilateralCreditDealingLimit, bilateralCreditDealingLimit.getOfferLimit(), fiCreditForLpDealingLimit.getOfferLimit() > lpCreditForFiDealingLimit.getOfferLimit() ? lpCreditForFiDealingLimit.getOfferLimit() : fiCreditForLpDealingLimit.getOfferLimit() );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
        finally
        {
            creditAdminSvc.setMinimumTenor( lpOrg, lpTpForFi, null );
        }
    }

    public void testAvailableLimit()
    {
        try
        {
            init( lpUser );
            boolean sanity = sanityCheck( lpOrg );
            log( "sanity on lporg=" + sanity );
            boolean fiSanity = sanityCheck( fiOrg );
            log( "sanity on fiorg=" + fiSanity );
            double limit = getAvailableCreditLimit( lpTpForDd, spotDate );
            log( "limit=" + limit );
            boolean enabled = CreditUtilizationManagerC.getInstance().isCreditEnabled( lpOrg, lpTpForDd );
            log( "enabled=" + enabled );
            boolean isOrgLevel = CreditUtilC.isOrgLevelCredit( lpOrg, ddOrg );
            log( "isOrgLevel=" + isOrgLevel );
            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTpForDd );
            log( "cclr=" + cclr );
            Collection<Rule> rules = cclr.getChildrenRules();
            for ( Rule rule : rules )
            {
                CreditLimitRule clr = ( CreditLimitRule ) rule;
                log( "clr limit=" + clr.getLimitAmount() + ",clr=" + clr );
                Collection<CreditUtilization> creditUtils = clr.getCreditUtilizations();
                for ( CreditUtilization cu : creditUtils )
                {
                    log( "credit util limit amt=" + cu.getLimitAmount() + ",cu.getLimit=" + cu.getLimit() + ",available=" + cu.getAvailableMarginReserveAmount() + "cu=" + cu );
                    Object utilPercentage = PropertyUtils.getProperty( cu, "utilizedPercentage" );
                    log( "utilization percentage is " + utilPercentage );
                }
            }

            // now set the credit limit
            double newLimit = 20000;
            creditAdminSvc.setCreditLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, newLimit, CreditUtilC.getCounterpartyCreditLimitCurrency( lpOrg, ddOrg, lpTpForDd ) );
            double checkLimit = getAvailableCreditLimit( lpTpForDd, spotDate );
            log( "new limit set is " + checkLimit );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testMultiLevelMethods()
    {
        try
        {
            init( fiUser );
            creditMgr.setSendCreditUtilizationUpdates( true );

            removeExistingCreditUtilizationEvents( lpOrg );
            CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().removeAllCreditUtilizations();
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, ********* );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, ********* );

            double tradeAmt = 1000;
            Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURGBP, lpOrg, lpTpForFi, bidRates[0], spotDate );
            String transactionId = "FXI" + System.nanoTime();
            trade1.setTransactionID( transactionId );
            List<CreditEntity> creditEntities = new ArrayList<CreditEntity>();
            creditEntities.add( CreditEntityFactory.buildCreditEntity( fiLe, fiTpForLp, false ) );
            //creditEntities.add( new CreditEntity( fiLe, lpOrg, fiTpForLp, false ) );
            creditEntities.add( CreditEntityFactory.buildCreditEntity( lpLe, lpTpForFi, true ) )
            //creditEntities.add( new CreditEntity( lpLe, fiOrg, lpTpForFi, true ) )
            CreditWorkflowMessage cwm = creditMgr.takeCreditMultiLevel( lpLe, fiLe, lpLe, fiLe, trade1, creditEntities, true, null, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            Collection<CreditUtilizationEvent> cues = cwm.getCreditUtilizationEvents();
            assert cues.size() == 4;
            Map<Long, Tuple<CurrencyPositionCollection, CurrencyPositionCollection>> cuCcyPosMap = new HashMap<Long, Tuple<CurrencyPositionCollection, CurrencyPositionCollection>>();
            for ( CreditUtilizationEvent cue : cues )
            {
                CreditUtilization cu = cue.getCreditUtilization();
                CurrencyPositionCollection cps = cu.getCurrencyPositions();
                Tuple<CurrencyPositionCollection, CurrencyPositionCollection> cpcTuple = new Tuple<CurrencyPositionCollection, CurrencyPositionCollection>( cps, cps.copy( false ) );
                cuCcyPosMap.put( cu.getObjectID(), cpcTuple );
            }

            sleepFor( 10000 );

            CreditWorkflowMessage replyMsg = ( CreditWorkflowMessage ) cwm.getReplyMessage();
            String eventSnapshot = ( String ) replyMsg.getParameterValue( CREDIT_UTIL_EVENT_SNAPSHOT_PROPERTY );
            String event = replyMsg.getEventName();
            IdcDate tradeDate = replyMsg.getTradeDate();

            for ( CreditUtilizationEvent cue : cues )
            {
                CreditUtilization cu = cue.getCreditUtilization();
                ( ( CreditUtilizationC ) cu ).setCurrencyPositions( cuCcyPosMap.get( cu.getObjectID() ).second );
            }

            // now apply the events from the snapshot.
            CreditUtilizationCacheSynchronizationHandlerC.applyUtilizationEvents( event, eventSnapshot, trade1.getTransactionID(), tradeDate, true );

            // now verify that the newly applied currency positions and original trade applied currency positions should match.

            for ( Long cuId : cuCcyPosMap.keySet() )
            {
                Tuple<CurrencyPositionCollection, CurrencyPositionCollection> tpl = cuCcyPosMap.get( cuId );
                assertTrue( tpl.first.isCurrencyPositionsMatch( tpl.second ) );
            }


            for ( CreditUtilizationEvent cue : cues )
            {
                CreditUtilization cu = cue.getCreditUtilization();
                ( ( CreditUtilizationC ) cu ).setCurrencyPositions( cuCcyPosMap.get( cu.getObjectID() ).first );
            }

            // now undo Credit
            cwm = creditMgr.undoCreditMultiLevel( lpLe, fiLe, lpLe, fiLe, trade1, false, null, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            cwm.setEvent( CreditMessageEvent.REMOVE );
            creditMgr.sendCreditUpdateNotification( cwm, lpLe, fiLe, transactionId );
            cues = cwm.getCreditUtilizationEvents();
            assert cues.size() == 4;

            Thread.sleep( 10000 );


            replyMsg = ( CreditWorkflowMessage ) cwm.getReplyMessage();
            eventSnapshot = ( String ) replyMsg.getParameterValue( CREDIT_UTIL_EVENT_SNAPSHOT_PROPERTY );
            event = replyMsg.getEventName();
            tradeDate = replyMsg.getTradeDate();

            for ( CreditUtilizationEvent cue : cues )
            {
                CreditUtilization cu = cue.getCreditUtilization();
                ( ( CreditUtilizationC ) cu ).setCurrencyPositions( cuCcyPosMap.get( cu.getObjectID() ).second );
            }

            // now apply the events from the snapshot.
            CreditUtilizationCacheSynchronizationHandlerC.applyUtilizationEvents( event, eventSnapshot, trade1.getTransactionID(), tradeDate, true );

            // now verify that the newly applied currency positions and original trade applied currency positions should match.

            for ( Long cuId : cuCcyPosMap.keySet() )
            {
                Tuple<CurrencyPositionCollection, CurrencyPositionCollection> tpl = cuCcyPosMap.get( cuId );
                assertTrue( tpl.first.isCurrencyPositionsMatch( tpl.second ) );
            }


        }
        catch ( Exception e )
        {
            fail( "testCreditUtilizationUpdatesOnUnilateralCreditTakeUpdateAmount", e );
        }
        finally
        {
            creditMgr.setSendCreditUtilizationUpdates( false );
        }
    }

    /**
     * This test is for verifying that trades will credit fail and rates will be allowed when both methodologies are set to 'None'
     * while credit is active. However, there is an issue in the current implementation is that if all the credit relationships are this
     * way, then rates are not allowed. Basically rates are allowed when at least there is one regular credit relationship.
     */
    public void testBilateralCreditNoneMethodologySubscription()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            removeExistingCreditUtilizationEvents( fiOrg );

            // set all the credit methodologies to null.
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, null, 1000000 );
            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, null, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );

            Trade trade = prepareSingleLegTrade( 10000, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate );
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( fiLe, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            log( "cwm.status=" + cwm.getStatus() );
            assertEquals( "cwm.status should be failure", MessageStatus.FAILURE, cwm.getStatus() );

            // subscribe some currency pairs.
            Collection<String> ccyPairs = new ArrayList<String>();
            ccyPairs.add( "EUR/USD" );
            ccyPairs.add( "EUR/GBP" );
            ccyPairs.add( "USD/CHF" );
            ccyPairs.add( "CAD/JPY" );
            ccyPairs.add( "USD/CHF" );

            Currency eur = CurrencyFactory.getCurrency( "EUR" );
            Currency usd = CurrencyFactory.getCurrency( "USD" );
            IdcDate spDate = CreditUtilC.getSpotDate( eur, usd );

            Collection ccyPairSet = new HashSet<String>( ccyPairs );

            for ( String cp : ccyPairs )
            {
                CurrencyPair currencyPair = CurrencyFactory.getCurrencyPairFromString( cp );
                CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate( lpLe, fiLe, currencyPair.getBaseCurrency(), currencyPair.getVariableCurrency(), true, true, spDate );
            }

            DealingLimitCollection subscriptions = CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().getCreditLimitSubscriptions( lpLe, lpTpForFi );
            log( "subscriptions=" + subscriptions );
            assertEquals( "set should equal to subscriptions.", ccyPairSet.size() == subscriptions.getDealingLimits().size(), true );
            DealingLimitCollection subscriptions2 = CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().getCreditLimitSubscriptions( fiLe, fiTpForLp );
            log( "subscriptions2=" + subscriptions2 );
            assertEquals( "set should equal to subscriptions2.", ccyPairSet.size() == subscriptions2.getDealingLimits().size(), true );


            DealingLimit dl = CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( lpLe, fiLe, spDate, eur, usd, true );
            log( "bilateral credit between fi and lp. bilateralCreditDealingLimit=" + dl );
            assertTrue( "BilateralCredit bid limit should be greater than zero. BilateralCredit=" + dl, dl.getBidLimit() > 0 );
            assertTrue( "BilateralCredit offer limit should be greater than zero. BilateralCredit=" + dl, dl.getOfferLimit() > 0 );

        }
        catch ( Exception e )
        {
            fail( "testBilateralCreditNoneMethodologySubscription", e );
        }
        finally
        {
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
        }
    }

    public void testMinTenorInBusinessDaysAtLELevelExposure()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            removeExistingCreditUtilizationEvents( fiOrg );
            Currency eur = CurrencyFactory.getCurrency( "EUR" );
            Currency usd = CurrencyFactory.getCurrency( "USD" );

            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );

            // set the minTenor limit to 21 business days so that value date with and without business lag would be different.
            Tenor minTenor = new Tenor( "21d" );
            creditAdminSvc.setMinimumTenor( lpOrg, lpTpForFi, minTenor );
            creditAdminSvc.setTenorRestrictionInBusinessDays( lpOrg, lpTpForFi, true );

            IdcDate valueDate = CreditUtilC.getStdQuoteConvention().getFXRateBasis( eur, usd ).getValueDate( tradeDate, minTenor );
            IdcDate valueDateWithBusinessDayLag = CreditUtilC.getStdQuoteConvention().getFXRateBasis( eur, usd ).getValueDateWithBusinessDaysLag( tradeDate, minTenor, true );

            DealingLimit dl = CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( lpLe, fiLe, valueDate, eur, usd, true, true);
            assertTrue( CreditLimit.ERROR_EXCEED_MINIMUM_TENOR.equals( dl.getErrorCode() ) );

            DealingLimit dl1 = CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( lpLe, fiLe, valueDateWithBusinessDayLag.addDays( 1 ), eur, usd, true, true);
            assertTrue( dl1.getErrorCode() == null );
        }
        catch ( Exception e )
        {
            fail( "testMinTenorInBusinessDaysAtLELevelExposure", e );
        }
        finally
        {
            creditAdminSvc.setMinimumTenor( lpOrg, lpTpForFi, null );
            creditAdminSvc.setMinimumTenor( fiOrg, fiTpForLp, null );
        }
    }

    public void testMinTenorInBusinessDaysAtOrgLevelExposure()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            removeExistingCreditUtilizationEvents( fiOrg );
            Currency eur = CurrencyFactory.getCurrency( "EUR" );
            Currency usd = CurrencyFactory.getCurrency( "USD" );

            creditAdminSvc.setOrganizationExposureLevel( lpOrg, fiOrg );

            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );

            // set the minTenor limit to 21 business days so that value date with and without business lag would be different.
            Tenor minTenor = new Tenor( "21d" );
            creditAdminSvc.setMinimumTenor( lpOrg, lpTpForFi, minTenor );
            creditAdminSvc.setTenorRestrictionInBusinessDays( lpOrg, lpTpForFi, true );

            IdcDate valueDate = CreditUtilC.getStdQuoteConvention().getFXRateBasis( eur, usd ).getValueDate( tradeDate, minTenor );
            IdcDate valueDateWithBusinessDayLag = CreditUtilC.getStdQuoteConvention().getFXRateBasis( eur, usd ).getValueDateWithBusinessDaysLag( tradeDate, minTenor, true );

            DealingLimit dl = CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( lpLe, fiLe, valueDate, eur, usd, true, true);
            assertTrue( CreditLimit.ERROR_EXCEED_MINIMUM_TENOR.equals( dl.getErrorCode() ) );

            DealingLimit dl1 = CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( lpLe, fiLe, valueDateWithBusinessDayLag.addDays( 1 ), eur, usd, true, true);
            assertTrue( dl1.getErrorCode() == null );
        }
        catch ( Exception e )
        {
            fail( "testMinTenorInBusinessDaysAtOrgLevelExposure", e );
        }
        finally
        {
            creditAdminSvc.setMinimumTenor( lpOrg, lpTpForFi, null );
            creditAdminSvc.setMinimumTenor( fiOrg, fiTpForLp, null );
            creditAdminSvc.setLegalEntityExposureLevel( lpOrg, fiOrg );
        }
    }

    public void testMinTenorInBusinessDaysAtOrgLevelExposureWithLEOverride()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            removeExistingCreditUtilizationEvents( fiOrg );
            Currency eur = CurrencyFactory.getCurrency( "EUR" );
            Currency usd = CurrencyFactory.getCurrency( "USD" );

            creditAdminSvc.setOrganizationExposureLevel( lpOrg, fiOrg );

            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );

            creditAdminSvc.setLEOverride( lpOrg, fiOrg, true );
            creditAdminSvc.setOrgDefault( lpOrg, fiOrg, lpTpForFi, false );

            // set the minTenor limit to 21 business days so that value date with and without business lag would be different.
            Tenor minTenor = new Tenor( "21d" );
            creditAdminSvc.setMinimumTenor( lpOrg, lpTpForFi, minTenor, true );
            creditAdminSvc.setCptyLETenorRestrictionInBusinessDays( lpOrg, lpTpForFi, true );

            IdcDate valueDate = CreditUtilC.getStdQuoteConvention().getFXRateBasis( eur, usd ).getValueDate( tradeDate, minTenor );
            IdcDate valueDateWithBusinessDayLag = CreditUtilC.getStdQuoteConvention().getFXRateBasis( eur, usd ).getValueDateWithBusinessDaysLag( tradeDate, minTenor, true );

            DealingLimit dl = CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( lpLe, fiLe, valueDate, eur, usd, true, true);
            assertTrue( CreditLimit.ERROR_EXCEED_MINIMUM_TENOR.equals( dl.getErrorCode() ) );

            DealingLimit dl1 = CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( lpLe, fiLe, valueDateWithBusinessDayLag.addDays( 1 ), eur, usd, true, true);
            assertTrue( dl1.getErrorCode() == null );
        }
        catch ( Exception e )
        {
            fail( "testMinTenorInBusinessDaysAtOrgLevelExposureWithLEOverride", e );
        }
        finally
        {
            creditAdminSvc.setLEOverride( lpOrg, fiOrg, false );
            creditAdminSvc.setOrgDefault( lpOrg, fiOrg, lpTpForFi, true );
            creditAdminSvc.setMinimumTenor( lpOrg, lpTpForFi, null );
            creditAdminSvc.setMinimumTenor( fiOrg, fiTpForLp, null );
            creditAdminSvc.setMinimumTenor( lpOrg, lpTpForFi, null, true );
            creditAdminSvc.setMinimumTenor( fiOrg, fiTpForLp, null, true );
            creditAdminSvc.setLegalEntityExposureLevel( lpOrg, fiOrg );
        }
    }

    public void testMaxTenorInBusinessDaysAtLELevelExposure()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            removeExistingCreditUtilizationEvents( fiOrg );
            Currency eur = CurrencyFactory.getCurrency( "EUR" );
            Currency usd = CurrencyFactory.getCurrency( "USD" );

            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );

            // set the maxTenor limit to 21 business days so that value date with and without business lag would be different.
            Tenor maxTenor = new Tenor( "21d" );
            creditAdminSvc.setMaximumTenor( lpOrg, lpTpForFi, maxTenor );
            creditAdminSvc.setTenorRestrictionInBusinessDays( lpOrg, lpTpForFi, true );

            IdcDate valueDate = CreditUtilC.getStdQuoteConvention().getFXRateBasis( eur, usd ).getValueDate( tradeDate, maxTenor );
            IdcDate valueDateWithBusinessDayLag = CreditUtilC.getStdQuoteConvention().getFXRateBasis( eur, usd ).getValueDateWithBusinessDaysLag( tradeDate, maxTenor, true );

            DealingLimit dl = CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( lpLe, fiLe, valueDateWithBusinessDayLag.addDays( 2 ), eur, usd, true, true);
            assertTrue( CreditLimit.ERROR_EXCEED_MAXIMUM_TENOR.equals( dl.getErrorCode() ) );

            DealingLimit dl1 = CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( lpLe, fiLe, valueDate.addDays( 1 ), eur, usd, true, true);
            assertTrue( dl1.getErrorCode() == null );

            DealingLimit dl2 = CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( lpLe, fiLe, valueDateWithBusinessDayLag, eur, usd, true, true);
            assertTrue( dl2.getErrorCode() == null );
        }
        catch ( Exception e )
        {
            fail( "testMaxTenorInBusinessDaysAtLELevelExposure", e );
        }
        finally
        {
            creditAdminSvc.setMaximumTenor( lpOrg, lpTpForFi, null );
            creditAdminSvc.setMaximumTenor( fiOrg, fiTpForLp, null );
        }
    }

    public void testMaxTenorInBusinessDaysAtOrgLevelExposure()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            removeExistingCreditUtilizationEvents( fiOrg );
            Currency eur = CurrencyFactory.getCurrency( "EUR" );
            Currency usd = CurrencyFactory.getCurrency( "USD" );

            creditAdminSvc.setOrganizationExposureLevel( lpOrg, fiOrg );

            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );

            // set the maxTenor limit to 21 business days so that value date with and without business lag would be different.
            Tenor maxTenor = new Tenor( "21d" );
            creditAdminSvc.setMaximumTenor( lpOrg, lpTpForFi, maxTenor );
            creditAdminSvc.setTenorRestrictionInBusinessDays( lpOrg, lpTpForFi, true );

            IdcDate valueDate = CreditUtilC.getStdQuoteConvention().getFXRateBasis( eur, usd ).getValueDate( tradeDate, maxTenor );
            IdcDate valueDateWithBusinessDayLag = CreditUtilC.getStdQuoteConvention().getFXRateBasis( eur, usd ).getValueDateWithBusinessDaysLag( tradeDate, maxTenor, true );

            DealingLimit dl = CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( lpLe, fiLe, valueDateWithBusinessDayLag.addDays( 2 ), eur, usd, true, true);
            assertTrue( CreditLimit.ERROR_EXCEED_MAXIMUM_TENOR.equals( dl.getErrorCode() ) );

            DealingLimit dl1 = CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( lpLe, fiLe, valueDate.addDays( 1 ), eur, usd, true, true);
            assertTrue( dl1.getErrorCode() == null );

            DealingLimit dl2 = CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( lpLe, fiLe, valueDateWithBusinessDayLag, eur, usd, true, true);
            assertTrue( dl2.getErrorCode() == null );
        }
        catch ( Exception e )
        {
            fail( "testMaxTenorInBusinessDaysAtOrgLevelExposure", e );
        }
        finally
        {
            creditAdminSvc.setMaximumTenor( lpOrg, lpTpForFi, null );
            creditAdminSvc.setMaximumTenor( fiOrg, fiTpForLp, null );
            creditAdminSvc.setLegalEntityExposureLevel( lpOrg, fiOrg );
        }
    }

    public void testMaxTenorInBusinessDaysAtOrgLevelExposureWithLEOverride()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            removeExistingCreditUtilizationEvents( fiOrg );
            Currency eur = CurrencyFactory.getCurrency( "EUR" );
            Currency usd = CurrencyFactory.getCurrency( "USD" );

            creditAdminSvc.setOrganizationExposureLevel( lpOrg, fiOrg );

            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );

            creditAdminSvc.setLEOverride( lpOrg, fiOrg, true );
            creditAdminSvc.setOrgDefault( lpOrg, fiOrg, lpTpForFi, false );

            // set the maxTenor limit to 21 business days so that value date with and without business lag would be different.
            Tenor maxTenor = new Tenor( "21d" );
            creditAdminSvc.setMaximumTenor( lpOrg, lpTpForFi, maxTenor, true );
            creditAdminSvc.setCptyLETenorRestrictionInBusinessDays( lpOrg, lpTpForFi, true );

            IdcDate valueDate = CreditUtilC.getStdQuoteConvention().getFXRateBasis( eur, usd ).getValueDate( tradeDate, maxTenor );
            IdcDate valueDateWithBusinessDayLag = CreditUtilC.getStdQuoteConvention().getFXRateBasis( eur, usd ).getValueDateWithBusinessDaysLag( tradeDate, maxTenor, true );

            DealingLimit dl = CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( lpLe, fiLe, valueDateWithBusinessDayLag.addDays( 2 ), eur, usd, true, true);
            assertTrue( "valueDate=" + valueDate + ",valueDateWithBusinessDayLag=" + valueDateWithBusinessDayLag, CreditLimit.ERROR_EXCEED_MAXIMUM_TENOR.equals( dl.getErrorCode() ) );

            DealingLimit dl1 = CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( lpLe, fiLe, valueDate.addDays( 1 ), eur, usd, true, true);
            assertTrue( dl1.getErrorCode() == null );

            DealingLimit dl2 = CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( lpLe, fiLe, valueDateWithBusinessDayLag, eur, usd, true, true);
            assertTrue( dl2.getErrorCode() == null );
        }
        catch ( Exception e )
        {
            fail( "testMaxTenorInBusinessDaysAtOrgLevelExposureWithLEOverride", e );
        }
        finally
        {
            creditAdminSvc.setLEOverride( lpOrg, fiOrg, false );
            creditAdminSvc.setOrgDefault( lpOrg, fiOrg, lpTpForFi, true );
            creditAdminSvc.setMaximumTenor( lpOrg, lpTpForFi, null );
            creditAdminSvc.setMaximumTenor( fiOrg, fiTpForLp, null );
            creditAdminSvc.setMaximumTenor( lpOrg, lpTpForFi, null, true );
            creditAdminSvc.setMaximumTenor( fiOrg, fiTpForLp, null, true );
            creditAdminSvc.setLegalEntityExposureLevel( lpOrg, fiOrg );
        }
    }
    public void testNZDMinTenorInBusinessDaysAtLELevelExposure()
    {
        String nzdRollTime = FinanceConfigurationFactory.getFinanceMBean().getNZDRollTimeInGMT();
        Time rollTime = EndOfDayServiceFactory.getEndOfDayService().getRollTimeBusinessCenter().getRollTime();
        try
        {
            TimeZone.setDefault( TimeZone.getTimeZone( "GMT" ));
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            removeExistingCreditUtilizationEvents( fiOrg );
            Currency nzd = CurrencyFactory.getCurrency( "NZD" );
            Currency usd = CurrencyFactory.getCurrency( "USD" );

            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );

            // set the minTenor limit to 21 business days so that value date with and without business lag would be different.
            Tenor minTenor = new Tenor( "21d" );
            creditAdminSvc.setMinimumTenor( lpOrg, lpTpForFi, minTenor );
            creditAdminSvc.setTenorRestrictionInBusinessDays( lpOrg, lpTpForFi, true );

            IdcDateTime dt = DateTimeFactory.newDateTime( TimeZone.getTimeZone( "GMT" ));
            if ( dt.getTime().getHours() < 2 || dt.getTime().getHours() > 22 )
            {
                return;
            }

            IdcDateTime newNZDRollTime = dt.subtractSeconds( IdcDate.SECONDS_PER_HOUR );
            IdcDateTime newRollTime = dt.addSeconds( IdcDate.SECONDS_PER_HOUR  );
            String newNZDRollTimeStr = newNZDRollTime.getTime().toString();
            WatchPropertyC.update( FinanceMBean.NZD_ROLLTIME_IN_NST, null, ConfigurationProperty.DYNAMIC_SCOPE );
            WatchPropertyC.update( FinanceMBean.NZD_ROLLTIME_IN_GMT, newNZDRollTimeStr, ConfigurationProperty.DYNAMIC_SCOPE );
            EndOfDayServiceFactory.getEndOfDayService().getRollTimeBusinessCenter().setRollTime( newRollTime.getTime() );
            EndOfDayServiceFactory.getEndOfDayService().updateCurrentTradeDate();
            EndOfDayServiceFactory.getEndOfDayService().updateCurrentNZDTradeDate();

            // test the roll time setting
            IdcDate trdDate = EndOfDayServiceFactory.getEndOfDayService().getCurrentTradeDate();
            IdcDate nzdTradeDate = EndOfDayServiceFactory.getEndOfDayService().getCurrentNZDTradeDate();
            assertTrue( nzdTradeDate.isLaterThan( trdDate ) );

            IdcDate spotDateWithoutIgnoringCurrentTime = CreditUtilC.getStdQuoteConvention().getFXRateBasis( nzd, usd ).getSpotDate( trdDate, false );
            IdcDate spotDateWithIgnoringCurrentTime = CreditUtilC.getStdQuoteConvention().getFXRateBasis( nzd, usd ).getSpotDate( trdDate, true );

            if ( spotDateWithoutIgnoringCurrentTime.isSameAs( spotDateWithIgnoringCurrentTime ))
            {
                return;
            }

            IdcDate valueDate = CreditUtilC.getStdQuoteConvention().getFXRateBasis( nzd, usd ).getValueDate( trdDate, minTenor );
            IdcDate valueDateWithBusinessDayLag = CreditUtilC.getStdQuoteConvention().getFXRateBasis( nzd, usd ).getValueDateWithBusinessDaysLag( trdDate, minTenor, false );
            IdcDate valueDateWithBusinessDayLagIgnoreCurrentTime = CreditUtilC.getStdQuoteConvention().getFXRateBasis( nzd, usd ).getValueDateWithBusinessDaysLag( trdDate, minTenor, true );

            if ( valueDateWithBusinessDayLag.isSameAs( valueDateWithBusinessDayLagIgnoreCurrentTime ))
            {
                return;
            }

            DealingLimit dl = CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( lpLe, fiLe, valueDate, nzd, usd, true, true);
            assertTrue( CreditLimit.ERROR_EXCEED_MINIMUM_TENOR.equals( dl.getErrorCode() ) );

            DealingLimit dl1 = CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( lpLe, fiLe, valueDateWithBusinessDayLagIgnoreCurrentTime, nzd, usd, true, true);
            assertTrue( CreditLimit.ERROR_EXCEED_MINIMUM_TENOR.equals( dl1.getErrorCode() ) );

            DealingLimit dl2 = CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( lpLe, fiLe, valueDateWithBusinessDayLag, nzd, usd, true, true);
            assertTrue( dl2.getErrorCode() == null );
        }
        catch ( Exception e )
        {
            fail( "testNZDMinTenorInBusinessDaysAtLELevelExposure", e );
        }
        finally
        {
            creditAdminSvc.setMinimumTenor( lpOrg, lpTpForFi, null );
            creditAdminSvc.setMinimumTenor( fiOrg, fiTpForLp, null );
            WatchPropertyC.update( FinanceMBean.NZD_ROLLTIME_IN_NST, null, ConfigurationProperty.DYNAMIC_SCOPE );
            WatchPropertyC.update( FinanceMBean.NZD_ROLLTIME_IN_GMT, nzdRollTime, ConfigurationProperty.DYNAMIC_SCOPE );
            EndOfDayServiceFactory.getEndOfDayService().getRollTimeBusinessCenter().setRollTime( rollTime );
            EndOfDayServiceFactory.getEndOfDayService().updateCurrentTradeDate();
            EndOfDayServiceFactory.getEndOfDayService().updateCurrentNZDTradeDate();
        }
    }

    public void testNZDMinTenorInBusinessDaysAtOrgLevelExposure()
    {
        String nzdRollTime = FinanceConfigurationFactory.getFinanceMBean().getNZDRollTimeInGMT();
        Time rollTime = EndOfDayServiceFactory.getEndOfDayService().getRollTimeBusinessCenter().getRollTime();
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            removeExistingCreditUtilizationEvents( fiOrg );
            Currency nzd = CurrencyFactory.getCurrency( "NZD" );
            Currency usd = CurrencyFactory.getCurrency( "USD" );

            creditAdminSvc.setOrganizationExposureLevel( lpOrg, fiOrg );

            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );

            // set the minTenor limit to 21 business days so that value date with and without business lag would be different.
            Tenor minTenor = new Tenor( "21d" );
            creditAdminSvc.setMinimumTenor( lpOrg, lpTpForFi, minTenor );
            creditAdminSvc.setTenorRestrictionInBusinessDays( lpOrg, lpTpForFi, true );

            IdcDateTime dt = DateTimeFactory.newDateTime( TimeZone.getTimeZone( "GMT" ));
            if ( dt.getTime().getHours() < 2  || dt.getTime().getHours() > 22)
            {
                return;
            }

            IdcDateTime newNZDRollTime = dt.subtractSeconds( IdcDate.SECONDS_PER_HOUR );
            IdcDateTime newRollTime = dt.addSeconds( IdcDate.SECONDS_PER_HOUR  );
            String newNZDRollTimeStr = newNZDRollTime.getTime().toString();
            WatchPropertyC.update( FinanceMBean.NZD_ROLLTIME_IN_NST, null, ConfigurationProperty.DYNAMIC_SCOPE );
            WatchPropertyC.update( FinanceMBean.NZD_ROLLTIME_IN_GMT, newNZDRollTimeStr, ConfigurationProperty.DYNAMIC_SCOPE );
            EndOfDayServiceFactory.getEndOfDayService().getRollTimeBusinessCenter().setRollTime( newRollTime.getTime() );
            EndOfDayServiceFactory.getEndOfDayService().updateCurrentTradeDate();
            EndOfDayServiceFactory.getEndOfDayService().updateCurrentNZDTradeDate();

            // test the roll time setting
            IdcDate trdDate = EndOfDayServiceFactory.getEndOfDayService().getCurrentTradeDate();
            IdcDate nzdTradeDate = EndOfDayServiceFactory.getEndOfDayService().getCurrentNZDTradeDate();
            assertTrue( nzdTradeDate.isLaterThan( trdDate ) );

            IdcDate spotDateWithoutIgnoringCurrentTime = CreditUtilC.getStdQuoteConvention().getFXRateBasis( nzd, usd ).getSpotDate( trdDate, false );
            IdcDate spotDateWithIgnoringCurrentTime = CreditUtilC.getStdQuoteConvention().getFXRateBasis( nzd, usd ).getSpotDate( trdDate, true );

            if ( spotDateWithoutIgnoringCurrentTime.isSameAs( spotDateWithIgnoringCurrentTime ))
            {
                return;
            }

            IdcDate valueDate = CreditUtilC.getStdQuoteConvention().getFXRateBasis( nzd, usd ).getValueDate( trdDate, minTenor );
            IdcDate valueDateWithBusinessDayLag = CreditUtilC.getStdQuoteConvention().getFXRateBasis( nzd, usd ).getValueDateWithBusinessDaysLag( trdDate, minTenor, false );
            IdcDate valueDateWithBusinessDayLagIgnoreCurrentTime = CreditUtilC.getStdQuoteConvention().getFXRateBasis( nzd, usd ).getValueDateWithBusinessDaysLag( trdDate, minTenor, true );

            if ( valueDateWithBusinessDayLag.isSameAs( valueDateWithBusinessDayLagIgnoreCurrentTime ))
            {
                return;
            }

            DealingLimit dl = CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( lpLe, fiLe, valueDate, nzd, usd, true, true);
            assertTrue( CreditLimit.ERROR_EXCEED_MINIMUM_TENOR.equals( dl.getErrorCode() ) );

            DealingLimit dl1 = CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( lpLe, fiLe, valueDateWithBusinessDayLagIgnoreCurrentTime, nzd, usd, true, true);
            assertTrue( CreditLimit.ERROR_EXCEED_MINIMUM_TENOR.equals( dl1.getErrorCode() ) );

            DealingLimit dl2 = CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( lpLe, fiLe, valueDateWithBusinessDayLag, nzd, usd, true, true);
            assertTrue( dl2.getErrorCode() == null );
        }
        catch ( Exception e )
        {
            fail( "testNZDMinTenorInBusinessDaysAtOrgLevelExposure", e );
        }
        finally
        {
            creditAdminSvc.setMinimumTenor( lpOrg, lpTpForFi, null );
            creditAdminSvc.setMinimumTenor( fiOrg, fiTpForLp, null );
            creditAdminSvc.setLegalEntityExposureLevel( lpOrg, fiOrg );
            WatchPropertyC.update( FinanceMBean.NZD_ROLLTIME_IN_NST, null, ConfigurationProperty.DYNAMIC_SCOPE );
            WatchPropertyC.update( FinanceMBean.NZD_ROLLTIME_IN_GMT, nzdRollTime, ConfigurationProperty.DYNAMIC_SCOPE );
            EndOfDayServiceFactory.getEndOfDayService().getRollTimeBusinessCenter().setRollTime( rollTime );
            EndOfDayServiceFactory.getEndOfDayService().updateCurrentTradeDate();
            EndOfDayServiceFactory.getEndOfDayService().updateCurrentNZDTradeDate();
        }
    }

    public void testNZDMinTenorInBusinessDaysAtOrgLevelExposureWithLEOverride()
    {
        String nzdRollTime = FinanceConfigurationFactory.getFinanceMBean().getNZDRollTimeInGMT();
        Time rollTime = EndOfDayServiceFactory.getEndOfDayService().getRollTimeBusinessCenter().getRollTime();
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            removeExistingCreditUtilizationEvents( fiOrg );
            Currency nzd = CurrencyFactory.getCurrency( "NZD" );
            Currency usd = CurrencyFactory.getCurrency( "USD" );

            creditAdminSvc.setOrganizationExposureLevel( lpOrg, fiOrg );

            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );

            creditAdminSvc.setLEOverride( lpOrg, fiOrg, true );
            creditAdminSvc.setOrgDefault( lpOrg, fiOrg, lpTpForFi, false );

            // set the minTenor limit to 21 business days so that value date with and without business lag would be different.
            Tenor minTenor = new Tenor( "21d" );
            creditAdminSvc.setMinimumTenor( lpOrg, lpTpForFi, minTenor, true );
            creditAdminSvc.setCptyLETenorRestrictionInBusinessDays( lpOrg, lpTpForFi, true );

            IdcDateTime dt = DateTimeFactory.newDateTime( TimeZone.getTimeZone( "GMT" ));
            if ( dt.getTime().getHours() < 2  || dt.getTime().getHours() > 22 )
            {
                return;
            }

            IdcDateTime newNZDRollTime = dt.subtractSeconds( IdcDate.SECONDS_PER_HOUR );
            IdcDateTime newRollTime = dt.addSeconds( IdcDate.SECONDS_PER_HOUR  );
            String newNZDRollTimeStr = newNZDRollTime.getTime().toString();
            WatchPropertyC.update( FinanceMBean.NZD_ROLLTIME_IN_NST, null, ConfigurationProperty.DYNAMIC_SCOPE );
            WatchPropertyC.update( FinanceMBean.NZD_ROLLTIME_IN_GMT, newNZDRollTimeStr, ConfigurationProperty.DYNAMIC_SCOPE );
            EndOfDayServiceFactory.getEndOfDayService().getRollTimeBusinessCenter().setRollTime( newRollTime.getTime() );
            EndOfDayServiceFactory.getEndOfDayService().updateCurrentTradeDate();
            EndOfDayServiceFactory.getEndOfDayService().updateCurrentNZDTradeDate();

            // test the roll time setting
            IdcDate trdDate = EndOfDayServiceFactory.getEndOfDayService().getCurrentTradeDate();
            IdcDate nzdTradeDate = EndOfDayServiceFactory.getEndOfDayService().getCurrentNZDTradeDate();
            assertTrue( nzdTradeDate.isLaterThan( trdDate ) );

            IdcDate spotDateWithoutIgnoringCurrentTime = CreditUtilC.getStdQuoteConvention().getFXRateBasis( nzd, usd ).getSpotDate( trdDate, false );
            IdcDate spotDateWithIgnoringCurrentTime = CreditUtilC.getStdQuoteConvention().getFXRateBasis( nzd, usd ).getSpotDate( trdDate, true );

            if ( spotDateWithoutIgnoringCurrentTime.isSameAs( spotDateWithIgnoringCurrentTime ))
            {
                return;
            }

            IdcDate valueDate = CreditUtilC.getStdQuoteConvention().getFXRateBasis( nzd, usd ).getValueDate( trdDate, minTenor );
            IdcDate valueDateWithBusinessDayLag = CreditUtilC.getStdQuoteConvention().getFXRateBasis( nzd, usd ).getValueDateWithBusinessDaysLag( trdDate, minTenor, false );
            IdcDate valueDateWithBusinessDayLagIgnoreCurrentTime = CreditUtilC.getStdQuoteConvention().getFXRateBasis( nzd, usd ).getValueDateWithBusinessDaysLag( trdDate, minTenor, true );

            if ( valueDateWithBusinessDayLag.isSameAs( valueDateWithBusinessDayLagIgnoreCurrentTime ))
            {
                return;
            }

            DealingLimit dl = CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( lpLe, fiLe, valueDate, nzd, usd, true, true);
            assertTrue( CreditLimit.ERROR_EXCEED_MINIMUM_TENOR.equals( dl.getErrorCode() ) );

            DealingLimit dl1 = CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( lpLe, fiLe, valueDateWithBusinessDayLagIgnoreCurrentTime, nzd, usd, true, true);
            assertTrue( CreditLimit.ERROR_EXCEED_MINIMUM_TENOR.equals( dl1.getErrorCode() ) );

            DealingLimit dl2 = CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( lpLe, fiLe, valueDateWithBusinessDayLag, nzd, usd, true, true);
            assertTrue( dl2.getErrorCode() == null );
        }
        catch ( Exception e )
        {
            fail( "testNZDMinTenorInBusinessDaysAtOrgLevelExposureWithLEOverride", e );
        }
        finally
        {
            creditAdminSvc.setLEOverride( lpOrg, fiOrg, false );
            creditAdminSvc.setOrgDefault( lpOrg, fiOrg, lpTpForFi, true );
            creditAdminSvc.setMinimumTenor( lpOrg, lpTpForFi, null );
            creditAdminSvc.setMinimumTenor( fiOrg, fiTpForLp, null );
            creditAdminSvc.setMinimumTenor( lpOrg, lpTpForFi, null, true );
            creditAdminSvc.setMinimumTenor( fiOrg, fiTpForLp, null, true );
            creditAdminSvc.setLegalEntityExposureLevel( lpOrg, fiOrg );
            WatchPropertyC.update( FinanceMBean.NZD_ROLLTIME_IN_NST, null, ConfigurationProperty.DYNAMIC_SCOPE );
            WatchPropertyC.update( FinanceMBean.NZD_ROLLTIME_IN_GMT, nzdRollTime, ConfigurationProperty.DYNAMIC_SCOPE );
            EndOfDayServiceFactory.getEndOfDayService().getRollTimeBusinessCenter().setRollTime( rollTime );
            EndOfDayServiceFactory.getEndOfDayService().updateCurrentTradeDate();
            EndOfDayServiceFactory.getEndOfDayService().updateCurrentNZDTradeDate();
        }
    }

    public void testNZDMaxTenorInBusinessDaysAtLELevelExposure()
    {
        String nzdRollTime = FinanceConfigurationFactory.getFinanceMBean().getNZDRollTimeInGMT();
        Time rollTime = EndOfDayServiceFactory.getEndOfDayService().getRollTimeBusinessCenter().getRollTime();
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            removeExistingCreditUtilizationEvents( fiOrg );
            Currency nzd = CurrencyFactory.getCurrency( "NZD" );
            Currency usd = CurrencyFactory.getCurrency( "USD" );

            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );

            // set the maxTenor limit to 21 business days so that value date with and without business lag would be different.
            Tenor maxTenor = new Tenor( "21d" );
            creditAdminSvc.setMaximumTenor( lpOrg, lpTpForFi, maxTenor );
            creditAdminSvc.setTenorRestrictionInBusinessDays( lpOrg, lpTpForFi, true );

            IdcDateTime dt = DateTimeFactory.newDateTime( TimeZone.getTimeZone( "GMT" ));
            if ( dt.getTime().getHours() < 2  || dt.getTime().getHours() > 22 )
            {
                return;
            }

            IdcDateTime newNZDRollTime = dt.subtractSeconds( IdcDate.SECONDS_PER_HOUR );
            IdcDateTime newRollTime = dt.addSeconds( IdcDate.SECONDS_PER_HOUR  );
            String newNZDRollTimeStr = newNZDRollTime.getTime().toString();
            WatchPropertyC.update( FinanceMBean.NZD_ROLLTIME_IN_NST, null, ConfigurationProperty.DYNAMIC_SCOPE );
            WatchPropertyC.update( FinanceMBean.NZD_ROLLTIME_IN_GMT, newNZDRollTimeStr, ConfigurationProperty.DYNAMIC_SCOPE );
            EndOfDayServiceFactory.getEndOfDayService().getRollTimeBusinessCenter().setRollTime( newRollTime.getTime() );
            EndOfDayServiceFactory.getEndOfDayService().updateCurrentTradeDate();
            EndOfDayServiceFactory.getEndOfDayService().updateCurrentNZDTradeDate();

            // test the roll time setting
            IdcDate trdDate = EndOfDayServiceFactory.getEndOfDayService().getCurrentTradeDate();
            IdcDate nzdTradeDate = EndOfDayServiceFactory.getEndOfDayService().getCurrentNZDTradeDate();
            assertTrue( nzdTradeDate.isLaterThan( trdDate ) );

            IdcDate spotDateWithoutIgnoringCurrentTime = CreditUtilC.getStdQuoteConvention().getFXRateBasis( nzd, usd ).getSpotDate( trdDate, false );
            IdcDate spotDateWithIgnoringCurrentTime = CreditUtilC.getStdQuoteConvention().getFXRateBasis( nzd, usd ).getSpotDate( trdDate, true );

            if ( spotDateWithoutIgnoringCurrentTime.isSameAs( spotDateWithIgnoringCurrentTime ))
            {
                return;
            }

            IdcDate valueDate = CreditUtilC.getStdQuoteConvention().getFXRateBasis( nzd, usd ).getValueDate( trdDate, maxTenor );
            IdcDate valueDateWithBusinessDayLag = CreditUtilC.getStdQuoteConvention().getFXRateBasis( nzd, usd ).getValueDateWithBusinessDaysLag( trdDate, maxTenor, false );
            IdcDate valueDateWithBusinessDayLagIgnoreCurrentTime = CreditUtilC.getStdQuoteConvention().getFXRateBasis( nzd, usd ).getValueDateWithBusinessDaysLag( trdDate, maxTenor, true );

            if ( valueDateWithBusinessDayLag.isSameAs( valueDateWithBusinessDayLagIgnoreCurrentTime ))
            {
                return;
            }

            DealingLimit dl = CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( lpLe, fiLe, valueDate, nzd, usd, true, true);
            assertTrue( dl.getErrorCode() == null );

            DealingLimit dl1 = CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( lpLe, fiLe, valueDateWithBusinessDayLagIgnoreCurrentTime, nzd, usd, true, true);
            assertTrue( dl1.getErrorCode() == null );

            DealingLimit dl2 = CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( lpLe, fiLe, valueDateWithBusinessDayLag, nzd, usd, true, true);
            assertTrue( dl2.getErrorCode() == null );

            DealingLimit dl3 = CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( lpLe, fiLe, valueDateWithBusinessDayLag.addDays( 1 ), nzd, usd, true, true);
            assertTrue( CreditLimit.ERROR_EXCEED_MAXIMUM_TENOR.equals( dl3.getErrorCode() ) );
        }
        catch ( Exception e )
        {
            fail( "testNZDMaxTenorInBusinessDaysAtLELevelExposure", e );
        }
        finally
        {
            creditAdminSvc.setMaximumTenor( lpOrg, lpTpForFi, null );
            creditAdminSvc.setMaximumTenor( fiOrg, fiTpForLp, null );
            WatchPropertyC.update( FinanceMBean.NZD_ROLLTIME_IN_NST, null, ConfigurationProperty.DYNAMIC_SCOPE );
            WatchPropertyC.update( FinanceMBean.NZD_ROLLTIME_IN_GMT, nzdRollTime, ConfigurationProperty.DYNAMIC_SCOPE );
            EndOfDayServiceFactory.getEndOfDayService().getRollTimeBusinessCenter().setRollTime( rollTime );
            EndOfDayServiceFactory.getEndOfDayService().updateCurrentTradeDate();
            EndOfDayServiceFactory.getEndOfDayService().updateCurrentNZDTradeDate();
        }
    }

    public void testNZDMaxTenorInBusinessDaysAtOrgLevelExposure()
    {
        String nzdRollTime = FinanceConfigurationFactory.getFinanceMBean().getNZDRollTimeInGMT();
        Time rollTime = EndOfDayServiceFactory.getEndOfDayService().getRollTimeBusinessCenter().getRollTime();
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            removeExistingCreditUtilizationEvents( fiOrg );
            Currency nzd = CurrencyFactory.getCurrency( "NZD" );
            Currency usd = CurrencyFactory.getCurrency( "USD" );

            creditAdminSvc.setOrganizationExposureLevel( lpOrg, fiOrg );

            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );

            // set the maxTenor limit to 21 business days so that value date with and without business lag would be different.
            Tenor maxTenor = new Tenor( "21d" );
            creditAdminSvc.setMaximumTenor( lpOrg, lpTpForFi, maxTenor );
            creditAdminSvc.setTenorRestrictionInBusinessDays( lpOrg, lpTpForFi, true );

            IdcDateTime dt = DateTimeFactory.newDateTime( TimeZone.getTimeZone( "GMT" ));
            if ( dt.getTime().getHours() < 2  || dt.getTime().getHours() > 22 )
            {
                return;
            }

            IdcDateTime newNZDRollTime = dt.subtractSeconds( IdcDate.SECONDS_PER_HOUR );
            IdcDateTime newRollTime = dt.addSeconds( IdcDate.SECONDS_PER_HOUR  );
            String newNZDRollTimeStr = newNZDRollTime.getTime().toString();
            WatchPropertyC.update( FinanceMBean.NZD_ROLLTIME_IN_NST, null, ConfigurationProperty.DYNAMIC_SCOPE );
            WatchPropertyC.update( FinanceMBean.NZD_ROLLTIME_IN_GMT, newNZDRollTimeStr, ConfigurationProperty.DYNAMIC_SCOPE );
            EndOfDayServiceFactory.getEndOfDayService().getRollTimeBusinessCenter().setRollTime( newRollTime.getTime() );
            EndOfDayServiceFactory.getEndOfDayService().updateCurrentTradeDate();
            EndOfDayServiceFactory.getEndOfDayService().updateCurrentNZDTradeDate();

            // test the roll time setting
            IdcDate trdDate = EndOfDayServiceFactory.getEndOfDayService().getCurrentTradeDate();
            IdcDate nzdTradeDate = EndOfDayServiceFactory.getEndOfDayService().getCurrentNZDTradeDate();
            assertTrue( nzdTradeDate.isLaterThan( trdDate ) );

            IdcDate spotDateWithoutIgnoringCurrentTime = CreditUtilC.getStdQuoteConvention().getFXRateBasis( nzd, usd ).getSpotDate( trdDate, false );
            IdcDate spotDateWithIgnoringCurrentTime = CreditUtilC.getStdQuoteConvention().getFXRateBasis( nzd, usd ).getSpotDate( trdDate, true );

            if ( spotDateWithoutIgnoringCurrentTime.isSameAs( spotDateWithIgnoringCurrentTime ))
            {
                return;
            }

            IdcDate valueDate = CreditUtilC.getStdQuoteConvention().getFXRateBasis( nzd, usd ).getValueDate( trdDate, maxTenor );
            IdcDate valueDateWithBusinessDayLag = CreditUtilC.getStdQuoteConvention().getFXRateBasis( nzd, usd ).getValueDateWithBusinessDaysLag( trdDate, maxTenor, false );
            IdcDate valueDateWithBusinessDayLagIgnoreCurrentTime = CreditUtilC.getStdQuoteConvention().getFXRateBasis( nzd, usd ).getValueDateWithBusinessDaysLag( trdDate, maxTenor, true );

            if ( valueDateWithBusinessDayLag.isSameAs( valueDateWithBusinessDayLagIgnoreCurrentTime ))
            {
                return;
            }

            DealingLimit dl = CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( lpLe, fiLe, valueDate, nzd, usd, true, true);
            assertTrue( dl.getErrorCode() == null );

            DealingLimit dl1 = CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( lpLe, fiLe, valueDateWithBusinessDayLagIgnoreCurrentTime, nzd, usd, true, true);
            assertTrue( dl1.getErrorCode() == null );

            DealingLimit dl2 = CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( lpLe, fiLe, valueDateWithBusinessDayLag, nzd, usd, true, true);
            assertTrue( dl2.getErrorCode() == null );

            DealingLimit dl3 = CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( lpLe, fiLe, valueDateWithBusinessDayLag.addDays( 1 ), nzd, usd, true, true);
            assertTrue( CreditLimit.ERROR_EXCEED_MAXIMUM_TENOR.equals( dl3.getErrorCode() ) );
        }
        catch ( Exception e )
        {
            fail( "testNZDMaxTenorInBusinessDaysAtOrgLevelExposure", e );
        }
        finally
        {
            creditAdminSvc.setMaximumTenor( lpOrg, lpTpForFi, null );
            creditAdminSvc.setMaximumTenor( fiOrg, fiTpForLp, null );
            creditAdminSvc.setLegalEntityExposureLevel( lpOrg, fiOrg );
            WatchPropertyC.update( FinanceMBean.NZD_ROLLTIME_IN_NST, null, ConfigurationProperty.DYNAMIC_SCOPE );
            WatchPropertyC.update( FinanceMBean.NZD_ROLLTIME_IN_GMT, nzdRollTime, ConfigurationProperty.DYNAMIC_SCOPE );
            EndOfDayServiceFactory.getEndOfDayService().getRollTimeBusinessCenter().setRollTime( rollTime );
            EndOfDayServiceFactory.getEndOfDayService().updateCurrentTradeDate();
            EndOfDayServiceFactory.getEndOfDayService().updateCurrentNZDTradeDate();
        }
    }

    public void testNZDMaxTenorInBusinessDaysAtOrgLevelExposureWithLEOverride()
    {
        String nzdRollTime = FinanceConfigurationFactory.getFinanceMBean().getNZDRollTimeInGMT();
        Time rollTime = EndOfDayServiceFactory.getEndOfDayService().getRollTimeBusinessCenter().getRollTime();
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            removeExistingCreditUtilizationEvents( fiOrg );
            Currency nzd = CurrencyFactory.getCurrency( "NZD" );
            Currency usd = CurrencyFactory.getCurrency( "USD" );

            creditAdminSvc.setOrganizationExposureLevel( fiOrg, lpOrg );

            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );

            creditAdminSvc.setLEOverride( fiOrg, lpOrg, true );
            creditAdminSvc.setOrgDefault( fiOrg, lpOrg, fiTpForLp, false );

            // set the maxTenor limit to 21 business days so that value date with and without business lag would be different.
            Tenor maxTenor = new Tenor( "21d" );
            creditAdminSvc.setMaximumTenor( lpOrg, lpTpForFi, maxTenor, true );
            creditAdminSvc.setCptyLETenorRestrictionInBusinessDays( lpOrg, lpTpForFi, true );

            IdcDateTime dt = DateTimeFactory.newDateTime( TimeZone.getTimeZone( "GMT" ));
            if ( dt.getTime().getHours() < 2  || dt.getTime().getHours() > 22 )
            {
                return;
            }

            IdcDateTime newNZDRollTime = dt.subtractSeconds( IdcDate.SECONDS_PER_HOUR );
            IdcDateTime newRollTime = dt.addSeconds( IdcDate.SECONDS_PER_HOUR  );
            String newNZDRollTimeStr = newNZDRollTime.getTime().toString();
            WatchPropertyC.update( FinanceMBean.NZD_ROLLTIME_IN_NST, null, ConfigurationProperty.DYNAMIC_SCOPE );
            WatchPropertyC.update( FinanceMBean.NZD_ROLLTIME_IN_GMT, newNZDRollTimeStr, ConfigurationProperty.DYNAMIC_SCOPE );
            EndOfDayServiceFactory.getEndOfDayService().getRollTimeBusinessCenter().setRollTime( newRollTime.getTime() );
            EndOfDayServiceFactory.getEndOfDayService().updateCurrentTradeDate();
            EndOfDayServiceFactory.getEndOfDayService().updateCurrentNZDTradeDate();

            // test the roll time setting
            IdcDate trdDate = EndOfDayServiceFactory.getEndOfDayService().getCurrentTradeDate();
            IdcDate nzdTradeDate = EndOfDayServiceFactory.getEndOfDayService().getCurrentNZDTradeDate();
            assertTrue( nzdTradeDate.isLaterThan( trdDate ) );

            IdcDate spotDateWithoutIgnoringCurrentTime = CreditUtilC.getStdQuoteConvention().getFXRateBasis( nzd, usd ).getSpotDate( trdDate, false );
            IdcDate spotDateWithIgnoringCurrentTime = CreditUtilC.getStdQuoteConvention().getFXRateBasis( nzd, usd ).getSpotDate( trdDate, true );

            if ( spotDateWithoutIgnoringCurrentTime.isSameAs( spotDateWithIgnoringCurrentTime ))
            {
                return;
            }

            IdcDate valueDate = CreditUtilC.getStdQuoteConvention().getFXRateBasis( nzd, usd ).getValueDate( trdDate, maxTenor );
            IdcDate valueDateWithBusinessDayLag = CreditUtilC.getStdQuoteConvention().getFXRateBasis( nzd, usd ).getValueDateWithBusinessDaysLag( trdDate, maxTenor, false );
            IdcDate valueDateWithBusinessDayLagIgnoreCurrentTime = CreditUtilC.getStdQuoteConvention().getFXRateBasis( nzd, usd ).getValueDateWithBusinessDaysLag( trdDate, maxTenor, true );

            if ( valueDateWithBusinessDayLag.isSameAs( valueDateWithBusinessDayLagIgnoreCurrentTime ))
            {
                return;
            }

            DealingLimit dl = CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( lpLe, fiLe, valueDate, nzd, usd, true, true);
            assertTrue( dl.getErrorCode() == null );

            DealingLimit dl1 = CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( lpLe, fiLe, valueDateWithBusinessDayLagIgnoreCurrentTime, nzd, usd, true, true);
            assertTrue( dl1.getErrorCode() == null );

            DealingLimit dl2 = CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( lpLe, fiLe, valueDateWithBusinessDayLag, nzd, usd, true, true);
            assertTrue( dl2.getErrorCode() == null );

            DealingLimit dl3 = CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( lpLe, fiLe, valueDateWithBusinessDayLag.addDays( 1 ), nzd, usd, true, true);
            assertTrue( CreditLimit.ERROR_EXCEED_MAXIMUM_TENOR.equals( dl3.getErrorCode() ) );
        }
        catch ( Exception e )
        {
            fail( "testNZDMaxTenorInBusinessDaysAtOrgLevelExposureWithLEOverride", e );
        }
        finally
        {
            creditAdminSvc.setLEOverride( lpOrg, fiOrg, false );
            creditAdminSvc.setOrgDefault( lpOrg, fiOrg, lpTpForFi, true );
            creditAdminSvc.setMaximumTenor( lpOrg, lpTpForFi, null );
            creditAdminSvc.setMaximumTenor( fiOrg, fiTpForLp, null );
            creditAdminSvc.setMaximumTenor( lpOrg, lpTpForFi, null, true );
            creditAdminSvc.setMaximumTenor( fiOrg, fiTpForLp, null, true );
            creditAdminSvc.setLegalEntityExposureLevel( lpOrg, fiOrg );
            WatchPropertyC.update( FinanceMBean.NZD_ROLLTIME_IN_NST, null, ConfigurationProperty.DYNAMIC_SCOPE );
            WatchPropertyC.update( FinanceMBean.NZD_ROLLTIME_IN_GMT, nzdRollTime, ConfigurationProperty.DYNAMIC_SCOPE );
            EndOfDayServiceFactory.getEndOfDayService().getRollTimeBusinessCenter().setRollTime( rollTime );
            EndOfDayServiceFactory.getEndOfDayService().updateCurrentTradeDate();
            EndOfDayServiceFactory.getEndOfDayService().updateCurrentNZDTradeDate();
        }
    }

    public void testFXSwapOptimizedTakeCredit_ANR()
    {
        try
        {
            init( fiUser );
            removeExistingCreditUtilizationEvents( fiOrg );
            removeExistingCreditUtilizationEvents( lpOrg );

            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, ********* );
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, ********* );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_LIMIT_CALCULATOR, 1000000 );

            FXSwap trade = prepareSwapTrade( 400000, 400000, true, false, false, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], bidRates[0], spotDate, spotDate.addDays( 2 ) );
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( lpLe, fiLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals( "Message Status=" + cwm.getStatusName(), cwm.getStatus().equals( MessageStatus.SUCCESS ), true );

            WatchPropertyC.update( CreditConfigurationMBean.SWAP_OPTIMIZED_CREDIT_TAKE_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE );

            FXSwap trade2 = prepareSwapTrade( 400000, 400000, true, false, false, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], bidRates[0], spotDate.addDays( 2 ), spotDate.addDays( 8 ) );
            CreditWorkflowMessage cwm2 = creditMgr.takeBilateralCredit( lpLe, fiLe, trade2, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals( "Message Status=" + cwm2.getStatusName(), cwm2.getStatus().equals( MessageStatus.SUCCESS ), true );
        }
        catch ( Exception e )
        {
            fail( "testFXSwapOptimizedTakeCredit", e );
        }
        finally
        {
            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            WatchPropertyC.update( CreditConfigurationMBean.SWAP_OPTIMIZED_CREDIT_TAKE_ENABLED, "false", ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }

    public void testFXSwapOptimizedTakeCredit_ANR_InsufficientCredit()
    {
        try
        {
            init( fiUser );
            removeExistingCreditUtilizationEvents( fiOrg );
            removeExistingCreditUtilizationEvents( lpOrg );

            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, ********* );
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, ********* );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_LIMIT_CALCULATOR, 1000000 );

            WatchPropertyC.update( CreditConfigurationMBean.SWAP_OPTIMIZED_CREDIT_TAKE_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE );

            FXSwap trade = prepareSwapTrade( 1200000, 1200000, true, false, false, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], bidRates[0], spotDate, spotDate.addDays( 2 ) );
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( lpLe, fiLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertEquals( "Message Status=" + cwm.getStatusName(), cwm.getStatus().equals( MessageStatus.FAILURE ), true );

            CreditUtilization aggCu1 = getAggregateCreditUtilization( lpLe, lpTpForFi, spotDate );
            double realtimeUsedAmount1 = aggCu1.getCreditLimitRule().getCreditUtilizationCalculator().getRealtimeUtilizationAmount( aggCu1 );
            assertTrue( Math.abs ( realtimeUsedAmount1 ) < MINIMUM );

            CreditUtilization aggCu2 = getAggregateCreditUtilization( fiLe, fiTpForLp, spotDate );
            double realtimeUsedAmount2 = aggCu2.getCreditLimitRule().getCreditUtilizationCalculator().getRealtimeUtilizationAmount( aggCu2 );
            assertTrue( Math.abs ( realtimeUsedAmount2 ) < MINIMUM );

            CreditUtilization dailyCu1 = getDailyCreditUtilization( lpLe, lpTpForFi, spotDate );
            double realtimeUsedAmount3 = dailyCu1.getCreditLimitRule().getCreditUtilizationCalculator().getRealtimeUtilizationAmount( dailyCu1 );
            assertTrue( Math.abs ( realtimeUsedAmount3 ) < MINIMUM );

            CreditUtilization dailyCu2 = getDailyCreditUtilization( fiLe, fiTpForLp, spotDate );
            double realtimeUsedAmount4 = dailyCu2.getCreditLimitRule().getCreditUtilizationCalculator().getRealtimeUtilizationAmount( dailyCu2 );
            assertTrue( Math.abs ( realtimeUsedAmount4 ) < MINIMUM );
        }
        catch ( Exception e )
        {
            fail( "testFXSwapOptimizedTakeCredit_ANR_InsufficientCredit", e );
        }
        finally
        {
            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            WatchPropertyC.update( CreditConfigurationMBean.SWAP_OPTIMIZED_CREDIT_TAKE_ENABLED, "false", ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }

    public void testFXSwapAvailableCreditCheckANRType_TwoWay()
    {
        try
        {
            init( fiUser );
            removeExistingCreditUtilizationEvents( fiOrg );
            removeExistingCreditUtilizationEvents( lpOrg );

            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_LIMIT_CALCULATOR, 1000000 );

            FXSwap trade = prepareSwapTrade( 600000, 600000, true, false, false, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], bidRates[0], spotDate, spotDate.addDays( 2 ) );
            Request fxSwapRequest = prepareSwapRequest ( trade );
            for ( DealingPrice dp: fxSwapRequest.getRequestPrices () )
            {
                dp.setBidOfferMode (  DealingPrice.TWO_WAY );
            }
            CreditWorkflowMessage cwm = CreditUtilizationManagerC.getInstance().checkMultiLegFXTradeCredit( fxSwapRequest, lpLe, fiLe );
            assertNotNull( cwm );
            assertTrue( cwm.getErrors().size() > 0 );
            log( "cwm.errorCode=" + cwm.getErrorCode() );

            FXSwap trade1 = prepareSwapTrade( 300000, 300000, true, false, false, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], bidRates[0], spotDate, spotDate.addDays( 2 ) );
            Request fxSwapRequest1 = prepareSwapRequest ( trade1 );
            for ( DealingPrice dp: fxSwapRequest1.getRequestPrices () )
            {
                dp.setBidOfferMode (  DealingPrice.TWO_WAY );
            }
            CreditWorkflowMessage cwm1 = CreditUtilizationManagerC.getInstance().checkMultiLegFXTradeCredit( fxSwapRequest1, lpLe, fiLe );
            assertNull( cwm1 );

            FXSwap trade2 = prepareSwapTrade( 600000, 600000, true, false, false, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], bidRates[0], spotDate, spotDate.addDays( 2 ) );
            Request fxSwapRequest2 = prepareSwapRequest ( trade2 );
            for ( DealingPrice dp: fxSwapRequest2.getRequestPrices () )
            {
                dp.setBidOfferMode (  DealingPrice.TWO_WAY );
            }
            CreditWorkflowMessage cwm2 = CreditUtilizationManagerC.getInstance().checkMultiLegFXTradeCredit( fxSwapRequest2, lpLe, fiLe );
            assertNotNull( cwm2 );
            assertTrue( cwm2.getErrors().size() > 0 );
            log( "cwm.errorCode=" + cwm2.getErrorCode() );

            FXSwap trade3 = prepareSwapTrade( 400000, 400000, true, false, false, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], bidRates[0], spotDate, spotDate.addDays( 2 ) );
            CreditWorkflowMessage cwm3 = creditMgr.takeBilateralCredit (  lpLe, fiLe, trade3, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertTrue (  MessageStatus.SUCCESS.equals (  cwm3.getStatus () ) );

            FXSwap trade4 = prepareSwapTrade( 400000, 400000, false, true, false, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], bidRates[0], spotDate, spotDate.addDays( 2 ) );
            Request fxSwapRequest4 = prepareSwapRequest ( trade4 );
            for ( DealingPrice dp: fxSwapRequest4.getRequestPrices () )
            {
                dp.setBidOfferMode (  DealingPrice.TWO_WAY );
            }

            WatchPropertyC.update ( CreditConfigurationMBean.CREDIT_TWOWAY_RFS_WITH_ONESIDEONLY_AVAILABLE_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE );
            CreditWorkflowMessage cwm4a = CreditUtilizationManagerC.getInstance().checkMultiLegFXTradeCredit( fxSwapRequest4, lpLe, fiLe );
            assertNull( cwm4a );

            WatchPropertyC.update ( CreditConfigurationMBean.CREDIT_TWOWAY_RFS_WITH_ONESIDEONLY_AVAILABLE_ENABLED, "false", ConfigurationProperty.DYNAMIC_SCOPE );
            CreditWorkflowMessage cwm4b = CreditUtilizationManagerC.getInstance().checkMultiLegFXTradeCredit( fxSwapRequest4, lpLe, fiLe );
            assertNotNull( cwm4b );
            assertTrue( cwm4b.getErrors().size() > 0 );

            FXSwap trade5 = prepareSwapTrade( 400000, 400000, true, false, false, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], bidRates[0], spotDate.addDays ( 2 ), spotDate.addDays( 5 ) );
            Request fxSwapRequest5 = prepareSwapRequest ( trade5 );
            for ( DealingPrice dp: fxSwapRequest5.getRequestPrices () )
            {
                dp.setBidOfferMode (  DealingPrice.TWO_WAY );
            }

            WatchPropertyC.update ( CreditConfigurationMBean.CREDIT_TWOWAY_RFS_WITH_ONESIDEONLY_AVAILABLE_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE );
            CreditWorkflowMessage cwm5a = CreditUtilizationManagerC.getInstance().checkMultiLegFXTradeCredit( fxSwapRequest5, lpLe, fiLe );
            assertNull( cwm5a );

            WatchPropertyC.update ( CreditConfigurationMBean.CREDIT_TWOWAY_RFS_WITH_ONESIDEONLY_AVAILABLE_ENABLED, "false", ConfigurationProperty.DYNAMIC_SCOPE );
            CreditWorkflowMessage cwm5b = CreditUtilizationManagerC.getInstance().checkMultiLegFXTradeCredit( fxSwapRequest5, lpLe, fiLe );
            assertNotNull( cwm5b );
            assertTrue( cwm5b.getErrors().size() > 0 );

        }
        catch ( Exception e )
        {
            fail( "testFXSwapAvailableCreditCheckANRType_TwoWay", e );
        }
        finally
        {
            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            WatchPropertyC.update ( CreditConfigurationMBean.CREDIT_TWOWAY_RFS_WITH_ONESIDEONLY_AVAILABLE_ENABLED, "false", ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }
    public void testFXSwapAvailableCreditCheckANRType_Bid()
    {
        try
        {
            init( fiUser );
            removeExistingCreditUtilizationEvents( fiOrg );
            removeExistingCreditUtilizationEvents( lpOrg );

            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_LIMIT_CALCULATOR, 1000000 );

            FXSwap trade = prepareSwapTrade( 600000, 600000, true, false, false, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], bidRates[0], spotDate, spotDate.addDays( 2 ) );
            Request fxSwapRequest = prepareSwapRequest ( trade );
            fxSwapRequest.getRequestPrice( ISCommonConstants.NEAR_LEG ).setBidOfferMode( DealingPrice.OFFER );
            fxSwapRequest.getRequestPrice( ISCommonConstants.FAR_LEG ).setBidOfferMode( DealingPrice.BID );
            CreditWorkflowMessage cwm = CreditUtilizationManagerC.getInstance().checkMultiLegFXTradeCredit( fxSwapRequest, lpLe, fiLe );
            assertNotNull( cwm );
            assertTrue( cwm.getErrors().size() > 0 );
            log( "cwm.errorCode=" + cwm.getErrorCode() );

            FXSwap trade1 = prepareSwapTrade( 300000, 300000, true, false, false, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], bidRates[0], spotDate, spotDate.addDays( 2 ) );
            Request fxSwapRequest1 = prepareSwapRequest ( trade1 );
            fxSwapRequest1.getRequestPrice( ISCommonConstants.NEAR_LEG ).setBidOfferMode( DealingPrice.OFFER );
            fxSwapRequest1.getRequestPrice( ISCommonConstants.FAR_LEG ).setBidOfferMode( DealingPrice.BID );
            CreditWorkflowMessage cwm1 = CreditUtilizationManagerC.getInstance().checkMultiLegFXTradeCredit( fxSwapRequest1, lpLe, fiLe );
            assertNull( cwm1 );

            FXSwap trade2 = prepareSwapTrade( 600000, 600000, true, false, false, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], bidRates[0], spotDate, spotDate.addDays( 2 ) );
            Request fxSwapRequest2 = prepareSwapRequest ( trade2 );
            fxSwapRequest2.getRequestPrice( ISCommonConstants.NEAR_LEG ).setBidOfferMode( DealingPrice.OFFER );
            fxSwapRequest2.getRequestPrice( ISCommonConstants.FAR_LEG ).setBidOfferMode( DealingPrice.BID );
            CreditWorkflowMessage cwm2 = CreditUtilizationManagerC.getInstance().checkMultiLegFXTradeCredit( fxSwapRequest2, lpLe, fiLe );
            assertNotNull( cwm2 );
            assertTrue( cwm2.getErrors().size() > 0 );
            log( "cwm.errorCode=" + cwm2.getErrorCode() );

            FXSwap trade3 = prepareSwapTrade( 400000, 400000, true, false, false, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], bidRates[0], spotDate, spotDate.addDays( 2 ) );
            CreditWorkflowMessage cwm3 = creditMgr.takeBilateralCredit (  lpLe, fiLe, trade3, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertTrue (  MessageStatus.SUCCESS.equals (  cwm3.getStatus () ) );

            FXSwap trade4 = prepareSwapTrade( 400000, 400000, false, true, false, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], bidRates[0], spotDate, spotDate.addDays( 2 ) );
            Request fxSwapRequest4 = prepareSwapRequest ( trade4 );
            fxSwapRequest4.getRequestPrice( ISCommonConstants.NEAR_LEG ).setBidOfferMode( DealingPrice.OFFER );
            fxSwapRequest4.getRequestPrice( ISCommonConstants.FAR_LEG ).setBidOfferMode( DealingPrice.BID );
            CreditWorkflowMessage cwm4a = CreditUtilizationManagerC.getInstance().checkMultiLegFXTradeCredit( fxSwapRequest4, lpLe, fiLe );
            assertNull( cwm4a );

            FXSwap trade4b = prepareSwapTrade( 400000, 400000, true, false, false, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], bidRates[0], spotDate, spotDate.addDays( 2 ) );
            Request fxSwapRequest4b = prepareSwapRequest ( trade4b );
            fxSwapRequest4b.getRequestPrice( ISCommonConstants.NEAR_LEG ).setBidOfferMode( DealingPrice.BID );
            fxSwapRequest4b.getRequestPrice( ISCommonConstants.FAR_LEG ).setBidOfferMode( DealingPrice.OFFER );
            CreditWorkflowMessage cwm4b = CreditUtilizationManagerC.getInstance().checkMultiLegFXTradeCredit( fxSwapRequest4b, lpLe, fiLe );
            assertNotNull( cwm4b );
            assertTrue( cwm4b.getErrors().size() > 0 );

            FXSwap trade5 = prepareSwapTrade( 400000, 400000, false, true, false, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], bidRates[0], spotDate.addDays ( 2 ), spotDate.addDays( 5 ) );
            Request fxSwapRequest5 = prepareSwapRequest ( trade5 );
            fxSwapRequest5.getRequestPrice( ISCommonConstants.NEAR_LEG ).setBidOfferMode( DealingPrice.BID );
            fxSwapRequest5.getRequestPrice( ISCommonConstants.FAR_LEG ).setBidOfferMode( DealingPrice.OFFER );
            CreditWorkflowMessage cwm5a = CreditUtilizationManagerC.getInstance().checkMultiLegFXTradeCredit( fxSwapRequest5, lpLe, fiLe );
            assertNull( cwm5a );

            FXSwap trade5b = prepareSwapTrade( 400000, 400000, true, false, false, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], bidRates[0], spotDate.addDays ( 2 ), spotDate.addDays( 5 ) );
            Request fxSwapRequest5b = prepareSwapRequest ( trade5b);
            fxSwapRequest5b.getRequestPrice( ISCommonConstants.NEAR_LEG ).setBidOfferMode( DealingPrice.OFFER );
            fxSwapRequest5b.getRequestPrice( ISCommonConstants.FAR_LEG ).setBidOfferMode( DealingPrice.BID );
            CreditWorkflowMessage cwm5b = CreditUtilizationManagerC.getInstance().checkMultiLegFXTradeCredit( fxSwapRequest5b, lpLe, fiLe );
            assertNotNull( cwm5b );
            assertTrue( cwm5b.getErrors().size() > 0 );
        }
        catch ( Exception e )
        {
            fail( "testFXSwapAvailableCreditCheckANRType_Bid", e );
        }
        finally
        {
            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
        }
    }
    public void testFXSwapAvailableCreditCheckANRType_TwoWay_DailyOff()
    {
        try
        {
            init( fiUser );
            removeExistingCreditUtilizationEvents( fiOrg );
            removeExistingCreditUtilizationEvents( lpOrg );

            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, null, 1000000 );
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, null, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_LIMIT_CALCULATOR, 1000000 );

            FXSwap trade = prepareSwapTrade( 600000, 600000, true, false, false, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], bidRates[0], spotDate, spotDate.addDays( 2 ) );
            Request fxSwapRequest = prepareSwapRequest ( trade );
            for ( DealingPrice dp: fxSwapRequest.getRequestPrices () )
            {
                dp.setBidOfferMode (  DealingPrice.TWO_WAY );
            }
            CreditWorkflowMessage cwm = CreditUtilizationManagerC.getInstance().checkMultiLegFXTradeCredit( fxSwapRequest, lpLe, fiLe );
            assertNotNull( cwm );
            assertTrue( cwm.getErrors().size() > 0 );
            log( "cwm.errorCode=" + cwm.getErrorCode() );

            FXSwap trade1 = prepareSwapTrade( 300000, 300000, true, false, false, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], bidRates[0], spotDate, spotDate.addDays( 2 ) );
            Request fxSwapRequest1 = prepareSwapRequest ( trade1 );
            for ( DealingPrice dp: fxSwapRequest1.getRequestPrices () )
            {
                dp.setBidOfferMode (  DealingPrice.TWO_WAY );
            }
            CreditWorkflowMessage cwm1 = CreditUtilizationManagerC.getInstance().checkMultiLegFXTradeCredit( fxSwapRequest1, lpLe, fiLe );
            assertNull( cwm1 );

            FXSwap trade2 = prepareSwapTrade( 600000, 600000, true, false, false, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], bidRates[0], spotDate, spotDate.addDays( 2 ) );
            Request fxSwapRequest2 = prepareSwapRequest ( trade2 );
            for ( DealingPrice dp: fxSwapRequest2.getRequestPrices () )
            {
                dp.setBidOfferMode (  DealingPrice.TWO_WAY );
            }
            CreditWorkflowMessage cwm2 = CreditUtilizationManagerC.getInstance().checkMultiLegFXTradeCredit( fxSwapRequest2, lpLe, fiLe );
            assertNotNull( cwm2 );
            assertTrue( cwm2.getErrors().size() > 0 );
            log( "cwm.errorCode=" + cwm2.getErrorCode() );

            FXSwap trade3 = prepareSwapTrade( 400000, 400000, true, false, false, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], bidRates[0], spotDate, spotDate.addDays( 2 ) );
            CreditWorkflowMessage cwm3 = creditMgr.takeBilateralCredit (  lpLe, fiLe, trade3, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertTrue (  MessageStatus.SUCCESS.equals (  cwm3.getStatus () ) );

            FXSwap trade4 = prepareSwapTrade( 400000, 400000, false, true, false, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], bidRates[0], spotDate, spotDate.addDays( 2 ) );
            Request fxSwapRequest4 = prepareSwapRequest ( trade4 );
            for ( DealingPrice dp: fxSwapRequest4.getRequestPrices () )
            {
                dp.setBidOfferMode (  DealingPrice.TWO_WAY );
            }

            WatchPropertyC.update ( CreditConfigurationMBean.CREDIT_TWOWAY_RFS_WITH_ONESIDEONLY_AVAILABLE_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE );
            CreditWorkflowMessage cwm4a = CreditUtilizationManagerC.getInstance().checkMultiLegFXTradeCredit( fxSwapRequest4, lpLe, fiLe );
            assertNull( cwm4a );

            WatchPropertyC.update ( CreditConfigurationMBean.CREDIT_TWOWAY_RFS_WITH_ONESIDEONLY_AVAILABLE_ENABLED, "false", ConfigurationProperty.DYNAMIC_SCOPE );
            CreditWorkflowMessage cwm4b = CreditUtilizationManagerC.getInstance().checkMultiLegFXTradeCredit( fxSwapRequest4, lpLe, fiLe );
            assertNotNull( cwm4b );
            assertTrue( cwm4b.getErrors().size() > 0 );

            FXSwap trade5 = prepareSwapTrade( 400000, 400000, true, false, false, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], bidRates[0], spotDate.addDays ( 2 ), spotDate.addDays( 5 ) );
            Request fxSwapRequest5 = prepareSwapRequest ( trade5 );
            for ( DealingPrice dp: fxSwapRequest5.getRequestPrices () )
            {
                dp.setBidOfferMode (  DealingPrice.TWO_WAY );
            }

            WatchPropertyC.update ( CreditConfigurationMBean.CREDIT_TWOWAY_RFS_WITH_ONESIDEONLY_AVAILABLE_ENABLED, "true", ConfigurationProperty.DYNAMIC_SCOPE );
            CreditWorkflowMessage cwm5a = CreditUtilizationManagerC.getInstance().checkMultiLegFXTradeCredit( fxSwapRequest5, lpLe, fiLe );
            assertNull( cwm5a );

            WatchPropertyC.update ( CreditConfigurationMBean.CREDIT_TWOWAY_RFS_WITH_ONESIDEONLY_AVAILABLE_ENABLED, "false", ConfigurationProperty.DYNAMIC_SCOPE );
            CreditWorkflowMessage cwm5b = CreditUtilizationManagerC.getInstance().checkMultiLegFXTradeCredit( fxSwapRequest5, lpLe, fiLe );
            assertNotNull( cwm5b );
            assertTrue( cwm5b.getErrors().size() > 0 );

        }
        catch ( Exception e )
        {
            fail( "testFXSwapAvailableCreditCheckANRType_TwoWay_DailyOff", e );
        }
        finally
        {
            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            WatchPropertyC.update ( CreditConfigurationMBean.CREDIT_TWOWAY_RFS_WITH_ONESIDEONLY_AVAILABLE_ENABLED, "false", ConfigurationProperty.DYNAMIC_SCOPE );
        }
    }

    public void testFXSwapAvailableCreditCheckANRType_Bid_DailyOff()
    {
        try
        {
            init( fiUser );
            removeExistingCreditUtilizationEvents( fiOrg );
            removeExistingCreditUtilizationEvents( lpOrg );

            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, null, 1000000 );
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, null, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_LIMIT_CALCULATOR, 1000000 );

            FXSwap trade = prepareSwapTrade( 600000, 600000, true, false, false, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], bidRates[0], spotDate, spotDate.addDays( 2 ) );
            Request fxSwapRequest = prepareSwapRequest ( trade );
            fxSwapRequest.getRequestPrice( ISCommonConstants.NEAR_LEG ).setBidOfferMode( DealingPrice.OFFER );
            fxSwapRequest.getRequestPrice( ISCommonConstants.FAR_LEG ).setBidOfferMode( DealingPrice.BID );
            CreditWorkflowMessage cwm = CreditUtilizationManagerC.getInstance().checkMultiLegFXTradeCredit( fxSwapRequest, lpLe, fiLe );
            assertNotNull( cwm );
            assertTrue( cwm.getErrors().size() > 0 );
            log( "cwm.errorCode=" + cwm.getErrorCode() );

            FXSwap trade1 = prepareSwapTrade( 300000, 300000, true, false, false, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], bidRates[0], spotDate, spotDate.addDays( 2 ) );
            Request fxSwapRequest1 = prepareSwapRequest ( trade1 );
            fxSwapRequest1.getRequestPrice( ISCommonConstants.NEAR_LEG ).setBidOfferMode( DealingPrice.OFFER );
            fxSwapRequest1.getRequestPrice( ISCommonConstants.FAR_LEG ).setBidOfferMode( DealingPrice.BID );
            CreditWorkflowMessage cwm1 = CreditUtilizationManagerC.getInstance().checkMultiLegFXTradeCredit( fxSwapRequest1, lpLe, fiLe );
            assertNull( cwm1 );

            FXSwap trade2 = prepareSwapTrade( 600000, 600000, true, false, false, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], bidRates[0], spotDate, spotDate.addDays( 2 ) );
            Request fxSwapRequest2 = prepareSwapRequest ( trade2 );
            fxSwapRequest2.getRequestPrice( ISCommonConstants.NEAR_LEG ).setBidOfferMode( DealingPrice.OFFER );
            fxSwapRequest2.getRequestPrice( ISCommonConstants.FAR_LEG ).setBidOfferMode( DealingPrice.BID );
            CreditWorkflowMessage cwm2 = CreditUtilizationManagerC.getInstance().checkMultiLegFXTradeCredit( fxSwapRequest2, lpLe, fiLe );
            assertNotNull( cwm2 );
            assertTrue( cwm2.getErrors().size() > 0 );
            log( "cwm.errorCode=" + cwm2.getErrorCode() );

            FXSwap trade3 = prepareSwapTrade( 400000, 400000, true, false, false, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], bidRates[0], spotDate, spotDate.addDays( 2 ) );
            CreditWorkflowMessage cwm3 = creditMgr.takeBilateralCredit (  lpLe, fiLe, trade3, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
            assertTrue (  MessageStatus.SUCCESS.equals (  cwm3.getStatus () ) );

            FXSwap trade4 = prepareSwapTrade( 400000, 400000, false, true, false, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], bidRates[0], spotDate, spotDate.addDays( 2 ) );
            Request fxSwapRequest4 = prepareSwapRequest ( trade4 );
            fxSwapRequest4.getRequestPrice( ISCommonConstants.NEAR_LEG ).setBidOfferMode( DealingPrice.OFFER );
            fxSwapRequest4.getRequestPrice( ISCommonConstants.FAR_LEG ).setBidOfferMode( DealingPrice.BID );
            CreditWorkflowMessage cwm4a = CreditUtilizationManagerC.getInstance().checkMultiLegFXTradeCredit( fxSwapRequest4, lpLe, fiLe );
            assertNull( cwm4a );

            FXSwap trade4b = prepareSwapTrade( 400000, 400000, true, false, false, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], bidRates[0], spotDate, spotDate.addDays( 2 ) );
            Request fxSwapRequest4b = prepareSwapRequest ( trade4b );
            fxSwapRequest4b.getRequestPrice( ISCommonConstants.NEAR_LEG ).setBidOfferMode( DealingPrice.OFFER );
            fxSwapRequest4b.getRequestPrice( ISCommonConstants.FAR_LEG ).setBidOfferMode( DealingPrice.BID );
            CreditWorkflowMessage cwm4b = CreditUtilizationManagerC.getInstance().checkMultiLegFXTradeCredit( fxSwapRequest4b, lpLe, fiLe );
            assertNull( cwm4b );

            FXSwap trade5 = prepareSwapTrade( 400000, 400000, false, true, false, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], bidRates[0], spotDate.addDays ( 2 ), spotDate.addDays( 5 ) );
            Request fxSwapRequest5 = prepareSwapRequest ( trade5 );
            fxSwapRequest5.getRequestPrice( ISCommonConstants.NEAR_LEG ).setBidOfferMode( DealingPrice.BID );
            fxSwapRequest5.getRequestPrice( ISCommonConstants.FAR_LEG ).setBidOfferMode( DealingPrice.OFFER );
            CreditWorkflowMessage cwm5a = CreditUtilizationManagerC.getInstance().checkMultiLegFXTradeCredit( fxSwapRequest5, lpLe, fiLe );
            assertNull( cwm5a );

            FXSwap trade5b = prepareSwapTrade( 400000, 400000, true, false, false, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], bidRates[0], spotDate.addDays ( 2 ), spotDate.addDays( 5 ) );
            Request fxSwapRequest5b = prepareSwapRequest ( trade5b);
            fxSwapRequest5b.getRequestPrice( ISCommonConstants.NEAR_LEG ).setBidOfferMode( DealingPrice.OFFER );
            fxSwapRequest5b.getRequestPrice( ISCommonConstants.FAR_LEG ).setBidOfferMode( DealingPrice.BID );
            CreditWorkflowMessage cwm5b = CreditUtilizationManagerC.getInstance().checkMultiLegFXTradeCredit( fxSwapRequest5b, lpLe, fiLe );
            assertNotNull( cwm5b );
            assertTrue( cwm5b.getErrors().size() > 0 );
        }
        catch ( Exception e )
        {
            fail( "testFXSwapAvailableCreditCheckANRType_Bid_DailyOff", e );
        }
        finally
        {
            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
        }
    }
}
