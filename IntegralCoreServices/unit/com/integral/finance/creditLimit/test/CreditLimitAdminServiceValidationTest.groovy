package com.integral.finance.creditLimit.test;

import com.integral.finance.counterparty.TradingParty;
import com.integral.finance.creditLimit.CreditLimitClassification;
import com.integral.finance.creditLimit.CreditLimitConstants;
import com.integral.finance.creditLimit.CreditUtilizationCalculator;
import com.integral.finance.currency.Currency;

// Copyright (c) 2005 Integral Development Corporation.  All Rights Reserved.

/**
 * Tests the credit limit admin service API for validation of parameters.
 *
 * <AUTHOR> Development Corp.
 */
public class CreditLimitAdminServiceValidationTest extends CreditLimitServiceBaseTestCase
{
    public void testSetCreditEnabled()
    {
        try
        {
            init( adminUser );
            boolean exceptionThrown = false;
            try
            {
                creditAdminSvc.setCreditEnabled( null, true );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, true );

            exceptionThrown = false;
            try
            {
                creditAdminSvc.setCreditEnabled( lpOrg, null, true );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, true );

            exceptionThrown = false;
            try
            {
                creditAdminSvc.setCreditEnabled( lpOrg, ( TradingParty ) lpLe, true );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, true );

            exceptionThrown = false;
            try
            {
                creditAdminSvc.setCreditEnabled( lpOrg, ( TradingParty ) cptyLe, true );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, true );

            exceptionThrown = false;
            try
            {
                creditAdminSvc.setCreditEnabled( lpOrg, ( TradingParty ) ddLe, true );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, false );

            exceptionThrown = false;
            try
            {
                creditAdminSvc.setCreditEnabled( lpOrg, lpTpForDd, true );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, false );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testSetCreditLimit()
    {
        try
        {
            init( adminUser );
            boolean exceptionThrown = false;
            CreditLimitClassification clsf = DAILY_SETTLEMENT_CLASSIFICATION;
            Currency limitCcy = creditAdminSvc.getCreditLimitCurrency( lpOrg, lpTpForDd, clsf );
            try
            {
                creditAdminSvc.setCreditLimit( null, lpTpForDd, clsf, 10, creditAdminSvc.getCreditLimitCurrency( lpOrg, lpTpForDd, clsf ) );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, true );

            exceptionThrown = false;
            try
            {
                creditAdminSvc.setCreditLimit( lpOrg, null, clsf, 10, limitCcy );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, true );

            exceptionThrown = false;
            try
            {
                creditAdminSvc.setCreditLimit( lpOrg, lpTpForDd, null, 10, limitCcy );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, true );

            exceptionThrown = false;
            try
            {
                creditAdminSvc.setCreditLimit( lpOrg, lpTpForDd, clsf, -10, limitCcy );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, true );

            exceptionThrown = false;
            try
            {
                creditAdminSvc.setCreditLimit( lpOrg, lpTpForDd, clsf, 10, null );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, true );

            exceptionThrown = false;
            try
            {
                creditAdminSvc.setCreditLimit( lpOrg, ( TradingParty ) lpLe, clsf, 10000000, limitCcy );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, true );

            exceptionThrown = false;
            try
            {
                creditAdminSvc.setCreditLimit( lpOrg, ( TradingParty ) cptyLe, clsf, 10000000, limitCcy );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, true );

            exceptionThrown = false;
            try
            {
                creditAdminSvc.setCreditLimit( lpOrg, ( TradingParty ) ddLe, clsf, 10000000, limitCcy );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, false );

            exceptionThrown = false;
            try
            {
                creditAdminSvc.setCreditLimit( lpOrg, lpTpForDd, clsf, 10000000, limitCcy );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, false );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testSetCreditLimitOnDate()
    {
        try
        {
            init( adminUser );
            boolean exceptionThrown = false;
            CreditLimitClassification clsf = DAILY_SETTLEMENT_CLASSIFICATION;
            Currency limitCcy = creditAdminSvc.getCreditLimitCurrency( lpOrg, lpTpForDd, clsf );
            try
            {
                creditAdminSvc.setCreditLimit( null, lpTpForDd, clsf, 10.0, creditAdminSvc.getCreditLimitCurrency( lpOrg, lpTpForDd, clsf ), spotDate );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, true );

            exceptionThrown = false;
            try
            {
                creditAdminSvc.setCreditLimit( lpOrg, null, clsf, 10.0, limitCcy, spotDate );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, true );

            exceptionThrown = false;
            try
            {
                creditAdminSvc.setCreditLimit( lpOrg, lpTpForDd, null, 10.0, limitCcy, spotDate );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, true );

            exceptionThrown = false;
            try
            {
                creditAdminSvc.setCreditLimit( lpOrg, lpTpForDd, clsf, -10.0, limitCcy, spotDate );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, true );

            exceptionThrown = false;
            try
            {
                creditAdminSvc.setCreditLimit( lpOrg, lpTpForDd, clsf, 10.0, null, spotDate );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, true );

            exceptionThrown = false;
            try
            {
                creditAdminSvc.setCreditLimit( lpOrg, lpTpForDd, clsf, 10.0, limitCcy, null );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, true );

            exceptionThrown = false;
            try
            {
                creditAdminSvc.setCreditLimit( lpOrg, ( TradingParty ) lpLe, clsf, 10000000.0, limitCcy, spotDate );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, true );

            exceptionThrown = false;
            try
            {
                creditAdminSvc.setCreditLimit( lpOrg, ( TradingParty ) cptyLe, clsf, 10000000.0, limitCcy, spotDate );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, true );

            exceptionThrown = false;
            try
            {
                creditAdminSvc.setCreditLimit( lpOrg, ( TradingParty ) ddLe, clsf, 10000000.0, limitCcy, spotDate );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, false );

            exceptionThrown = false;
            try
            {
                creditAdminSvc.setCreditLimit( lpOrg, lpTpForDd, clsf, 10000000.0, limitCcy, spotDate );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, false );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testSetCreditExposure()
    {
        try
        {
            boolean exceptionThrown = false;
            try
            {
                creditAdminSvc.setOrganizationExposureLevel( null, ddOrg );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, true );

            exceptionThrown = false;
            try
            {
                creditAdminSvc.setOrganizationExposureLevel( lpOrg, null );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, true );

            exceptionThrown = false;
            try
            {
                creditAdminSvc.setOrganizationExposureLevel( lpOrg, lpOrg );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, true );

            exceptionThrown = false;
            try
            {
                creditAdminSvc.setLegalEntityExposureLevel( null, ddOrg );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, true );

            exceptionThrown = false;
            try
            {
                creditAdminSvc.setLegalEntityExposureLevel( lpOrg, null );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, true );

            exceptionThrown = false;
            try
            {
                creditAdminSvc.setLegalEntityExposureLevel( lpOrg, lpOrg );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testSetNettingMethodology()
    {
        try
        {
            CreditLimitClassification clsf = GROSS_NOTIONAL_CLASSIFICATION;
            CreditUtilizationCalculator calc = CreditLimitConstants.DAILY_SETTLEMENT_LIMIT_CALCULATOR;
            boolean exceptionThrown = false;
            try
            {
                creditAdminSvc.setNettingMethodology( null, lpTpForDd, clsf, calc );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, true );

            exceptionThrown = false;
            try
            {
                creditAdminSvc.setNettingMethodology( lpOrg, null, clsf, calc );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, true );

            exceptionThrown = false;
            try
            {
                creditAdminSvc.setNettingMethodology( lpOrg, lpTpForDd, null, calc );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, false );

            exceptionThrown = false;
            try
            {
                creditAdminSvc.setNettingMethodology( lpOrg, lpTpForDd, null, null );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, true );

            exceptionThrown = false;
            try
            {
                creditAdminSvc.setNettingMethodology( lpOrg, lpTpForDd, clsf, null );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, false );

            exceptionThrown = false;
            try
            {
                creditAdminSvc.setNettingMethodology( lpOrg, lpTpForDd, clsf, GROSS_DAILY_LIMIT_CALCULATOR );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, true );

            exceptionThrown = false;
            try
            {
                creditAdminSvc.setNettingMethodology( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, AGGREGATE_LIMIT_CALCULATOR );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, true );


            exceptionThrown = false;
            try
            {
                creditAdminSvc.setNettingMethodology( lpOrg, ( TradingParty ) lpLe, clsf, null );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, true );

            exceptionThrown = false;
            try
            {
                creditAdminSvc.setNettingMethodology( lpOrg, ( TradingParty ) cptyLe, clsf, null );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testSetNotificationPercentage()
    {
        try
        {
            boolean exceptionThrown = false;
            try
            {
                creditAdminSvc.setNotificationPercentage( null, 10 );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, true );

            exceptionThrown = false;
            try
            {
                creditAdminSvc.setNotificationPercentage( lpOrg, 1001 );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, true );

            exceptionThrown = false;
            try
            {
                creditAdminSvc.setNotificationPercentage( lpOrg, -1 );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, true );

            exceptionThrown = false;
            try
            {
                creditAdminSvc.setNotificationPercentage( null, lpTpForDd, 10 );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, true );

            exceptionThrown = false;
            try
            {
                creditAdminSvc.setNotificationPercentage( lpOrg, lpTpForDd, 1001 );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, true );

            exceptionThrown = false;
            try
            {
                creditAdminSvc.setNotificationPercentage( lpOrg, lpTpForDd, -1 );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, true );

            exceptionThrown = false;
            try
            {
                creditAdminSvc.setNotificationPercentage( lpOrg, ( TradingParty ) lpLe, 10 );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, true );

            exceptionThrown = false;
            try
            {
                creditAdminSvc.setNotificationPercentage( lpOrg, ( TradingParty ) cptyLe, 10 );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testSetWarningPercentage()
    {
        try
        {
            boolean exceptionThrown = false;
            try
            {
                creditAdminSvc.setWarningPercentage( null, 10 );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, true );

            exceptionThrown = false;
            try
            {
                creditAdminSvc.setWarningPercentage( lpOrg, 1001 );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, true );

            exceptionThrown = false;
            try
            {
                creditAdminSvc.setWarningPercentage( lpOrg, -1 );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, true );

            exceptionThrown = false;
            try
            {
                creditAdminSvc.setWarningPercentage( null, lpTpForDd, 10 );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, true );

            exceptionThrown = false;
            try
            {
                creditAdminSvc.setWarningPercentage( lpOrg, lpTpForDd, 1001 );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, true );

            exceptionThrown = false;
            try
            {
                creditAdminSvc.setWarningPercentage( lpOrg, lpTpForDd, -1 );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, true );

            exceptionThrown = false;
            try
            {
                creditAdminSvc.setWarningPercentage( lpOrg, ( TradingParty ) lpLe, 10 );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, true );

            exceptionThrown = false;
            try
            {
                creditAdminSvc.setWarningPercentage( lpOrg, ( TradingParty ) cptyLe, 10 );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testSetSuspensionPercentage()
    {
        try
        {
            boolean exceptionThrown = false;
            try
            {
                creditAdminSvc.setSuspensionPercentage( null, 10 );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, true );

            exceptionThrown = false;
            try
            {
                creditAdminSvc.setSuspensionPercentage( lpOrg, 1001 );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, true );

            exceptionThrown = false;
            try
            {
                creditAdminSvc.setSuspensionPercentage( lpOrg, -1 );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, true );

            exceptionThrown = false;
            try
            {
                creditAdminSvc.setSuspensionPercentage( null, lpTpForDd, 10 );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, true );

            exceptionThrown = false;
            try
            {
                creditAdminSvc.setSuspensionPercentage( lpOrg, lpTpForDd, 1001 );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, true );

            exceptionThrown = false;
            try
            {
                creditAdminSvc.setSuspensionPercentage( lpOrg, lpTpForDd, -1 );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, true );

            exceptionThrown = false;
            try
            {
                creditAdminSvc.setSuspensionPercentage( lpOrg, ( TradingParty ) lpLe, 10 );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, true );

            exceptionThrown = false;
            try
            {
                creditAdminSvc.setSuspensionPercentage( lpOrg, ( TradingParty ) cptyLe, 10 );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testSetSenderEmailAddress()
    {
        try
        {
            init( adminUser );
            boolean exceptionThrown = false;
            try
            {
                creditAdminSvc.setSenderEmailAddress( null, null );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, true );

            exceptionThrown = false;
            try
            {
                creditAdminSvc.setSenderEmailAddress( lpOrg, "sdfdsfs" );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, true );

            exceptionThrown = false;
            try
            {
                creditAdminSvc.setSenderEmailAddress( lpOrg, "nobody@integral" );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, true );

            exceptionThrown = false;
            try
            {
                creditAdminSvc.setSenderEmailAddress( lpOrg, null );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, false );

            exceptionThrown = false;
            try
            {
                creditAdminSvc.setSenderEmailAddress( lpOrg, "<EMAIL>" );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, false );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testSetNotificationEmailAddress()
    {
        try
        {
            init( adminUser );
            boolean exceptionThrown = false;
            try
            {
                creditAdminSvc.setNotificationEmailAddress( null, null );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, true );

            exceptionThrown = false;
            try
            {
                creditAdminSvc.setNotificationEmailAddress( lpOrg, "sdfdsfs" );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, true );

            exceptionThrown = false;
            try
            {
                creditAdminSvc.setNotificationEmailAddress( lpOrg, "nobody@integral" );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, true );

            exceptionThrown = false;
            try
            {
                creditAdminSvc.setNotificationEmailAddress( lpOrg, null );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, false );

            exceptionThrown = false;
            try
            {
                creditAdminSvc.setNotificationEmailAddress( lpOrg, "<EMAIL>;<EMAIL>" );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, false );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testSetLeverageFactor()
    {
        try
        {
            boolean exceptionThrown = false;
            try
            {
                creditAdminSvc.setDefaultLeverageFactor( null, 10.0 );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, true );

            exceptionThrown = false;
            try
            {
                creditAdminSvc.setDefaultLeverageFactor( lpOrg, 0.0 );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, true );

            exceptionThrown = false;
            try
            {
                creditAdminSvc.setDefaultLeverageFactor( lpOrg, -1.0 );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, true );

            exceptionThrown = false;
            try
            {
                creditAdminSvc.setLeverageFactor( null, lpTpForDd, CreditLimitConstants.GROSS_NOTIONAL_CLASSIFICATION, 10.0 );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, true );

            exceptionThrown = false;
            try
            {
                creditAdminSvc.setLeverageFactor( lpOrg, lpTpForDd, CreditLimitConstants.GROSS_NOTIONAL_CLASSIFICATION, 0.0 );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, true );

            exceptionThrown = false;
            try
            {
                creditAdminSvc.setLeverageFactor( lpOrg, lpTpForDd, CreditLimitConstants.GROSS_NOTIONAL_CLASSIFICATION, -1.0 );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, true );

            exceptionThrown = false;
            try
            {
                creditAdminSvc.setLeverageFactor( lpOrg, ( TradingParty ) lpLe, CreditLimitConstants.GROSS_NOTIONAL_CLASSIFICATION, 10.0 );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, true );

            exceptionThrown = false;
            try
            {
                creditAdminSvc.setLeverageFactor( lpOrg, ( TradingParty ) cptyLe, CreditLimitConstants.GROSS_NOTIONAL_CLASSIFICATION, 10.0 );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, true );

            exceptionThrown = false;
            try
            {
                creditAdminSvc.setLeverageFactor( lpOrg, lpTpForDd, null, 10.0 );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
        finally
        {
            creditAdminSvc.setLeverageFactor( lpOrg, lpTpForDd, CreditLimitConstants.GROSS_NOTIONAL_CLASSIFICATION, 1.0 );
        }
    }

    public void testSetApplyPandL()
    {
        try
        {
            boolean exceptionThrown = false;
            try
            {
                creditAdminSvc.setDefaultApplyPandL( null, true );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, true );

            exceptionThrown = false;
            try
            {
                creditAdminSvc.setDefaultApplyPandL( lpOrg, null );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, false );

            exceptionThrown = false;
            try
            {
                creditAdminSvc.setDefaultApplyPandL( lpOrg, true );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, false );

            exceptionThrown = false;
            try
            {
                creditAdminSvc.setApplyPandL( null, lpTpForDd, CreditLimitConstants.GROSS_NOTIONAL_CLASSIFICATION, true );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, true );

            exceptionThrown = false;
            try
            {
                creditAdminSvc.setApplyPandL( lpOrg, lpTpForDd, CreditLimitConstants.GROSS_NOTIONAL_CLASSIFICATION, null );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, false );

            exceptionThrown = false;
            try
            {
                creditAdminSvc.setApplyPandL( lpOrg, lpTpForDd, CreditLimitConstants.GROSS_NOTIONAL_CLASSIFICATION, false );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, false );

            exceptionThrown = false;
            try
            {
                creditAdminSvc.setApplyPandL( lpOrg, ( TradingParty ) lpLe, CreditLimitConstants.GROSS_NOTIONAL_CLASSIFICATION, false );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, true );

            exceptionThrown = false;
            try
            {
                creditAdminSvc.setApplyPandL( lpOrg, ( TradingParty ) cptyLe, CreditLimitConstants.GROSS_NOTIONAL_CLASSIFICATION, true );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, true );

            exceptionThrown = false;
            try
            {
                creditAdminSvc.setApplyPandL( lpOrg, lpTpForDd, null, false );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testSetIgnoreCurrDatePostions()
    {
        try
        {
            boolean exceptionThrown = false;
            try
            {
                creditAdminSvc.setDefaultIgnoreCurrDatePositions( null, true );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, true );

            exceptionThrown = false;
            try
            {
                creditAdminSvc.setDefaultIgnoreCurrDatePositions( lpOrg, null );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, false );

            exceptionThrown = false;
            try
            {
                creditAdminSvc.setDefaultIgnoreCurrDatePositions( lpOrg, true );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, false );

            exceptionThrown = false;
            try
            {
                creditAdminSvc.setIgnoreCurrDatePositions( null, lpTpForDd, CreditLimitConstants.GROSS_NOTIONAL_CLASSIFICATION, true );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, true );

            exceptionThrown = false;
            try
            {
                creditAdminSvc.setIgnoreCurrDatePositions( lpOrg, lpTpForDd, CreditLimitConstants.GROSS_NOTIONAL_CLASSIFICATION, null );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, false );

            exceptionThrown = false;
            try
            {
                creditAdminSvc.setIgnoreCurrDatePositions( lpOrg, lpTpForDd, CreditLimitConstants.GROSS_NOTIONAL_CLASSIFICATION, false );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, false );

            exceptionThrown = false;
            try
            {
                creditAdminSvc.setIgnoreCurrDatePositions( lpOrg, ( TradingParty ) lpLe, CreditLimitConstants.GROSS_NOTIONAL_CLASSIFICATION, false );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, true );

            exceptionThrown = false;
            try
            {
                creditAdminSvc.setIgnoreCurrDatePositions( lpOrg, ( TradingParty ) cptyLe, CreditLimitConstants.GROSS_NOTIONAL_CLASSIFICATION, true );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, true );

            exceptionThrown = false;
            try
            {
                creditAdminSvc.setIgnoreCurrDatePositions( lpOrg, lpTpForDd, null, false );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testSetStopOutPercentage()
    {
        try
        {
            boolean exceptionThrown = false;
            try
            {
                creditAdminSvc.setStopOutPercentage ( null, 10.0 );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, true );

            exceptionThrown = false;
            try
            {
                creditAdminSvc.setStopOutPercentage( lpOrg, 1001.0 );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, true );

            exceptionThrown = false;
            try
            {
                creditAdminSvc.setStopOutPercentage( lpOrg, -1.0 );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, true );

            exceptionThrown = false;
            try
            {
                creditAdminSvc.setStopOutPercentage( null, lpTpForDd, 10.0 );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, true );

            exceptionThrown = false;
            try
            {
                creditAdminSvc.setStopOutPercentage( lpOrg, lpTpForDd, 1001.0 );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, true );

            exceptionThrown = false;
            try
            {
                creditAdminSvc.setStopOutPercentage( lpOrg, lpTpForDd, -1.0 );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, true );

            exceptionThrown = false;
            try
            {
                creditAdminSvc.setStopOutPercentage( lpOrg, ( TradingParty ) lpLe, 10.0 );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, true );

            exceptionThrown = false;
            try
            {
                creditAdminSvc.setStopOutPercentage( lpOrg, ( TradingParty ) cptyLe, 10.0 );
            }
            catch ( Exception e )
            {
                exceptionThrown = true;
            }
            assertEquals( "exceptionThrown=" + exceptionThrown, exceptionThrown, true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }
}
