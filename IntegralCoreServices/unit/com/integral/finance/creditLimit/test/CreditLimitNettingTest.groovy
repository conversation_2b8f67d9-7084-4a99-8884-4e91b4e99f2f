package com.integral.finance.creditLimit.test

// Copyright (c) 2023 Integral Development Corporation.  All Rights Reserved.

import com.integral.finance.currency.Currency
import com.integral.finance.currency.CurrencyFactory
import com.integral.finance.dealing.Request
import com.integral.finance.trade.Trade
import com.integral.message.MessageStatus
import com.integral.workflow.dealing.DealingLimit
import com.integral.finance.creditLimit.*
import com.integral.session.IdcTransaction

/**
 * Tests various credit netting methodologies.
 *
 * <AUTHOR> Development Corp.
 */
class CreditLimitNettingTest extends CreditLimitServiceBaseTestCase
{
    void testSimpleDailySettlementNetting()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 10000 )
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 )
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION )
            log( "Credit limit before first bid trade take Credit : " + limit0 )
            double tradeAmt = 1000
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd )
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION )
            log( "Credit limit after first bid trade take Credit : " + limit1 + " success : " + cwm.getStatus() )
            assertEquals( "before limit=" + limit0 + ",limitAfter=" + limit1, true, Math.abs( limit0 - limit1 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM )
            validateCreditUtilizationEvents( cwm )

            // Now take an offer side trade.
            Trade trade1 = prepareSingleLegTrade( tradeAmt, false, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd )
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION )
            log( "Credit limit after second offer trade take Credit : " + limit2 + " success : " + cwm1.getStatus() )
            validateCreditUtilizationEvents( cwm1 )
            assertEquals( "before limit=" + limit1 + ",after limit=" + limit2, true, Math.abs( limit0 - limit2 ) < CREDIT_CALCULATION_MINIMUM )
        }
        catch ( Exception e )
        {
            e.printStackTrace()
            fail()
        }
    }

    void testDailySettlementNettingWithDifferentValueDates()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 10000 )
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 )
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION )
            log( "On Spot date, credit limit before take Credit : " + limit0 )
            double tradeAmt = 1000
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd )
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION )
            log( "On spot date, credit limit after take Credit : " + limit1 + " success : " + cwm.getStatus() )
            assertEquals( "before limit=" + limit0 + ",limit after=" + limit1, true, Math.abs( limit0 - limit1 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM )
            validateCreditUtilizationEvents( cwm )

            // Now do a trade on spot next date
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate.addDays( 1 ), DAILY_SETTLEMENT_CLASSIFICATION )
            log( "On spot/next date, credit limit before take Credit : " + limit2 )
            Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate.addDays( 1 ) )
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd )
            double limit3 = getAvailableCreditLimit( lpTpForDd, spotDate.addDays( 1 ), DAILY_SETTLEMENT_CLASSIFICATION )
            log( "On spot/next date, credit limit  after take Credit : " + limit3 + " success : " + cwm1.getStatus() )
            assertEquals( "before limit=" + limit2 + ",limit after=" + limit3, true, Math.abs( limit2 - limit3 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM )
            validateCreditUtilizationEvents( cwm1 )
        }
        catch ( Exception e )
        {
            e.printStackTrace()
            fail()
        }
    }

    void testDailySettlementNettingWithFXRateConversion()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 10000 )
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 )
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION )
            log( "Credit limit before first bid trade take Credit : " + limit0 )
            double tradeAmt = 1000
            Trade trade = prepareSingleLegTrade( tradeAmt, true, true, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd )
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION )
            double usedAmt1 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "EUR", tradeAmt, true )
            log( "Credit limit after first bid trade take Credit : " + limit1 + " success : " + cwm.getStatus() + ",utilizedAmt=" + usedAmt1 )
            assertEquals( "before limit=" + limit0 + ",limit after=" + limit1 + ",usedAmt=" + usedAmt1, true, Math.abs( limit0 - limit1 - usedAmt1 ) < CREDIT_CALCULATION_MINIMUM )
            validateCreditUtilizationEvents( cwm )

            // Now take an offer side trade.
            Trade trade1 = prepareSingleLegTrade( tradeAmt, false, true, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd )
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION )
            log( "Credit limit after second offer trade take Credit : " + limit2 + " success : " + cwm1.getStatus() )
            validateCreditUtilizationEvents( cwm1 )
            assertEquals( "before limit=" + limit1 + ",limit after=" + limit2, true, Math.abs( limit0 - limit2 ) < CREDIT_CALCULATION_MINIMUM )
        }
        catch ( Exception e )
        {
            e.printStackTrace()
            fail()
        }
    }

    void testDailySettlementNettingWithNoCreditLimitCurrency()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 10000 )
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 )
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION )
            log( "Credit limit before first bid trade take Credit : " + limit0 )
            double tradeAmt = 1000
            Trade trade = prepareSingleLegTrade( tradeAmt, true, true, EURGBP, lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd )
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION )
            double usedAmt1 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "EUR", tradeAmt, true )
            log( "Credit limit after first bid trade take Credit : " + limit1 + " success : " + cwm.getStatus() + ",utilizedAmt=" + usedAmt1 )
            printCurrencyPositions( cwm )
            assertEquals( "before limit=" + limit0 + ",limit after=" + limit1 + ",usedAmt=" + usedAmt1, true, Math.abs( limit0 - limit1 - usedAmt1 ) < CREDIT_CALCULATION_MINIMUM )
            validateCreditUtilizationEvents( cwm )

            // Now take an offer side trade.
            Trade trade1 = prepareSingleLegTrade( tradeAmt, false, true, EURGBP, lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd )
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION )
            log( "Credit limit after second offer trade take Credit : " + limit2 + " success : " + cwm1.getStatus() )
            printCurrencyPositions( cwm1 )
            validateCreditUtilizationEvents( cwm1 )
            assertEquals( "before limit=" + limit1 + ",limit after=" + limit2, true, Math.abs( limit0 - limit2 ) < CREDIT_CALCULATION_MINIMUM )
        }
        catch ( Exception e )
        {
            e.printStackTrace()
            fail()
        }
    }

    void testDailySettlementReserve()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 10000 )
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 )
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION )
            log( "Credit limit before first bid reserve Credit : " + limit0 )
            double tradeAmt = 1000
            Request request = prepareSingleLegRequest( prepareSingleLegTrade( tradeAmt, true, true, EURGBP, lpOrg, lpTpForDd, bidRates[0], spotDate ) )
            CreditWorkflowMessage cwm = creditMgr.reserveCredit( request, lpLe, ddOrg, lpTpForDd )
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION )
            double usedAmt1 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "EUR", tradeAmt, true )
            log( "Credit limit after first reserve Credit : " + limit1 + " success : " + cwm.getStatus() + ",utilizedAmt=" + usedAmt1 )
            printCurrencyPositions( cwm )
            assertEquals( "before limit=" + limit0 + ",limit after=" + limit1 + ",usedAmt=" + usedAmt1, true, Math.abs( limit0 - limit1 - usedAmt1 ) < CREDIT_CALCULATION_MINIMUM )
            assertEquals( "success=" + cwm.getStatusName(), cwm.getStatus().equals( MessageStatus.SUCCESS ), true )
            validateCreditUtilizationEvents( cwm )

            // now release the credit.
            CreditWorkflowMessage cwm1 = creditMgr.undoReserve( request, lpLe, ddOrg, lpTpForDd )
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION )
            log( "Credit limit after undoing reserve : " + limit2 + " success : " + cwm1.getStatus() )
            printCurrencyPositions( cwm1 )
            assertEquals( "Reserve amount should be zero. limit0=" + limit0 + ",limit2=" + limit2, true, Math.abs( limit2 - limit0 ) < CREDIT_CALCULATION_MINIMUM )
            assertEquals( "success=" + cwm1.getStatusName(), cwm1.getStatus().equals( MessageStatus.SUCCESS ), true )
        }
        catch ( Exception e )
        {
            e.printStackTrace()
            fail()
        }
    }

    void testDailySettlementReserveFailure()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 10000 )
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 )
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION )
            log( "Credit limit before first bid reserve Credit : " + limit0 )
            double tradeAmt = limit0 + 100000
            Request request = prepareSingleLegRequest( prepareSingleLegTrade( tradeAmt, true, true, EURGBP, lpOrg, lpTpForDd, bidRates[0], spotDate ) )
            CreditWorkflowMessage cwm = creditMgr.reserveCredit( request, lpLe, ddOrg, lpTpForDd )
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION )
            log( "Credit limit after first reserve Credit : " + limit1 + " success : " + cwm.getStatus() + ",before=" + limit0 )
            printCurrencyPositions( cwm )
            assertEquals( "before limit=" + limit0 + ",limit after=" + limit1, true, Math.abs( limit0 - limit1 ) < CREDIT_CALCULATION_MINIMUM )
            assertEquals( "success=" + cwm.getStatusName(), cwm.getStatus().equals( MessageStatus.SUCCESS ), false )
            validateCreditUtilizationEvents( cwm )
        }
        catch ( Exception e )
        {
            e.printStackTrace()
            fail()
        }
    }

    void testDailySettlementUndoCredit()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 10000 )
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 )

            // first do a trade to buy 1k USD vs EUR
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION )
            log( "Credit limit before first bid trade take Credit : " + limit1 )
            double tradeAmt = 1000
            Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd )
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION )
            log( "Credit limit after first bid trade take Credit : " + limit2 + " success : " + cwm1.getStatus() )
            assertEquals( "before limit=" + limit1 + ",limit after=" + limit2, true, Math.abs( limit1 - limit2 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM )
            assertEquals( "Message Status=" + cwm1.getStatusName(), cwm1.getStatus().equals( MessageStatus.SUCCESS ), true )
            validateCreditUtilizationEvents( cwm1 )

            // do another trade to sell 1k USD vs EUR. Now the net utilization should be zero.
            log( "Credit limit before second offer trade take Credit : " + limit2 )
            Trade trade2 = prepareSingleLegTrade( tradeAmt, false, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowMessage cwm2 = creditMgr.takeCredit( trade2, lpLe, ddOrg, lpTpForDd )
            double limit3 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION )
            log( "Credit limit after second offer trade take Credit : " + limit3 + " success : " + cwm2.getStatus() )
            assertEquals( "before limit=" + limit2 + ",limit after=" + limit3, true, Math.abs( limit3 - limit1 ) < CREDIT_CALCULATION_MINIMUM )
            assertEquals( "Message Status=" + cwm2.getStatusName(), cwm2.getStatus().equals( MessageStatus.SUCCESS ), true )
            validateCreditUtilizationEvents( cwm2 )

            // undo the last trade so that net utilization becomes that of first trade.
            CreditWorkflowMessage cwm3 = creditMgr.undoCredit( trade2, lpLe, ddOrg, lpTpForDd )
            double limit4 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION )
            log( "Credit limit after second offer trade undo Credit : " + limit4 + " success : " + cwm3.getStatus() )
            assertEquals( "before limit=" + limit3 + ",limit after=" + limit4, true, Math.abs( limit1 - limit4 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM )
            assertEquals( "Message Status=" + cwm3.getStatusName(), cwm3.getStatus().equals( MessageStatus.SUCCESS ), true )
            validateCreditUtilizationEvents( cwm3 )
        }
        catch ( Exception e )
        {
            e.printStackTrace()
            fail()
        }
    }

    void testDailySettlementMultipleTradesWithDifferentCurrenciesBuyTerm()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 10000 )
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 )

            // first do a trade to buy 1k GBP vs EUR
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION )
            log( "Credit limit before first bid trade take Credit : " + limit1 )
            double tradeAmt = 1000
            Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURGBP, lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd )
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION )
            log( "Credit limit after first bid trade take Credit : " + limit2 + " success : " + cwm1.getStatus() )

            // in this case net receivable would be EURO 1k
            double usedAmt1 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "GBP", getTradeAmount( trade1, "GBP" ), true )
            assertEquals( "before limit=" + limit1 + ",limit after=" + limit2 + ",usedAmt=" + usedAmt1, true, Math.abs( limit1 - limit2 - usedAmt1 ) < CREDIT_CALCULATION_MINIMUM )
            assertEquals( "Message Status=" + cwm1.getStatusName(), cwm1.getStatus().equals( MessageStatus.SUCCESS ), true )
            validateCreditUtilizationEvents( cwm1 )

            // do a trade to buy 1k JPY vs CHF
            double limit3 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION )
            log( "Credit limit before second bid trade take Credit : " + limit3 )
            Trade trade2 = prepareSingleLegTrade( tradeAmt, true, false, "CHF/JPY", lpOrg, lpTpForDd, bidRates[4], spotDate )
            CreditWorkflowMessage cwm2 = creditMgr.takeCredit( trade2, lpLe, ddOrg, lpTpForDd )
            double limit4 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION )

            // in this case, there will be two net receivables GBP xk and JPY xk.
            double usedAmt2 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "JPY", getTradeAmount( trade2, "JPY" ), true )
            log( "Credit limit after second bid trade take Credit : " + limit4 + " success : " + cwm2.getStatus() )
            assertEquals( "before limit=" + limit3 + ",limit after=" + limit4 + ",usedAmt=" + usedAmt2, true, Math.abs( limit3 - limit4 - usedAmt2 ) < CREDIT_CALCULATION_MINIMUM )
            assertEquals( "Message Status=" + cwm2.getStatusName(), cwm2.getStatus().equals( MessageStatus.SUCCESS ), true )
            validateCreditUtilizationEvents( cwm2 )

            // do a trade to buy 1k DKK vs AUD
            double limit5 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION )
            log( "Credit limit before second bid trade take Credit : " + limit5 )
            Trade trade3 = prepareSingleLegTrade( tradeAmt, true, false, "AUD/DKK", lpOrg, lpTpForDd, bidRates[4], spotDate )
            CreditWorkflowMessage cwm3 = creditMgr.takeCredit( trade3, lpLe, ddOrg, lpTpForDd )
            assertTrue( MessageStatus.SUCCESS.equals( cwm3.getStatus() ) )
       }
        catch ( Exception e )
        {
            e.printStackTrace()
            fail()
        }
    }

    void testDailySettlementMultipleTradesWithDifferentCurrenciesSellTerm()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 100000 )
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_LIMIT_CALCULATOR, 100000 )

            // first do a trade to sell 1k  GBP vs EUR
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION )
            log( "Credit limit before first bid trade take Credit : " + limit1 )
            double tradeAmt = 10000
            Trade trade1 = prepareSingleLegTrade( tradeAmt, false, false, EURGBP, lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd )
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION )
            log( "Credit limit after first bid trade take Credit : " + limit2 + " success : " + cwm1.getStatus() )

            // in this case net receivable would be EUR 1k
            double usedAmt1 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "EUR", getTradeAmount( trade1, "EUR" ), true )
            assertEquals( "before limit=" + limit1 + ",limit after=" + limit2 + ",usedAmt=" + usedAmt1, true, Math.abs( limit1 - limit2 - usedAmt1 ) < CREDIT_CALCULATION_MINIMUM )
            assertEquals( "Message Status=" + cwm1.getStatusName(), cwm1.getStatus().equals( MessageStatus.SUCCESS ), true )
            validateCreditUtilizationEvents( cwm1 )

            // do a trade to sell 1k JPY vs CHF
            double limit3 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION )
            log( "Credit limit before second bid trade take Credit : " + limit3 )
            Trade trade2 = prepareSingleLegTrade( tradeAmt, false, false, "CHF/JPY", lpOrg, lpTpForDd, bidRates[4], spotDate )
            CreditWorkflowMessage cwm2 = creditMgr.takeCredit( trade2, lpLe, ddOrg, lpTpForDd )
            double limit4 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION )

            // in this case, there will be two net receivables EUR 1k and CHF 1k.
            double usedAmt2 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "CHF", getTradeAmount( trade2, "CHF" ), true )
            log( "Credit limit after second bid trade take Credit : " + limit4 + " success : " + cwm2.getStatus() )
            assertEquals( "before limit=" + limit3 + ",limit after=" + limit4 + ",usedAmt=" + usedAmt2, true, Math.abs( limit3 - limit4 - usedAmt2 ) < CREDIT_CALCULATION_MINIMUM )
            assertEquals( "Message Status=" + cwm2.getStatusName(), cwm2.getStatus().equals( MessageStatus.SUCCESS ), true )
            validateCreditUtilizationEvents( cwm2 )

            // do a trade to sell 1k DKK vs AUD
            double limit5 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION )
            log( "Credit limit before second bid trade take Credit : " + limit5 )
            Trade trade3 = prepareSingleLegTrade( tradeAmt, false, false, "AUD/DKK", lpOrg, lpTpForDd, bidRates[4], spotDate )
            CreditWorkflowMessage cwm3 = creditMgr.takeCredit( trade3, lpLe, ddOrg, lpTpForDd )
            double limit6 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION )

            // in this case, there will be 3 receivable amounts EUR 1k, CHF 1k, and AUD 1k
            double usedAmt3 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "AUD", getTradeAmount( trade3, "AUD" ), true )
            log( "Credit limit after second bid trade take Credit : " + limit6 + " success : " + cwm3.getStatus() )
            assertEquals( "before limit=" + limit5 + ",limit after=" + limit6 + ",usedAmt=" + usedAmt3, true, Math.abs( limit5 - limit6 - usedAmt3 ) < CREDIT_CALCULATION_MINIMUM )
            assertEquals( "Message Status=" + cwm3.getStatusName(), cwm3.getStatus().equals( MessageStatus.SUCCESS ), true )
            validateCreditUtilizationEvents( cwm3 )
        }
        catch ( Exception e )
        {
            e.printStackTrace()
            fail()
        }
    }

    void testDailySettlementMultipleTradesWithDifferentCurrenciesBuyBase()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 10000 )
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 )

            // first do a trade to buy 1k EUR vs GBP
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION )
            log( "Credit limit before first bid trade take Credit : " + limit1 )
            double tradeAmt = 1000
            Trade trade1 = prepareSingleLegTrade( tradeAmt, true, true, EURGBP, lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd )
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION )
            log( "Credit limit after first bid trade take Credit : " + limit2 + " success : " + cwm1.getStatus() )

            // in this case net receivable would be EUR 1k
            double usedAmt1 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "EUR", tradeAmt, true )
            assertEquals( "before limit=" + limit1 + ",limit after=" + limit2 + ",usedAmt=" + usedAmt1, true, Math.abs( limit1 - limit2 - usedAmt1 ) < CREDIT_CALCULATION_MINIMUM )
            assertEquals( "Message Status=" + cwm1.getStatusName(), cwm1.getStatus().equals( MessageStatus.SUCCESS ), true )
            validateCreditUtilizationEvents( cwm1 )

            // do a trade to buy 1k CHF vs JPY
            double limit3 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION )
            log( "Credit limit before second bid trade take Credit : " + limit3 )
            Trade trade2 = prepareSingleLegTrade( tradeAmt, true, true, "CHF/JPY", lpOrg, lpTpForDd, bidRates[4], spotDate )
            CreditWorkflowMessage cwm2 = creditMgr.takeCredit( trade2, lpLe, ddOrg, lpTpForDd )
            double limit4 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION )

            // in this case, there will be two net receivables EUR 1k and CHF 1k.
            double usedAmt2 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "CHF", tradeAmt, true )
            log( "Credit limit after second bid trade take Credit : " + limit4 + " success : " + cwm2.getStatus() )
            assertEquals( "before limit=" + limit3 + ",limit after=" + limit4 + ",usedAmt=" + usedAmt2, true, Math.abs( limit3 - limit4 - usedAmt2 ) < CREDIT_CALCULATION_MINIMUM )
            assertEquals( "Message Status=" + cwm2.getStatusName(), cwm2.getStatus().equals( MessageStatus.SUCCESS ), true )
            validateCreditUtilizationEvents( cwm2 )

            // do a trade to buy 1k AUD vs DKK
            double limit5 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION )
            log( "Credit limit before second bid trade take Credit : " + limit5 )
            Trade trade3 = prepareSingleLegTrade( tradeAmt, true, true, "AUD/DKK", lpOrg, lpTpForDd, bidRates[4], spotDate )
            CreditWorkflowMessage cwm3 = creditMgr.takeCredit( trade3, lpLe, ddOrg, lpTpForDd )
            double limit6 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION )

            // in this case, there will be 3 receivable amounts EUR 1k, CHF 1k, and AUD 1k
            double usedAmt3 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "AUD", tradeAmt, true )
            log( "Credit limit after second bid trade take Credit : " + limit6 + " success : " + cwm3.getStatus() )
            assertEquals( "before limit=" + limit5 + ",limit after=" + limit6 + ",usedAmt=" + usedAmt3, true, Math.abs( limit5 - limit6 - usedAmt3 ) < CREDIT_CALCULATION_MINIMUM )
            assertEquals( "Message Status=" + cwm3.getStatusName(), cwm3.getStatus().equals( MessageStatus.SUCCESS ), true )
            validateCreditUtilizationEvents( cwm3 )
        }
        catch ( Exception e )
        {
            e.printStackTrace()
            fail()
        }
    }

    void testDailySettlementMultipleTradesWithDifferentCurrenciesSellBase()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 100000 )
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 100000 )

            // first do a trade to sell 1k EUR vs GBP
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION )
            log( "Credit limit before first bid trade take Credit : " + limit1 )
            double tradeAmt = 1000
            Trade trade1 = prepareSingleLegTrade( tradeAmt, false, true, EURGBP, lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd )
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION )
            log( "Credit limit after first bid trade take Credit : " + limit2 + " success : " + cwm1.getStatus() )

            // in this case net receivable would be USD 1k
            double usedAmt1 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "GBP", getTradeAmount( trade1, "GBP" ), true )
            assertEquals( "before limit=" + limit1 + ",limit after=" + limit2 + ",usedAmt=" + usedAmt1, true, Math.abs( limit1 - limit2 - usedAmt1 ) < CREDIT_CALCULATION_MINIMUM )
            assertEquals( "Message Status=" + cwm1.getStatusName(), cwm1.getStatus().equals( MessageStatus.SUCCESS ), true )
            validateCreditUtilizationEvents( cwm1 )

            // do a trade to sell 1k CHF vs JPY
            double limit3 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION )
            log( "Credit limit before second bid trade take Credit : " + limit3 )
            Trade trade2 = prepareSingleLegTrade( tradeAmt, false, true, "CHF/JPY", lpOrg, lpTpForDd, bidRates[4], spotDate )
            CreditWorkflowMessage cwm2 = creditMgr.takeCredit( trade2, lpLe, ddOrg, lpTpForDd )
            double limit4 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION )

            // in this case, there will be two net receivables GBP 1k and JPY 1k.
            double usedAmt2 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "JPY", getTradeAmount( trade2, "JPY" ), true )
            log( "Credit limit after second bid trade take Credit : " + limit4 + " success : " + cwm2.getStatus() )
            assertEquals( "before limit=" + limit3 + ",limit after=" + limit4 + ",usedAmt=" + usedAmt2, true, Math.abs( limit3 - limit4 - usedAmt2 ) < CREDIT_CALCULATION_MINIMUM )
            assertEquals( "Message Status=" + cwm2.getStatusName(), cwm2.getStatus().equals( MessageStatus.SUCCESS ), true )
            validateCreditUtilizationEvents( cwm2 )

            // do a trade to sell 1k AUD vs DKK
            double limit5 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION )
            log( "Credit limit before second bid trade take Credit : " + limit5 )
            Trade trade3 = prepareSingleLegTrade( tradeAmt, false, true, "AUD/DKK", lpOrg, lpTpForDd, bidRates[4], spotDate )
            CreditWorkflowMessage cwm3 = creditMgr.takeCredit( trade3, lpLe, ddOrg, lpTpForDd )
            double limit6 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION )

            // in this case, there will be 3 receivable amounts GBP 1k, JPY 1k, and DKK 1k
            double usedAmt3 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "DKK", getTradeAmount( trade3, "DKK" ), true )
            log( "Credit limit after second bid trade take Credit : " + limit6 + " success : " + cwm3.getStatus() )
            assertEquals( "before limit=" + limit5 + ",limit after=" + limit6 + ",usedAmt=" + usedAmt3, true, Math.abs( limit5 - limit6 - usedAmt3 ) < CREDIT_CALCULATION_MINIMUM )
            assertEquals( "Message Status=" + cwm3.getStatusName(), cwm3.getStatus().equals( MessageStatus.SUCCESS ), true )
            validateCreditUtilizationEvents( cwm3 )
        }
        catch ( Exception e )
        {
            e.printStackTrace()
            fail()
        }
    }

    void testDailySettlementMultipleTradesCancelsOut()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 100000 )
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 100000 )

            // first do a trade to buy 1k EUR vs GBP
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION )
            log( "Credit limit before first bid trade take Credit : " + limit1 )
            double tradeAmt = 1000
            Trade trade1 = prepareSingleLegTrade( tradeAmt, true, true, EURGBP, lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd )
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION )
            log( "Credit limit after first bid trade take Credit : " + limit2 + " success : " + cwm1.getStatus() )

            // in this case net receivable would be EURO 1k
            double usedAmt1 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "EUR", tradeAmt, true )
            assertEquals( "before limit=" + limit1 + ",limit after=" + limit2 + ",usedAmt=" + usedAmt1, true, Math.abs( limit1 - limit2 - usedAmt1 ) < CREDIT_CALCULATION_MINIMUM )
            assertEquals( "Message Status=" + cwm1.getStatusName(), cwm1.getStatus().equals( MessageStatus.SUCCESS ), true )
            validateCreditUtilizationEvents( cwm1 )

            // do a trade to buy 1k CHF vs JPY
            double limit3 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION )
            log( "Credit limit before second bid trade take Credit : " + limit3 )
            Trade trade2 = prepareSingleLegTrade( tradeAmt, true, true, "CHF/JPY", lpOrg, lpTpForDd, bidRates[4], spotDate )
            CreditWorkflowMessage cwm2 = creditMgr.takeCredit( trade2, lpLe, ddOrg, lpTpForDd )
            double limit4 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION )

            // in this case, there will be two net receivables EUR 1k and CHF 1k.
            double usedAmt2 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "CHF", tradeAmt, true )
            log( "Credit limit after second bid trade take Credit : " + limit4 + " success : " + cwm2.getStatus() )
            assertEquals( "before limit=" + limit3 + ",limit after=" + limit4 + ",usedAmt=" + usedAmt2, true, Math.abs( limit3 - limit4 - usedAmt2 ) < CREDIT_CALCULATION_MINIMUM )
            assertEquals( "Message Status=" + cwm2.getStatusName(), cwm2.getStatus().equals( MessageStatus.SUCCESS ), true )
            validateCreditUtilizationEvents( cwm2 )

            // do a trade to buy 1k AUD/DKK
            double limit5 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION )
            log( "Credit limit before third bid trade take Credit : " + limit5 )
            Trade trade3 = prepareSingleLegTrade( tradeAmt, true, true, "AUD/DKK", lpOrg, lpTpForDd, bidRates[4], spotDate )
            CreditWorkflowMessage cwm3 = creditMgr.takeCredit( trade3, lpLe, ddOrg, lpTpForDd )
            double limit6 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION )

            // in this case, there will be 3 receivable amounts EUR 1k, CHF 1k, and AUD 1k
            double usedAmt3 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "AUD", tradeAmt, true )
            log( "Credit limit after third bid trade take Credit : " + limit6 + " success : " + cwm3.getStatus() )
            assertEquals( "before limit=" + limit5 + ",limit after=" + limit6 + ",usedAmt=" + usedAmt3, true, Math.abs( limit5 - limit6 - usedAmt3 ) < CREDIT_CALCULATION_MINIMUM )
            assertEquals( "Message Status=" + cwm3.getStatusName(), cwm3.getStatus().equals( MessageStatus.SUCCESS ), true )
            validateCreditUtilizationEvents( cwm3 )

            // first do a trade to sell 1k EUR vs GBP
            double limit7 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION )
            log( "Credit limit before first bid trade take Credit : " + limit7 )
            Trade trade4 = prepareSingleLegTrade( tradeAmt, false, true, EURGBP, lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowMessage cwm4 = creditMgr.takeCredit( trade4, lpLe, ddOrg, lpTpForDd )
            double limit8 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION )
            log( "Credit limit after first bid trade take Credit : " + limit8 + " success : " + cwm4.getStatus() )

            // in this case net receivable would be EURO 1k
            double usedAmt4 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "EUR", tradeAmt, true )
            assertEquals( "before limit=" + limit7 + ",limit after=" + limit8 + ",usedAmt=" + usedAmt4, true, Math.abs( limit8 - limit7 - usedAmt4 ) < CREDIT_CALCULATION_MINIMUM )
            assertEquals( "Message Status=" + cwm4.getStatusName(), cwm4.getStatus().equals( MessageStatus.SUCCESS ), true )
            validateCreditUtilizationEvents( cwm4 )

            // do a trade to sell 1k CHF vs JPY
            double limit9 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION )
            log( "Credit limit before second bid trade take Credit : " + limit9 )
            Trade trade5 = prepareSingleLegTrade( tradeAmt, false, true, "CHF/JPY", lpOrg, lpTpForDd, bidRates[4], spotDate )
            CreditWorkflowMessage cwm5 = creditMgr.takeCredit( trade5, lpLe, ddOrg, lpTpForDd )
            double limit10 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION )

            // in this case, there will be two net receivables EUR 1k and CHF 1k.
            double usedAmt5 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "CHF", tradeAmt, true )
            log( "Credit limit after second bid trade take Credit : " + limit10 + " success : " + cwm5.getStatus() )
            assertEquals( "before limit=" + limit9 + ",limit after=" + limit10 + ",usedAmt=" + usedAmt5, true, Math.abs( limit10 - limit9 - usedAmt5 ) < CREDIT_CALCULATION_MINIMUM )
            assertEquals( "Message Status=" + cwm5.getStatusName(), cwm5.getStatus().equals( MessageStatus.SUCCESS ), true )
            validateCreditUtilizationEvents( cwm5 )

            // do a trade to sell 1k AUD vs DKK
            double limit11 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION )
            log( "Credit limit before second bid trade take Credit : " + limit11 )
            Trade trade6 = prepareSingleLegTrade( tradeAmt, false, true, "AUD/DKK", lpOrg, lpTpForDd, bidRates[4], spotDate )
            CreditWorkflowMessage cwm6 = creditMgr.takeCredit( trade6, lpLe, ddOrg, lpTpForDd )
            double limit12 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION )

            // in this case, all the net receivables got cancelled due to opposite direction trades.
            log( "Credit limit after second bid trade take Credit : " + limit12 + " success : " + cwm6.getStatus() )
            assertEquals( "before limit=" + limit11 + ",limit after=" + limit12, true, Math.abs( limit12 - limit1 ) < CREDIT_CALCULATION_MINIMUM )
            assertEquals( "Message Status=" + cwm6.getStatusName(), cwm6.getStatus().equals( MessageStatus.SUCCESS ), true )
            validateCreditUtilizationEvents( cwm6 )
        }
        catch ( Exception e )
        {
            e.printStackTrace()
            fail()
        }
    }

    void testSimpleNOPNetting()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, NET_OPEN_POSITION_CALCULATOR, 10000 )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 10000 )
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "Credit limit before first bid trade take Credit : " + limit0 )
            double tradeAmt = 1000
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate )

            // in this case, check the net receivable and net payable.
            double eurAmt1 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "EUR", tradeAmt / bidRates[0], true )
            double utilizedAmt = Math.abs( eurAmt1 ) > Math.abs( tradeAmt ) ? Math.abs( eurAmt1 ) : Math.abs( tradeAmt )

            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd )
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "Credit limit after first bid trade take Credit : " + limit1 + " success : " + cwm.getStatus() )
            assertEquals( "before limit=" + limit0 + ",limitAfter=" + limit1, true, Math.abs( limit0 - limit1 - utilizedAmt ) < CREDIT_CALCULATION_MINIMUM )
            validateCreditUtilizationEvents( cwm )

            // Now take an offer side trade.
            Trade trade1 = prepareSingleLegTrade( tradeAmt, false, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd )
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "Credit limit after second offer trade take Credit : " + limit2 + " success : " + cwm1.getStatus() )
            validateCreditUtilizationEvents( cwm1 )
            assertEquals( "before limit=" + limit1 + ",after limit=" + limit2, true, Math.abs( limit0 - limit2 ) < CREDIT_CALCULATION_MINIMUM )
        }
        catch ( Exception e )
        {
            e.printStackTrace()
            fail()
        }
    }

    void testNOPNettingWithDifferentValueDates()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 )
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, NET_OPEN_POSITION_CALCULATOR, 10000 )
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "On Spot date, credit limit before take Credit : " + limit0 )
            double tradeAmt = 1000
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd )
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )

            // in this case, check the net receivable and net payable.
            double eurAmt1 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "EUR", tradeAmt / bidRates[0], true )
            double utilizedAmt = Math.abs( eurAmt1 ) > Math.abs( tradeAmt ) ? Math.abs( eurAmt1 ) : Math.abs( tradeAmt )

            log( "On spot date, credit limit after take Credit : " + limit1 + " success : " + cwm.getStatus() )
            assertEquals( "before limit=" + limit0 + ",limit after=" + limit1, true, Math.abs( limit0 - limit1 - utilizedAmt ) < CREDIT_CALCULATION_MINIMUM )
            validateCreditUtilizationEvents( cwm )

            // Now do an offer trade on spot next date
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate.addDays( 1 ), GROSS_NOTIONAL_CLASSIFICATION )
            log( "On spot/next date, credit limit before take Credit : " + limit2 )
            Trade trade1 = prepareSingleLegTrade( tradeAmt, false, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate.addDays( 1 ) )
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd )
            double limit3 = getAvailableCreditLimit( lpTpForDd, spotDate.addDays( 1 ), GROSS_NOTIONAL_CLASSIFICATION )
            log( "On spot/next date, credit limit  after take Credit : " + limit3 + " success : " + cwm1.getStatus() )
            assertEquals( "before limit=" + limit2 + ",limit after=" + limit3, true, Math.abs( limit3 - limit0 ) < CREDIT_CALCULATION_MINIMUM )
            validateCreditUtilizationEvents( cwm1 )
        }
        catch ( Exception e )
        {
            e.printStackTrace()
            fail()
        }
    }

    void testNOPNettingWithFXRateConversion()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 )
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, NET_OPEN_POSITION_CALCULATOR, 10000 )
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "Credit limit before first bid trade take Credit : " + limit0 )
            double tradeAmt = 1000
            Trade trade = prepareSingleLegTrade( tradeAmt, true, true, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd )
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )

            // in this case, check the net receivable and net payable.
            double usdAmt1 = tradeAmt * bidRates[0]
            double eurAmt1 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "EUR", tradeAmt, true )
            double utilizedAmt = Math.abs( eurAmt1 ) > Math.abs( usdAmt1 ) ? Math.abs( eurAmt1 ) : Math.abs( usdAmt1 )
            log( "Credit limit after first bid trade take Credit : " + limit1 + " success : " + cwm.getStatus() + ",utilizedAmt=" + utilizedAmt )
            assertEquals( "before limit=" + limit0 + ",limit after=" + limit1 + ",usedAmt=" + utilizedAmt, true, Math.abs( limit0 - limit1 - utilizedAmt ) < CREDIT_CALCULATION_MINIMUM )
            validateCreditUtilizationEvents( cwm )

            // Now take an offer side trade.
            Trade trade1 = prepareSingleLegTrade( tradeAmt, false, true, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd )
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "Credit limit after second offer trade take Credit : " + limit2 + " success : " + cwm1.getStatus() )
            validateCreditUtilizationEvents( cwm1 )
            assertEquals( "before limit=" + limit1 + ",limit after=" + limit2, true, Math.abs( limit0 - limit2 ) < CREDIT_CALCULATION_MINIMUM )
        }
        catch ( Exception e )
        {
            e.printStackTrace()
            fail()
        }
    }

    void testNOPNettingWithNoCreditLimitCurrency()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 )
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, NET_OPEN_POSITION_CALCULATOR, 10000 )
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "Credit limit before first bid trade take Credit : " + limit0 )
            double tradeAmt = 1000
            Trade trade = prepareSingleLegTrade( tradeAmt, true, true, EURGBP, lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd )
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )

            // in this case, check the net receivable and net payable.
            double gbpAmt1 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "GBP", tradeAmt * bidRates[0], false )
            double eurAmt1 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "EUR", tradeAmt, true )
            double utilizedAmt = Math.abs( eurAmt1 ) > Math.abs( gbpAmt1 ) ? Math.abs( eurAmt1 ) : Math.abs( gbpAmt1 )

            log( "Credit limit after first bid trade take Credit : " + limit1 + " success : " + cwm.getStatus() + ",utilizedAmt=" + utilizedAmt )
            printCurrencyPositions( cwm )
            assertEquals( "before limit=" + limit0 + ",limit after=" + limit1 + ",usedAmt=" + utilizedAmt, true, Math.abs( limit0 - limit1 - utilizedAmt ) < CREDIT_CALCULATION_MINIMUM )
            validateCreditUtilizationEvents( cwm )

            // Now take an offer side trade.
            Trade trade1 = prepareSingleLegTrade( tradeAmt, false, true, EURGBP, lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd )
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "Credit limit after second offer trade take Credit : " + limit2 + " success : " + cwm1.getStatus() )
            printCurrencyPositions( cwm1 )
            validateCreditUtilizationEvents( cwm1 )
            assertEquals( "before limit=" + limit1 + ",limit after=" + limit2, true, Math.abs( limit0 - limit2 ) < CREDIT_CALCULATION_MINIMUM )
        }
        catch ( Exception e )
        {
            e.printStackTrace()
            fail()
        }
    }

    void testNOPReserve()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 )
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, NET_OPEN_POSITION_CALCULATOR, 10000 )
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "Credit limit before first bid reserve Credit : " + limit0 )
            double tradeAmt = 1000
            Request request = prepareSingleLegRequest( prepareSingleLegTrade( tradeAmt, true, true, EURGBP, lpOrg, lpTpForDd, bidRates[0], spotDate ) )
            CreditWorkflowMessage cwm = creditMgr.reserveCredit( request, lpLe, ddOrg, lpTpForDd )
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            double usedAmt1 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "EUR", tradeAmt, true )
            log( "Credit limit after first reserve Credit : " + limit1 + " success : " + cwm.getStatus() + ",utilizedAmt=" + usedAmt1 )
            printCurrencyPositions( cwm )
            assertEquals( "before limit=" + limit0 + ",limit after=" + limit1 + ",usedAmt=" + usedAmt1, true, Math.abs( limit0 - limit1 - usedAmt1 ) < CREDIT_CALCULATION_MINIMUM )
            assertEquals( "success=" + cwm.getStatusName(), cwm.getStatus().equals( MessageStatus.SUCCESS ), true )
            validateCreditUtilizationEvents( cwm )

            // now release the credit.
            CreditWorkflowMessage cwm1 = creditMgr.undoReserve( request, lpLe, ddOrg, lpTpForDd )
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "Credit limit after undoing reserve : " + limit2 + " success : " + cwm1.getStatus() )
            printCurrencyPositions( cwm1 )
            assertEquals( "Reserve amount should be zero. ", true, Math.abs( limit2 - limit0 ) < CREDIT_CALCULATION_MINIMUM )
            assertEquals( "success=" + cwm1.getStatusName(), cwm1.getStatus().equals( MessageStatus.SUCCESS ), true )
        }
        catch ( Exception e )
        {
            e.printStackTrace()
            fail()
        }
    }

    void testNOPReserveFailure()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 )
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, NET_OPEN_POSITION_CALCULATOR, 10000 )
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "Credit limit before first bid reserve Credit : " + limit0 )
            double tradeAmt = limit0 + 100000
            Request request = prepareSingleLegRequest( prepareSingleLegTrade( tradeAmt, true, true, EURGBP, lpOrg, lpTpForDd, bidRates[0], spotDate ) )
            CreditWorkflowMessage cwm = creditMgr.reserveCredit( request, lpLe, ddOrg, lpTpForDd )
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "Credit limit after first reserve Credit : " + limit1 + " success : " + cwm.getStatus() + ",before=" + limit0 )
            printCurrencyPositions( cwm )
            assertEquals( "before limit=" + limit0 + ",limit after=" + limit1, true, Math.abs( limit0 - limit1 ) < CREDIT_CALCULATION_MINIMUM )
            assertEquals( "success=" + cwm.getStatusName(), cwm.getStatus().equals( MessageStatus.SUCCESS ), false )
            validateCreditUtilizationEvents( cwm )
        }
        catch ( Exception e )
        {
            e.printStackTrace()
            fail()
        }
    }

    void testNOPUndoCredit()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, NET_OPEN_POSITION_CALCULATOR, 10000 )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 )

            // first do a trade to buy 1k USD vs EUR
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "Credit limit before first bid trade take Credit : " + limit1 )
            double tradeAmt = 1000
            Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd )
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )

            // in this case, check the net receivable and net payable.
            double eurAmt1 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "EUR", tradeAmt / bidRates[0], true )
            double utilizedAmt = Math.abs( eurAmt1 ) > Math.abs( tradeAmt ) ? Math.abs( eurAmt1 ) : Math.abs( tradeAmt )

            log( "Credit limit after first bid trade take Credit : " + limit2 + " success : " + cwm.getStatus() + ",utilizedAmt=" + utilizedAmt )
            printCurrencyPositions( cwm )
            assertEquals( "before limit=" + limit1 + ",limit after=" + limit2 + ",usedAmt=" + utilizedAmt, true, Math.abs( limit1 - limit2 - utilizedAmt ) < CREDIT_CALCULATION_MINIMUM )
            validateCreditUtilizationEvents( cwm )

            // Now take an offer side trade.
            Trade trade2 = prepareSingleLegTrade( tradeAmt, false, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade2, lpLe, ddOrg, lpTpForDd )
            double limit3 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "Credit limit after second offer trade take Credit : " + limit3 + " success : " + cwm1.getStatus() )
            printCurrencyPositions( cwm1 )
            validateCreditUtilizationEvents( cwm1 )
            assertEquals( "before limit=" + limit2 + ",limit after=" + limit3, true, Math.abs( limit1 - limit3 ) < CREDIT_CALCULATION_MINIMUM )

            // undo the last trade so that net utilization becomes that of first trade.
            CreditWorkflowMessage cwm3 = creditMgr.undoCredit( trade2, lpLe, ddOrg, lpTpForDd )
            double limit4 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "Credit limit after second offer trade undo Credit : " + limit4 + " success : " + cwm3.getStatus() )
            assertEquals( "before limit=" + limit3 + ",limit after=" + limit4, true, Math.abs( limit1 - limit2 - utilizedAmt ) < CREDIT_CALCULATION_MINIMUM )
            assertEquals( "Message Status=" + cwm3.getStatusName(), cwm3.getStatus().equals( MessageStatus.SUCCESS ), true )
            validateCreditUtilizationEvents( cwm3 )
        }
        catch ( Exception e )
        {
            e.printStackTrace()
            fail()
        }
    }

    void testSimpleAggregateNetPRSettlementNetting()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )

            Boolean dailyPLAtCptyLevel = creditAdminSvc.isDailyPL( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION )
            Boolean dailyPLAtOrgLevel = creditAdminSvc.isDefaultDailyPL( lpOrg )
            if ( ( dailyPLAtCptyLevel != null && dailyPLAtCptyLevel ) || ( dailyPLAtOrgLevel != null && dailyPLAtOrgLevel ) )
            {
                log( "Skipping test as daily PL is enabled. TestName=testSimpleAggregateNetPRSettlementNetting" )
                return
            }

            Currency limitCcy = CreditUtilC.getCounterpartyCreditLimitCurrency( lpOrg, ddOrg, lpTpForDd )
            if ( !limitCcy.isSameAs( CurrencyFactory.getCurrency( "USD" ) ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( lpOrg, CurrencyFactory.getCurrency( "USD" ) )
                creditAdminSvc.reinitialize( lpOrg )
            }
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_NPR_SETTLEMENT_CALCULATOR, 10000 )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 10000 )
            creditAdminSvc.setIgnoreCurrDatePositions( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, true )
            double tradeDateLimit0 = getAvailableCreditLimit( lpTpForDd, tradeDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "Credit limit before first bid trade take Credit : " + tradeDateLimit0 )
            double tradeAmt = 1000

            // test trade on trade date. This should not have any impact on the utilization.
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], tradeDate )
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd )
            double tradeDateLimit1 = getAvailableCreditLimit( lpTpForDd, tradeDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "Credit limit after first bid trade take Credit on trade date : " + tradeDateLimit1 + " success : " + cwm.getStatus() )
            assertEquals( "before limit=" + tradeDateLimit0 + ",limitAfter=" + tradeDateLimit1, true, Math.abs( tradeDateLimit0 - tradeDateLimit1 ) < CREDIT_CALCULATION_MINIMUM )
            validateCreditUtilizationEvents( cwm )

            // now do a trade in spot date and it should have only non-limit currency positions taken for utilization.
            double spotDateLimit0 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate )
            double utilizedAmt = getLimitCurrencyAmount( lpOrg, lpTpForDd, "EUR", tradeAmt / bidRates[0], true )

            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd )
            double spotDateLimit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "Credit limit after first bid trade take Credit : " + spotDateLimit1 + " success : " + cwm1.getStatus() )
            assertEquals( "before limit=" + spotDateLimit0 + ",limitAfter=" + spotDateLimit1, true, Math.abs( spotDateLimit0 - spotDateLimit1 - utilizedAmt ) < CREDIT_CALCULATION_MINIMUM )
            validateCreditUtilizationEvents( cwm1 )

            // Now take an offer side trade. This should put back the earlier EUR amount and result in no utilization at all.
            Trade trade2 = prepareSingleLegTrade( tradeAmt, false, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowMessage cwm2 = creditMgr.takeCredit( trade2, lpLe, ddOrg, lpTpForDd )
            double spotDateLimit2 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "Credit limit after second offer trade take Credit : " + spotDateLimit2 + " success : " + cwm2.getStatus() )
            validateCreditUtilizationEvents( cwm2 )
            assertEquals( "before limit=" + spotDateLimit1 + ",after limit=" + spotDateLimit2, true, Math.abs( spotDateLimit0 - spotDateLimit2 ) < CREDIT_CALCULATION_MINIMUM )

            // for spot date and non-limit currency trade it would just act same as NOP.
            Trade trade3 = prepareSingleLegTrade( tradeAmt, true, false, EURGBP, lpOrg, lpTpForDd, bidRates[0], spotDate )

            // in this case, check the net receivable and net payable.
            double eurAmt3 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "EUR", tradeAmt / bidRates[0], true )
            double gbpAmt3 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "GBP", tradeAmt, true )
            double utilizedAmt3 = Math.abs( eurAmt3 ) + Math.abs( gbpAmt3 )

            CreditWorkflowMessage cwm3 = creditMgr.takeCredit( trade3, lpLe, ddOrg, lpTpForDd )
            double spotDateLimit3 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "Credit limit after first bid trade take Credit : " + spotDateLimit3 + " success : " + cwm.getStatus() )
            assertEquals( "before limit=" + spotDateLimit2 + ",limitAfter=" + spotDateLimit3, true, Math.abs( spotDateLimit2 - spotDateLimit3 - utilizedAmt3 ) < CREDIT_CALCULATION_MINIMUM )
            validateCreditUtilizationEvents( cwm3 )

            // Now take an offer side trade.
            Trade trade4 = prepareSingleLegTrade( tradeAmt, false, false, EURGBP, lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowMessage cwm4 = creditMgr.takeCredit( trade4, lpLe, ddOrg, lpTpForDd )
            double spotDateLimit4 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "Credit limit after second offer trade take Credit : " + spotDateLimit4 + " success : " + cwm4.getStatus() )
            validateCreditUtilizationEvents( cwm4 )
            assertEquals( "before limit=" + spotDateLimit3 + ",after limit=" + spotDateLimit4, true, Math.abs( spotDateLimit2 - spotDateLimit4 ) < CREDIT_CALCULATION_MINIMUM )
        }
        catch ( Exception e )
        {
            e.printStackTrace()
            fail()
        }
    }

    void testAggregateNetPRSettlementWithDifferentValueDates()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )
            Currency limitCcy = CreditUtilC.getCounterpartyCreditLimitCurrency( lpOrg, ddOrg, lpTpForDd )
            if ( !limitCcy.isSameAs( CurrencyFactory.getCurrency( "USD" ) ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( lpOrg, CurrencyFactory.getCurrency( "USD" ) )
                creditAdminSvc.reinitialize( lpOrg )
            }
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 )
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_NPR_SETTLEMENT_CALCULATOR, 10000 )

            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "On Spot date, credit limit before take Credit : " + limit0 )
            double tradeAmt = 1000
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd )
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )

            // in this case, check the net receivable and net payable.
            double utilizedAmt = getLimitCurrencyAmount( lpOrg, lpTpForDd, "EUR", tradeAmt / bidRates[0], true )

            log( "On spot date, credit limit after take Credit : " + limit1 + " success : " + cwm.getStatus() )
            assertEquals( "before limit=" + limit0 + ",limit after=" + limit1, true, Math.abs( limit0 - limit1 - utilizedAmt ) < CREDIT_CALCULATION_MINIMUM )
            validateCreditUtilizationEvents( cwm )

            // Now do an offer trade on spot next date
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate.addDays( 1 ), GROSS_NOTIONAL_CLASSIFICATION )
            log( "On spot/next date, credit limit before take Credit : " + limit2 )
            Trade trade1 = prepareSingleLegTrade( tradeAmt, false, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate.addDays( 1 ) )
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd )
            double limit3 = getAvailableCreditLimit( lpTpForDd, spotDate.addDays( 1 ), GROSS_NOTIONAL_CLASSIFICATION )
            log( "On spot/next date, credit limit  after take Credit : " + limit3 + " success : " + cwm1.getStatus() )
            assertEquals( "before limit=" + limit2 + ",limit after=" + limit3, true, Math.abs( limit3 - limit0 ) < CREDIT_CALCULATION_MINIMUM )
            validateCreditUtilizationEvents( cwm1 )
        }
        catch ( Exception e )
        {
            e.printStackTrace()
            fail()
        }
    }

    void testAggregateNetPRSettlementWithFXRateConversion()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )
            Currency limitCcy = CreditUtilC.getCounterpartyCreditLimitCurrency( lpOrg, ddOrg, lpTpForDd )
            if ( !limitCcy.isSameAs( CurrencyFactory.getCurrency( "USD" ) ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( lpOrg, CurrencyFactory.getCurrency( "USD" ) )
                creditAdminSvc.reinitialize( lpOrg )
            }
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 )
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_NPR_SETTLEMENT_CALCULATOR, 10000 )

            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "Credit limit before first bid trade take Credit : " + limit0 )
            double tradeAmt = 1000
            Trade trade = prepareSingleLegTrade( tradeAmt, true, true, "GBP/JPY", lpOrg, lpTpForDd, bidRates[4], spotDate )
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd )
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )

            // in this case, check the net receivable and net payable.
            double gbpAmt1 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "GBP", tradeAmt, true )
            double jpyAmt1 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "JPY", tradeAmt * bidRates[4], false )
            double utilizedAmt = Math.abs( jpyAmt1 ) + Math.abs( gbpAmt1 )
            log( "Credit limit after first bid trade take Credit : " + limit1 + " success : " + cwm.getStatus() + ",utilizedAmt=" + utilizedAmt )
            assertEquals( "before limit=" + limit0 + ",limit after=" + limit1 + ",usedAmt=" + utilizedAmt, true, Math.abs( limit0 - limit1 - utilizedAmt ) < CREDIT_CALCULATION_MINIMUM )
            validateCreditUtilizationEvents( cwm )

            // Now take an offer side trade.
            Trade trade1 = prepareSingleLegTrade( tradeAmt, false, true, "GBP/JPY", lpOrg, lpTpForDd, bidRates[4], spotDate )
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd )
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "Credit limit after second offer trade take Credit : " + limit2 + " success : " + cwm1.getStatus() )
            validateCreditUtilizationEvents( cwm1 )
            assertEquals( "before limit=" + limit1 + ",limit after=" + limit2, true, Math.abs( limit0 - limit2 ) < CREDIT_CALCULATION_MINIMUM )

            // Now take trade date trade.
            double tradeDateLimit0 = getAvailableCreditLimit( lpTpForDd, tradeDate, GROSS_NOTIONAL_CLASSIFICATION )
            Trade trade2 = prepareSingleLegTrade( tradeAmt, false, true, "GBP/JPY", lpOrg, lpTpForDd, bidRates[4], tradeDate )
            CreditWorkflowMessage cwm2 = creditMgr.takeCredit( trade2, lpLe, ddOrg, lpTpForDd )
            double tradeDateLimit1 = getAvailableCreditLimit( lpTpForDd, tradeDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "Credit limit after second offer trade take Credit : " + tradeDateLimit1 + " success : " + cwm2.getStatus() )
            validateCreditUtilizationEvents( cwm2 )
            assertEquals( "before limit=" + tradeDateLimit0 + ",limit after=" + limit2, true, Math.abs( limit0 - limit2 ) < CREDIT_CALCULATION_MINIMUM )

        }
        catch ( Exception e )
        {
            e.printStackTrace()
            fail()
        }
    }

    void testAggregateNetPRSettlementWithCreditLimitCurrency()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )
            Currency limitCcy = CreditUtilC.getCounterpartyCreditLimitCurrency( lpOrg, ddOrg, lpTpForDd )
            if ( !limitCcy.isSameAs( CurrencyFactory.getCurrency( "USD" ) ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( lpOrg, CurrencyFactory.getCurrency( "USD" ) )
                creditAdminSvc.reinitialize( lpOrg )
            }
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 100000 )
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_NPR_SETTLEMENT_CALCULATOR, 100000 )

            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "Credit limit before first bid trade take Credit : " + limit0 )
            double tradeAmt = 10000
            Trade trade = prepareSingleLegTrade( tradeAmt, true, true, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd )
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )

            // in this case, check the net receivable and net payable.
            double utilizedAmt1 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "EUR", tradeAmt, true )
            log( "Credit limit after first bid trade take Credit : " + limit1 + " success : " + cwm.getStatus() + ",utilizedAmt=" + utilizedAmt1 )
            assertEquals( "before limit=" + limit0 + ",limit after=" + limit1 + ",usedAmt=" + utilizedAmt1, true, Math.abs( limit0 - limit1 - utilizedAmt1 ) < CREDIT_CALCULATION_MINIMUM )
            validateCreditUtilizationEvents( cwm )

            // Now take another trade on GBP/USD. In this case GBP amount falls on to net payable.
            Trade trade1 = prepareSingleLegTrade( tradeAmt, false, true, "GBP/USD", lpOrg, lpTpForDd, bidRates[1], spotDate )
            double gbpAmt = getLimitCurrencyAmount( lpOrg, lpTpForDd, "GBP", tradeAmt, false )
            double utilizedAmt2 = Math.abs( utilizedAmt1 ) + Math.abs( gbpAmt )
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd )
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "Credit limit after second offer trade take Credit : " + limit2 + " success : " + cwm1.getStatus() )
            validateCreditUtilizationEvents( cwm1 )
            assertEquals( "before limit=" + limit1 + ",limit after=" + limit2 + ",gbpAmt=" + gbpAmt + ",eurAmt=" + utilizedAmt1, true, Math.abs( limit0 - limit2 - utilizedAmt2 ) < CREDIT_CALCULATION_MINIMUM )
        }
        catch ( Exception e )
        {
            e.printStackTrace()
            fail()
        }
    }

    void testAggregateNetPRSettlementReserve()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )
            Currency limitCcy = CreditUtilC.getCounterpartyCreditLimitCurrency( lpOrg, ddOrg, lpTpForDd )
            if ( !limitCcy.isSameAs( CurrencyFactory.getCurrency( "USD" ) ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( lpOrg, CurrencyFactory.getCurrency( "USD" ) )
                creditAdminSvc.reinitialize( lpOrg )
            }
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 )
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_NPR_SETTLEMENT_CALCULATOR, 10000 )

            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "Credit limit before first bid reserve Credit : " + limit0 )
            double tradeAmt = 1000
            Request request = prepareSingleLegRequest( prepareSingleLegTrade( tradeAmt, true, true, EURGBP, lpOrg, lpTpForDd, bidRates[0], spotDate ) )
            CreditWorkflowMessage cwm = creditMgr.reserveCredit( request, lpLe, ddOrg, lpTpForDd )
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            double usedAmt1 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "EUR", tradeAmt, true )
            log( "Credit limit after first reserve Credit : " + limit1 + " success : " + cwm.getStatus() + ",utilizedAmt=" + usedAmt1 )
            printCurrencyPositions( cwm )
            assertEquals( "before limit=" + limit0 + ",limit after=" + limit1 + ",usedAmt=" + usedAmt1, true, Math.abs( limit0 - limit1 - usedAmt1 ) < CREDIT_CALCULATION_MINIMUM )
            assertEquals( "success=" + cwm.getStatusName(), cwm.getStatus().equals( MessageStatus.SUCCESS ), true )
            validateCreditUtilizationEvents( cwm )

            // now release the credit.
            CreditWorkflowMessage cwm1 = creditMgr.undoReserve( request, lpLe, ddOrg, lpTpForDd )
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "Credit limit after undoing reserve : " + limit2 + " success : " + cwm1.getStatus() )
            printCurrencyPositions( cwm1 )
            assertEquals( "Reserve amount should be zero. ", true, Math.abs( limit2 - limit0 ) < CREDIT_CALCULATION_MINIMUM )
            assertEquals( "success=" + cwm1.getStatusName(), cwm1.getStatus().equals( MessageStatus.SUCCESS ), true )
        }
        catch ( Exception e )
        {
            e.printStackTrace()
            fail()
        }
    }

    void testAggregateNetPRSettlementReserveFailure()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )
            Currency limitCcy = CreditUtilC.getCounterpartyCreditLimitCurrency( lpOrg, ddOrg, lpTpForDd )
            if ( !limitCcy.isSameAs( CurrencyFactory.getCurrency( "USD" ) ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( lpOrg, CurrencyFactory.getCurrency( "USD" ) )
                creditAdminSvc.reinitialize( lpOrg )
            }
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 )
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_NPR_SETTLEMENT_CALCULATOR, 10000 )

            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "Credit limit before first bid reserve Credit : " + limit0 )
            double tradeAmt = limit0 + 100000
            Request request = prepareSingleLegRequest( prepareSingleLegTrade( tradeAmt, true, true, EURGBP, lpOrg, lpTpForDd, bidRates[0], spotDate ) )
            CreditWorkflowMessage cwm = creditMgr.reserveCredit( request, lpLe, ddOrg, lpTpForDd )
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "Credit limit after first reserve Credit : " + limit1 + " success : " + cwm.getStatus() + ",before=" + limit0 )
            printCurrencyPositions( cwm )
            assertEquals( "before limit=" + limit0 + ",limit after=" + limit1, true, Math.abs( limit0 - limit1 ) < CREDIT_CALCULATION_MINIMUM )
            assertEquals( "success=" + cwm.getStatusName(), cwm.getStatus().equals( MessageStatus.SUCCESS ), false )
            validateCreditUtilizationEvents( cwm )
        }
        catch ( Exception e )
        {
            e.printStackTrace()
            fail()
        }
    }

    void testSimpleAggregateLimitNetting()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 )
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_LIMIT_CALCULATOR, 10000 )
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "Credit limit before first bid trade take Credit : " + limit0 )
            double tradeAmt = 1000
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate )

            // in this case, check the net receivable and net payable.
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd )
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "Credit limit after first bid trade take Credit : " + limit1 + " success : " + cwm.getStatus() )
            assertEquals( "before limit=" + limit0 + ",limitAfter=" + limit1, true, Math.abs( limit0 - limit1 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM )
            validateCreditUtilizationEvents( cwm )

            // Now take an offer side trade.
            Trade trade1 = prepareSingleLegTrade( tradeAmt, false, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd )
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "Credit limit after second offer trade take Credit : " + limit2 + " success : " + cwm1.getStatus() )
            validateCreditUtilizationEvents( cwm1 )
            assertEquals( "before limit=" + limit1 + ",after limit=" + limit2, true, Math.abs( limit0 - limit2 ) < CREDIT_CALCULATION_MINIMUM )
        }
        catch ( Exception e )
        {
            e.printStackTrace()
            fail()
        }
    }

    void testAggregateLimitWithDifferentValueDates()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 )
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_LIMIT_CALCULATOR, 10000 )
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "Credit limit before first bid trade take Credit : " + limit0 )
            double tradeAmt = 1000
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate )

            // in this case, check the net receivable and net payable.

            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd )
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "Credit limit after first bid trade take Credit : " + limit1 + " success : " + cwm.getStatus() )
            assertEquals( "before limit=" + limit0 + ",limitAfter=" + limit1, true, Math.abs( limit0 - limit1 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM )
            validateCreditUtilizationEvents( cwm )

            // Now take another bid trade on the spot next day.
            Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate.addDays( 1 ) )
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd )
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "Credit limit after second bid trade take Credit : " + limit2 + " success : " + cwm1.getStatus() )
            validateCreditUtilizationEvents( cwm1 )
            assertEquals( "before limit=" + limit1 + ",after limit=" + limit2, true, Math.abs( limit1 - limit2 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM )

            // Now take an offer trade on the spot next day. This will remove the net receivable position in spot/next day.
            Trade trade2 = prepareSingleLegTrade( tradeAmt, false, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate.addDays( 1 ) )
            CreditWorkflowMessage cwm2 = creditMgr.takeCredit( trade2, lpLe, ddOrg, lpTpForDd )
            double limit3 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "Credit limit after third offer trade take Credit : " + limit3 + " success : " + cwm2.getStatus() )
            validateCreditUtilizationEvents( cwm2 )
            assertEquals( "before limit=" + limit2 + ",after limit=" + limit3, true, Math.abs( limit3 - limit1 ) < CREDIT_CALCULATION_MINIMUM )

            // Now take an offer trade on the spot day. This will remove the net receivable position in spot day also.
            Trade trade3 = prepareSingleLegTrade( tradeAmt, false, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowMessage cwm3 = creditMgr.takeCredit( trade3, lpLe, ddOrg, lpTpForDd )
            double limit4 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "Credit limit after fourth offer trade take Credit : " + limit4 + " success : " + cwm3.getStatus() )
            validateCreditUtilizationEvents( cwm3 )
            assertEquals( "before limit=" + limit3 + ",after limit=" + limit4, true, Math.abs( limit4 - limit0 ) < CREDIT_CALCULATION_MINIMUM )
        }
        catch ( Exception e )
        {
            e.printStackTrace()
            fail()
        }
    }

    void testAggregateLimitWithFXRateConversion()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 )
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_LIMIT_CALCULATOR, 10000 )
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "Credit limit before first bid trade take Credit : " + limit0 )
            double tradeAmt = 1000
            Trade trade = prepareSingleLegTrade( tradeAmt, true, true, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate )

            // in this case, check the net receivable and net payable.
            double utilizedAmt = getLimitCurrencyAmount( lpOrg, lpTpForDd, "EUR", tradeAmt, true )

            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd )
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "Credit limit after first bid trade take Credit : " + limit1 + " success : " + cwm.getStatus() )
            assertEquals( "before limit=" + limit0 + ",limitAfter=" + limit1, true, Math.abs( limit0 - limit1 - utilizedAmt ) < CREDIT_CALCULATION_MINIMUM )
            validateCreditUtilizationEvents( cwm )

            // Now take another bid trade on the spot next day.
            Trade trade1 = prepareSingleLegTrade( tradeAmt, true, true, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate.addDays( 1 ) )
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd )
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "Credit limit after second bid trade take Credit : " + limit2 + " success : " + cwm1.getStatus() )
            validateCreditUtilizationEvents( cwm1 )
            assertEquals( "before limit=" + limit1 + ",after limit=" + limit2, true, Math.abs( limit1 - limit2 - utilizedAmt ) < CREDIT_CALCULATION_MINIMUM )

            // Now take an offer trade on the spot next day. This will remove the net receivable position in spot/next day.
            Trade trade2 = prepareSingleLegTrade( tradeAmt, false, true, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate.addDays( 1 ) )
            CreditWorkflowMessage cwm2 = creditMgr.takeCredit( trade2, lpLe, ddOrg, lpTpForDd )
            double limit3 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "Credit limit after third offer trade take Credit : " + limit3 + " success : " + cwm2.getStatus() )
            validateCreditUtilizationEvents( cwm2 )
            assertEquals( "before limit=" + limit2 + ",after limit=" + limit3, true, Math.abs( limit3 - limit1 ) < CREDIT_CALCULATION_MINIMUM )

            // Now take an offer trade on the spot day. This will remove the net receivable position in spot day also.
            Trade trade3 = prepareSingleLegTrade( tradeAmt, false, true, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowMessage cwm3 = creditMgr.takeCredit( trade3, lpLe, ddOrg, lpTpForDd )
            double limit4 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "Credit limit after fourth offer trade take Credit : " + limit4 + " success : " + cwm3.getStatus() )
            validateCreditUtilizationEvents( cwm3 )
            assertEquals( "before limit=" + limit3 + ",after limit=" + limit4, true, Math.abs( limit4 - limit0 ) < CREDIT_CALCULATION_MINIMUM )
        }
        catch ( Exception e )
        {
            e.printStackTrace()
            fail()
        }
    }

    void testAggregateLimitWithNoCreditLimitCurrency()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_LIMIT_CALCULATOR, 10000 )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 )
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "Credit limit before first bid trade take Credit : " + limit0 )
            double tradeAmt = 1000
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURGBP, lpOrg, lpTpForDd, bidRates[0], spotDate )

            // in this case, check the net receivable and net payable.
            double utilizedAmt = getLimitCurrencyAmount( lpOrg, lpTpForDd, "GBP", tradeAmt, true )

            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd )
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "Credit limit after first bid trade take Credit : " + limit1 + " success : " + cwm.getStatus() )
            assertEquals( "before limit=" + limit0 + ",limitAfter=" + limit1, true, Math.abs( limit0 - limit1 - utilizedAmt ) < CREDIT_CALCULATION_MINIMUM )
            validateCreditUtilizationEvents( cwm )

            // Now take another bid trade on the spot next day.
            Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURGBP, lpOrg, lpTpForDd, bidRates[0], spotDate.addDays( 1 ) )
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd )
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "Credit limit after second bid trade take Credit : " + limit2 + " success : " + cwm1.getStatus() )
            validateCreditUtilizationEvents( cwm1 )
            assertEquals( "before limit=" + limit1 + ",after limit=" + limit2, true, Math.abs( limit1 - limit2 - utilizedAmt ) < CREDIT_CALCULATION_MINIMUM )

            // Now take an offer trade on the spot next day. This will remove the net receivable position in spot/next day.
            Trade trade2 = prepareSingleLegTrade( tradeAmt, false, false, EURGBP, lpOrg, lpTpForDd, bidRates[0], spotDate.addDays( 1 ) )
            CreditWorkflowMessage cwm2 = creditMgr.takeCredit( trade2, lpLe, ddOrg, lpTpForDd )
            double limit3 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "Credit limit after third offer trade take Credit : " + limit3 + " success : " + cwm2.getStatus() )
            validateCreditUtilizationEvents( cwm2 )
            assertEquals( "before limit=" + limit2 + ",after limit=" + limit3, true, Math.abs( limit3 - limit1 ) < CREDIT_CALCULATION_MINIMUM )

            // Now take an offer trade on the spot day. This will remove the net receivable position in spot day also.
            Trade trade3 = prepareSingleLegTrade( tradeAmt, false, false, EURGBP, lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowMessage cwm3 = creditMgr.takeCredit( trade3, lpLe, ddOrg, lpTpForDd )
            double limit4 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "Credit limit after fourth offer trade take Credit : " + limit4 + " success : " + cwm3.getStatus() )
            validateCreditUtilizationEvents( cwm3 )
            assertEquals( "before limit=" + limit3 + ",after limit=" + limit4, true, Math.abs( limit4 - limit0 ) < CREDIT_CALCULATION_MINIMUM )
        }
        catch ( Exception e )
        {
            e.printStackTrace()
            fail()
        }
    }

    void testAggregateLimitReserve()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_LIMIT_CALCULATOR, 10000 )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 )
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "Credit limit before first bid reserve Credit : " + limit0 )
            double tradeAmt = 1000
            Request request = prepareSingleLegRequest( prepareSingleLegTrade( tradeAmt, true, true, EURGBP, lpOrg, lpTpForDd, bidRates[0], spotDate ) )
            CreditWorkflowMessage cwm = creditMgr.reserveCredit( request, lpLe, ddOrg, lpTpForDd )
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            double usedAmt1 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "EUR", tradeAmt, true )
            log( "Credit limit after first reserve Credit : " + limit1 + " success : " + cwm.getStatus() + ",utilizedAmt=" + usedAmt1 )
            printCurrencyPositions( cwm )
            assertEquals( "before limit=" + limit0 + ",limit after=" + limit1 + ",usedAmt=" + usedAmt1, true, Math.abs( limit0 - limit1 - usedAmt1 ) < CREDIT_CALCULATION_MINIMUM )
            assertEquals( "success=" + cwm.getStatusName(), cwm.getStatus().equals( MessageStatus.SUCCESS ), true )
            validateCreditUtilizationEvents( cwm )

            // now release the credit.
            CreditWorkflowMessage cwm1 = creditMgr.undoReserve( request, lpLe, ddOrg, lpTpForDd )
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "Credit limit after undoing reserve : " + limit2 + " success : " + cwm1.getStatus() )
            printCurrencyPositions( cwm1 )
            assertEquals( "Reserve amount should be zero. ", true, Math.abs( limit2 - limit0 ) < CREDIT_CALCULATION_MINIMUM )
            assertEquals( "success=" + cwm1.getStatusName(), cwm1.getStatus().equals( MessageStatus.SUCCESS ), true )
        }
        catch ( Exception e )
        {
            e.printStackTrace()
            fail()
        }
    }

    void testAggregateLimitReserveFailure()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_LIMIT_CALCULATOR, 10000 )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 )
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "Credit limit before first bid reserve Credit : " + limit0 )
            double tradeAmt = limit0 + 1000
            Request request = prepareSingleLegRequest( prepareSingleLegTrade( tradeAmt, true, true, EURGBP, lpOrg, lpTpForDd, bidRates[0], spotDate ) )
            CreditWorkflowMessage cwm = creditMgr.reserveCredit( request, lpLe, ddOrg, lpTpForDd )
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "Credit limit after first reserve Credit : " + limit1 + " success : " + cwm.getStatus() + ",before=" + limit0 )
            printCurrencyPositions( cwm )
            assertEquals( "before limit=" + limit0 + ",limit after=" + limit1, true, Math.abs( limit0 - limit1 ) < CREDIT_CALCULATION_MINIMUM )
            assertEquals( "success=" + cwm.getStatusName(), cwm.getStatus().equals( MessageStatus.SUCCESS ), false )
            validateCreditUtilizationEvents( cwm )
        }
        catch ( Exception e )
        {
            e.printStackTrace()
            fail()
        }
    }

    void testIgnoreCurrentValueDatePositions()
    {
        try
        {
            init( lpUser )
            Boolean dailyPLAtCptyLevel = creditAdminSvc.isDailyPL( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION )
            Boolean dailyPLAtOrgLevel = creditAdminSvc.isDefaultDailyPL( lpOrg )
            if ( ( dailyPLAtCptyLevel != null && dailyPLAtCptyLevel ) || ( dailyPLAtOrgLevel != null && dailyPLAtOrgLevel ) )
            {
                log( "Skipping test as daily PL is enabled. TestName=testSimpleAggregateNetPRSettlementNetting" )
                return
            }

            Collection<CreditUtilizationCalculator> supportedCalcs = CreditUtilC.getSupportedNonAccountAggregateNettingMethodologies()

            // initialise trade settling today itself 
            double tradeAmt = 1000


            for ( CreditUtilizationCalculator calculator : supportedCalcs )
            {
                removeExistingCreditUtilizationEvents( lpOrg )
                setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, calculator, 10000 )
                //set exclude trades settling today flag to true
                creditAdminSvc.setIgnoreCurrDatePositions( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, true )

                double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
                log( "Credit limit before first trade take Credit for calculator type " + calculator.getShortName() + " is : " + limit0 )

                Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], tradeDate )
                CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd )
                double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
                log( "Credit limit after first trade take Credit for calculator type " + calculator.getShortName() + " is : " + limit1 + " success : " + cwm.getStatus() + ",events=" + cwm.getCreditUtilizationEvents() )

                assertEquals( "before limit=" + limit0 + ",limitAfter=" + limit1, true, Math.abs( limit0 - limit1 ) < MINIMUM )
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace()
            fail()
        }
        finally
        {
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 )
        }
    }

    void testAggregateLimitUndoCredit()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_LIMIT_CALCULATOR, 10000 )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 )

            // first do a trade to buy 1k USD vs EUR
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "Credit limit before first bid trade take Credit : " + limit1 )
            double tradeAmt = 1000
            Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd )
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "Credit limit after first bid trade take Credit : " + limit2 + " success : " + cwm1.getStatus() )
            assertEquals( "before limit=" + limit1 + ",limit after=" + limit2, true, Math.abs( limit1 - limit2 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM )
            assertEquals( "Message Status=" + cwm1.getStatusName(), cwm1.getStatus().equals( MessageStatus.SUCCESS ), true )
            validateCreditUtilizationEvents( cwm1 )

            // do another trade to sell 1k USD vs EUR. Now the net utilization should be zero.
            log( "Credit limit before second offer trade take Credit : " + limit2 )
            Trade trade2 = prepareSingleLegTrade( tradeAmt, false, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowMessage cwm2 = creditMgr.takeCredit( trade2, lpLe, ddOrg, lpTpForDd )
            double limit3 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "Credit limit after second offer trade take Credit : " + limit3 + " success : " + cwm2.getStatus() )
            assertEquals( "before limit=" + limit2 + ",limit after=" + limit3, true, Math.abs( limit3 - limit1 ) < CREDIT_CALCULATION_MINIMUM )
            assertEquals( "Message Status=" + cwm2.getStatusName(), cwm2.getStatus().equals( MessageStatus.SUCCESS ), true )
            validateCreditUtilizationEvents( cwm2 )

            // undo the last trade so that net utilization becomes that of first trade.
            CreditWorkflowMessage cwm3 = creditMgr.undoCredit( trade2, lpLe, ddOrg, lpTpForDd )
            double limit4 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "Credit limit after second offer trade undo Credit : " + limit4 + " success : " + cwm3.getStatus() )
            assertEquals( "before limit=" + limit3 + ",limit after=" + limit4, true, Math.abs( limit1 - limit4 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM )
            assertEquals( "Message Status=" + cwm3.getStatusName(), cwm3.getStatus().equals( MessageStatus.SUCCESS ), true )
            validateCreditUtilizationEvents( cwm3 )
        }
        catch ( Exception e )
        {
            e.printStackTrace()
            fail()
        }
    }

    void testSimpleGrossDaily()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 )
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 )
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION )
            log( "Credit limit before first bid trade take Credit : " + limit0 )
            double tradeAmt = 1000
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate )

            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd )
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION )
            log( "Credit limit after first bid trade take Credit : " + limit1 + " success : " + cwm.getStatus() )
            assertEquals( "before limit=" + limit0 + ",limitAfter=" + limit1, true, Math.abs( limit0 - limit1 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM )
            validateCreditUtilizationEvents( cwm )

            // Now take an offer side trade. In this case buy currency is EUR. but, if sell currency is credit limit currency, then that amount will be used.
            Currency limitCcy = CreditUtilC.getCounterpartyCreditLimitCurrency( lpOrg, ddOrg, lpTpForDd )
            double utilizedAmt = limitCcy.isSameAs( CurrencyFactory.getCurrency( "USD" ) ) ? tradeAmt : getLimitCurrencyAmount( lpOrg, lpTpForDd, "EUR", tradeAmt / bidRates[0], true )
            Trade trade1 = prepareSingleLegTrade( tradeAmt, false, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd )
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION )
            log( "Credit limit after second offer trade take Credit : " + limit2 + " success : " + cwm1.getStatus() )
            validateCreditUtilizationEvents( cwm1 )
            assertEquals( "before limit=" + limit1 + ",after limit=" + limit2, true, Math.abs( limit1 - limit2 - utilizedAmt ) < CREDIT_CALCULATION_MINIMUM )
        }
        catch ( Exception e )
        {
            e.printStackTrace()
            fail()
        }
    }

    void testGrossDailyWithDifferentValueDates()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 )
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 )
            double limitSpot0 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION )
            log( "Credit limit before first bid trade take Credit : " + limitSpot0 )
            double tradeAmt = 1000
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate )

            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd )
            double limitSpot1 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION )
            log( "Credit limit after first bid trade take Credit : " + limitSpot1 + " success : " + cwm.getStatus() )
            assertEquals( "before limit=" + limitSpot0 + ",limitAfter=" + limitSpot1, true, Math.abs( limitSpot0 - limitSpot1 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM )
            validateCreditUtilizationEvents( cwm )

            // Now take a bid trade on the spot next day.
            double limitSpotNext0 = getAvailableCreditLimit( lpTpForDd, spotDate.addDays( 1 ), DAILY_SETTLEMENT_CLASSIFICATION )
            Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate.addDays( 1 ) )
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd )
            double limitSpotNext1 = getAvailableCreditLimit( lpTpForDd, spotDate.addDays( 1 ), DAILY_SETTLEMENT_CLASSIFICATION )
            log( "Credit limit after second bid trade take Credit : " + limitSpotNext1 + " success : " + cwm1.getStatus() )
            validateCreditUtilizationEvents( cwm1 )
            assertEquals( "before limit=" + limitSpotNext0 + ",after limit=" + limitSpotNext1, true, Math.abs( limitSpotNext0 - limitSpotNext1 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM )

            // Now take an offer trade on the spot next day. This will add to the utilization on spot/next day.
            Trade trade2 = prepareSingleLegTrade( tradeAmt, false, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate.addDays( 1 ) )
            CreditWorkflowMessage cwm2 = creditMgr.takeCredit( trade2, lpLe, ddOrg, lpTpForDd )
            double limitSpotNext2 = getAvailableCreditLimit( lpTpForDd, spotDate.addDays( 1 ), DAILY_SETTLEMENT_CLASSIFICATION )
            log( "Credit limit after third offer trade take Credit : " + limitSpotNext2 + " success : " + cwm2.getStatus() )
            validateCreditUtilizationEvents( cwm2 )
            Currency limitCcy = CreditUtilC.getCounterpartyCreditLimitCurrency( lpOrg, ddOrg, lpTpForDd )
            double utilizedAmt = limitCcy.isSameAs( CurrencyFactory.getCurrency( "USD" ) ) ? tradeAmt : getLimitCurrencyAmount( lpOrg, lpTpForDd, "EUR", tradeAmt / bidRates[0], true )
            assertEquals( "before limit=" + limitSpotNext1 + ",after limit=" + limitSpotNext2, true, Math.abs( limitSpotNext1 - limitSpotNext2 - utilizedAmt ) < CREDIT_CALCULATION_MINIMUM )

            // Now take an offer trade on the spot day. This will remove the net receivable position in spot day also.
            Trade trade3 = prepareSingleLegTrade( tradeAmt, false, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowMessage cwm3 = creditMgr.takeCredit( trade3, lpLe, ddOrg, lpTpForDd )
            double limitSpot2 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION )
            log( "Credit limit after fourth offer trade take Credit : " + limitSpot2 + " success : " + cwm3.getStatus() )
            validateCreditUtilizationEvents( cwm3 )
            assertEquals( "before limit=" + limitSpot1 + ",after limit=" + limitSpot2, true, Math.abs( limitSpot1 - limitSpot2 - utilizedAmt ) < CREDIT_CALCULATION_MINIMUM )
        }
        catch ( Exception e )
        {
            e.printStackTrace()
            fail()
        }
    }

    void testGrossDailyWithFXRateConversion()
    {
        try
        {
            init( lpUser )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 )
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 )
            removeExistingCreditUtilizationEvents( lpOrg )
            double spotLimit0 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION )
            log( "Credit limit before first bid trade take Credit : " + spotLimit0 )
            double tradeAmt = 1000
            Trade trade = prepareSingleLegTrade( tradeAmt, true, true, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate )

            // in this case, EUR is the buying currency. but, USD if the limit currency, then USD amount is used.
            Currency limitCcy = CreditUtilC.getCounterpartyCreditLimitCurrency( lpOrg, ddOrg, lpTpForDd )
            double utilizedAmt = limitCcy.isSameAs( CurrencyFactory.getCurrency( "USD" ) ) ? tradeAmt * bidRates[0] : getLimitCurrencyAmount( lpOrg, lpTpForDd, "EUR", tradeAmt, true )

            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd )
            double spotLimit1 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION )
            log( "Credit limit after first bid trade take Credit : " + spotLimit1 + " success : " + cwm.getStatus() + ",utilizedAmt=" + utilizedAmt )
            assertEquals( "before limit=" + spotLimit0 + ",limitAfter=" + spotLimit1, true, Math.abs( spotLimit0 - spotLimit1 - utilizedAmt ) < CREDIT_CALCULATION_MINIMUM )
            validateCreditUtilizationEvents( cwm )

            // Now take another bid trade on the spot next day.
            double spotNextLimit0 = getAvailableCreditLimit( lpTpForDd, spotDate.addDays( 1 ), DAILY_SETTLEMENT_CLASSIFICATION )
            Trade trade1 = prepareSingleLegTrade( tradeAmt, true, true, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate.addDays( 1 ) )
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd )
            double spotNextLimit1 = getAvailableCreditLimit( lpTpForDd, spotDate.addDays( 1 ), DAILY_SETTLEMENT_CLASSIFICATION )
            log( "Credit limit after second bid trade take Credit : " + spotNextLimit1 + " success : " + cwm1.getStatus() )
            validateCreditUtilizationEvents( cwm1 )
            assertEquals( "before limit=" + spotNextLimit0 + ",after limit=" + spotNextLimit1, true, Math.abs( spotNextLimit0 - spotNextLimit1 - utilizedAmt ) < CREDIT_CALCULATION_MINIMUM )

            // Now take an offer trade on the spot next day. This will add to the utilization. But, buying currency is now USD.
            Trade trade2 = prepareSingleLegTrade( tradeAmt, false, true, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate.addDays( 1 ) )
            CreditWorkflowMessage cwm2 = creditMgr.takeCredit( trade2, lpLe, ddOrg, lpTpForDd )
            double spotNextLimit2 = getAvailableCreditLimit( lpTpForDd, spotDate.addDays( 1 ), DAILY_SETTLEMENT_CLASSIFICATION )
            log( "Credit limit after third offer trade take Credit : " + spotNextLimit2 + " success : " + cwm2.getStatus() )
            validateCreditUtilizationEvents( cwm2 )
            assertEquals( "before limit=" + spotNextLimit1 + ",after limit=" + spotNextLimit2, true, Math.abs( spotNextLimit1 - spotNextLimit2 - ( tradeAmt * bidRates[0] ) ) < CREDIT_CALCULATION_MINIMUM )

            // Now take an offer trade on the spot day. This will add to the spot utilization. But buying currency is USD.
            Trade trade3 = prepareSingleLegTrade( tradeAmt, false, true, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowMessage cwm3 = creditMgr.takeCredit( trade3, lpLe, ddOrg, lpTpForDd )
            double spotLimit2 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION )
            log( "Credit limit after fourth offer trade take Credit : " + spotLimit2 + " success : " + cwm3.getStatus() )
            validateCreditUtilizationEvents( cwm3 )
            assertEquals( "before limit=" + spotLimit1 + ",after limit=" + spotLimit2, true, Math.abs( spotLimit1 - spotLimit2 - ( tradeAmt * bidRates[0] ) ) < CREDIT_CALCULATION_MINIMUM )
        }
        catch ( Exception e )
        {
            e.printStackTrace()
            fail()
        }
    }

    void testGrossDailyWithNoCreditLimitCurrency()
    {
        try
        {
            init( lpUser )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 )
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 )
            removeExistingCreditUtilizationEvents( lpOrg )
            double spotLimit0 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION )
            log( "Credit limit before first bid trade take Credit : " + spotLimit0 )
            double tradeAmt = 1000
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURGBP, lpOrg, lpTpForDd, bidRates[0], spotDate )

            // in this case, buying currency is GBP.
            double utilizedAmt = getLimitCurrencyAmount( lpOrg, lpTpForDd, "GBP", tradeAmt, true )

            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd )
            double spotLimit1 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION )
            log( "Credit limit after first bid trade take Credit : " + spotLimit1 + " success : " + cwm.getStatus() )
            assertEquals( "before limit=" + spotLimit0 + ",limitAfter=" + spotLimit1, true, Math.abs( spotLimit0 - spotLimit1 - utilizedAmt ) < CREDIT_CALCULATION_MINIMUM )
            validateCreditUtilizationEvents( cwm )

            // Now take another bid trade on the spot next day. buying currency is GBP
            double spotNextLimit0 = getAvailableCreditLimit( lpTpForDd, spotDate.addDays( 1 ), DAILY_SETTLEMENT_CLASSIFICATION )
            Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURGBP, lpOrg, lpTpForDd, bidRates[0], spotDate.addDays( 1 ) )
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd )
            double spotNextLimit1 = getAvailableCreditLimit( lpTpForDd, spotDate.addDays( 1 ), DAILY_SETTLEMENT_CLASSIFICATION )
            log( "Credit limit after second bid trade take Credit : " + spotNextLimit1 + " success : " + cwm1.getStatus() )
            validateCreditUtilizationEvents( cwm1 )
            assertEquals( "before limit=" + spotNextLimit0 + ",after limit=" + spotNextLimit1, true, Math.abs( spotNextLimit0 - spotNextLimit1 - utilizedAmt ) < CREDIT_CALCULATION_MINIMUM )

            // Now take an offer trade on the spot next day. In this case, the buying currency is EUR.
            double utilizedAmt1 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "EUR", tradeAmt / bidRates[0], true )
            Trade trade2 = prepareSingleLegTrade( tradeAmt, false, false, EURGBP, lpOrg, lpTpForDd, bidRates[0], spotDate.addDays( 1 ) )
            CreditWorkflowMessage cwm2 = creditMgr.takeCredit( trade2, lpLe, ddOrg, lpTpForDd )
            double spotNextLimit2 = getAvailableCreditLimit( lpTpForDd, spotDate.addDays( 1 ), DAILY_SETTLEMENT_CLASSIFICATION )
            log( "Credit limit after third offer trade take Credit : " + spotNextLimit2 + " success : " + cwm2.getStatus() )
            validateCreditUtilizationEvents( cwm2 )
            assertEquals( "before limit=" + spotNextLimit1 + ",after limit=" + spotNextLimit2, true, Math.abs( spotNextLimit1 - spotNextLimit2 - utilizedAmt1 ) < CREDIT_CALCULATION_MINIMUM )

            // Now take an offer trade on the spot day. GBP is the buying currency.
            Trade trade3 = prepareSingleLegTrade( tradeAmt, false, false, EURGBP, lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowMessage cwm3 = creditMgr.takeCredit( trade3, lpLe, ddOrg, lpTpForDd )
            double spotLimit2 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION )
            log( "Credit limit after fourth offer trade take Credit : " + spotLimit2 + " success : " + cwm3.getStatus() )
            validateCreditUtilizationEvents( cwm3 )
            assertEquals( "before limit=" + spotLimit1 + ",after limit=" + spotLimit2, true, Math.abs( spotLimit1 - spotLimit2 - utilizedAmt1 ) < CREDIT_CALCULATION_MINIMUM )
        }
        catch ( Exception e )
        {
            e.printStackTrace()
            fail()
        }
    }

    void testGrossDailyReserve()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 )
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 )
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION )
            log( "Credit limit before first bid reserve Credit : " + limit0 )
            double tradeAmt = 1000
            Request request = prepareSingleLegRequest( prepareSingleLegTrade( tradeAmt, true, true, EURGBP, lpOrg, lpTpForDd, bidRates[0], spotDate ) )
            CreditWorkflowMessage cwm = creditMgr.reserveCredit( request, lpLe, ddOrg, lpTpForDd )
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION )
            double usedAmt1 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "EUR", tradeAmt, true )
            log( "Credit limit after first reserve Credit : " + limit1 + " success : " + cwm.getStatus() + ",utilizedAmt=" + usedAmt1 )
            printCurrencyPositions( cwm )
            assertEquals( "before limit=" + limit0 + ",limit after=" + limit1 + ",usedAmt=" + usedAmt1, true, Math.abs( limit0 - limit1 - usedAmt1 ) < CREDIT_CALCULATION_MINIMUM )
            assertEquals( "success=" + cwm.getStatusName(), cwm.getStatus().equals( MessageStatus.SUCCESS ), true )
            validateCreditUtilizationEvents( cwm )

            // now release the credit.
            CreditWorkflowMessage cwm1 = creditMgr.undoReserve( request, lpLe, ddOrg, lpTpForDd )
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION )
            log( "Credit limit after undoing reserve : " + limit2 + " success : " + cwm1.getStatus() )
            printCurrencyPositions( cwm1 )
            assertEquals( "Reserve amount should be zero. ", true, Math.abs( limit2 - limit0 ) < CREDIT_CALCULATION_MINIMUM )
            assertEquals( "success=" + cwm1.getStatusName(), cwm1.getStatus().equals( MessageStatus.SUCCESS ), true )
        }
        catch ( Exception e )
        {
            e.printStackTrace()
            fail()
        }
    }

    void testGrossDailyReserveFailure()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 )
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 )
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION )
            log( "Credit limit before first bid reserve Credit : " + limit0 )
            double tradeAmt = limit0 + 1000000
            Request request = prepareSingleLegRequest( prepareSingleLegTrade( tradeAmt, false, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate ) )
            CreditWorkflowMessage cwm = creditMgr.reserveCredit( request, lpLe, ddOrg, lpTpForDd )
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION )
            log( "Credit limit after first reserve Credit : " + limit1 + " success : " + cwm.getStatus() + ",before=" + limit0 )
            printCurrencyPositions( cwm )
            assertEquals( "before limit=" + limit0 + ",limit after=" + limit1, true, Math.abs( limit0 - limit1 ) < CREDIT_CALCULATION_MINIMUM )
            assertEquals( "success=" + cwm.getStatusName(), cwm.getStatus().equals( MessageStatus.SUCCESS ), false )
            validateCreditUtilizationEvents( cwm )
        }
        catch ( Exception e )
        {
            e.printStackTrace()
            fail()
        }
    }

    void testGrossDailyUndoCredit()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 )
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 )

            // first do a trade to buy 1k USD vs EUR. Buying currency is USD
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION )
            log( "Credit limit before first bid trade take Credit : " + limit1 )
            double tradeAmt = 1000
            Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd )
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION )
            log( "Credit limit after first bid trade take Credit : " + limit2 + " success : " + cwm1.getStatus() )
            assertEquals( "before limit=" + limit1 + ",limit after=" + limit2, true, Math.abs( limit1 - limit2 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM )
            assertEquals( "Message Status=" + cwm1.getStatusName(), cwm1.getStatus().equals( MessageStatus.SUCCESS ), true )
            validateCreditUtilizationEvents( cwm1 )

            // do another trade to sell 1k USD vs EUR. buying currency is EUR. but, USD amount will be considered if USD is the limit ccy
            Currency limitCcy = CreditUtilC.getCounterpartyCreditLimitCurrency( lpOrg, ddOrg, lpTpForDd )
            double utilizationAmt = limitCcy.isSameAs( CurrencyFactory.getCurrency( "USD" ) ) ? tradeAmt : getLimitCurrencyAmount( lpOrg, lpTpForDd, "EUR", tradeAmt / bidRates[0], true )
            log( "Credit limit before second offer trade take Credit : " + limit2 )
            Trade trade2 = prepareSingleLegTrade( tradeAmt, false, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowMessage cwm2 = creditMgr.takeCredit( trade2, lpLe, ddOrg, lpTpForDd )
            double limit3 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION )
            log( "Credit limit after second offer trade take Credit : " + limit3 + " success : " + cwm2.getStatus() )
            assertEquals( "before limit=" + limit2 + ",limit after=" + limit3, true, Math.abs( limit2 - limit3 - utilizationAmt ) < CREDIT_CALCULATION_MINIMUM )
            assertEquals( "Message Status=" + cwm2.getStatusName(), cwm2.getStatus().equals( MessageStatus.SUCCESS ), true )
            validateCreditUtilizationEvents( cwm2 )

            // undo the last trade so that it goes back to the previous utilization.
            CreditWorkflowMessage cwm3 = creditMgr.undoCredit( trade2, lpLe, ddOrg, lpTpForDd )
            double limit4 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION )
            log( "Credit limit after second offer trade undo Credit : " + limit4 + " success : " + cwm3.getStatus() )
            assertEquals( "before limit=" + limit3 + ",limit after=" + limit4, true, Math.abs( limit2 - limit4 ) < CREDIT_CALCULATION_MINIMUM )
            assertEquals( "Message Status=" + cwm3.getStatusName(), cwm3.getStatus().equals( MessageStatus.SUCCESS ), true )
            validateCreditUtilizationEvents( cwm3 )
        }
        catch ( Exception e )
        {
            e.printStackTrace()
            fail()
        }
    }

    void testAggregateNetCashSettlementWithLimitCurrencyTrades()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )

            Currency usd = CurrencyFactory.getCurrency ( "USD" )
            Currency eur = CurrencyFactory.getCurrency ( "EUR" )
            Currency gbp = CurrencyFactory.getCurrency ( "GBP" )
            Currency jpy = CurrencyFactory.getCurrency ( "JPY" )

            Currency limitCcy = CreditUtilC.getCounterpartyCreditLimitCurrency( lpOrg, ddOrg, lpTpForDd )
            if ( !limitCcy.isSameAs( CurrencyFactory.getCurrency( "USD" ) ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( lpOrg, CurrencyFactory.getCurrency( "USD" ) )
                creditAdminSvc.reinitialize( lpOrg )
            }

            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_NET_CASH_SETTLEMENT_CALCULATOR, 0.0 )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, null, 10000 )


            double tradeAmt = 1000
            DealingLimit dl = CreditLimitSubscriptionManagerC.getInstance ().getAvailableBaseCurrencyBilateralCreditLimit ( lpLe, ddLe, spotDate, eur, usd, true )
            assertNotNull ( dl )
            assertTrue ( dl.getBidLimit () <= 0.0 )
            assertTrue ( dl.getOfferLimit () <= 0.0 )

            // test trade on trade date. This should not have any impact on the utilization.
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], tradeDate )
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd )
            assertTrue ( MessageStatus.FAILURE.equals ( cwm.getStatus () ) )

            sleepFor ( 1000 )

            // now deposit USD amount
            creditAdminSvc.deposit ( lpOrg, lpTpForDd, usd, 10000, "DEP" + System.currentTimeMillis (), null, null, "DEP" + System.currentTimeMillis () )

            DealingLimit dl1 = CreditLimitSubscriptionManagerC.getInstance ().getAvailableBaseCurrencyBilateralCreditLimit ( lpLe, ddLe, spotDate, eur, usd, true )
            assertNotNull ( dl1 )
            assertTrue ( dl1.getBidLimit () <= 0.0 )
            assertTrue ( dl1.getOfferLimit () > 0.0 )


            // now do a trade in the bid side and it should be rejected.
            Trade trade1 = prepareSingleLegTrade( tradeAmt, false, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate )

            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd )
            assertTrue ( MessageStatus.FAILURE.equals ( cwm1.getStatus () ) )

            // Now take an offer side trade for more than what is available
            Trade trade2 = prepareSingleLegTrade( 100000, false, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowMessage cwm2 = creditMgr.takeCredit( trade2, lpLe, ddOrg, lpTpForDd )
            assertTrue ( MessageStatus.FAILURE.equals ( cwm2.getStatus () ) )

            // Now take an offer side trade within the available limit. This should go through.
            Trade trade3 = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowMessage cwm3 = creditMgr.takeCredit( trade3, lpLe, ddOrg, lpTpForDd )
            assertTrue ( MessageStatus.SUCCESS.equals ( cwm3.getStatus () ) )
            validateCreditUtilizationEvents( cwm3 )

            // Now trade on a different currency pair
            Trade trade4 = prepareSingleLegTrade( tradeAmt, true, false, EURGBP, lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowMessage cwm4 = creditMgr.takeCredit( trade4, lpLe, ddOrg, lpTpForDd )
            assertTrue ( MessageStatus.FAILURE.equals ( cwm4.getStatus () ) )

            Trade trade5 = prepareSingleLegTrade( tradeAmt, true, true, "USD/JPY", lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowMessage cwm5 = creditMgr.takeCredit( trade5, lpLe, ddOrg, lpTpForDd )
            assertTrue ( MessageStatus.SUCCESS.equals ( cwm5.getStatus () ) )
            validateCreditUtilizationEvents( cwm5 )

            DealingLimit dl2 = CreditLimitSubscriptionManagerC.getInstance ().getAvailableBaseCurrencyBilateralCreditLimit ( lpLe, ddLe, spotDate, usd, jpy, true )
            assertNotNull ( dl2 )
            assertTrue ( dl2.getBidLimit () > 0.0 )
            assertTrue ( dl2.getOfferLimit () > 0.0 )

            Trade trade6 = prepareSingleLegTrade( tradeAmt, true, false, "USD/JPY", lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowMessage cwm6 = creditMgr.takeCredit( trade6, lpLe, ddOrg, lpTpForDd )
            assertTrue ( MessageStatus.SUCCESS.equals ( cwm6.getStatus () ) )
            validateCreditUtilizationEvents( cwm6 )

        }
        catch ( Exception e )
        {
            fail ( "testAggregateNetCashSettlementWithLimitCurrencyTrades", e )
        }
        finally
        {
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 )
        }
    }

    void testAggregateNetCashSettlementWithLimitCurrencyTradesAndLimitCheckEnabledBid()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )

            Currency usd = CurrencyFactory.getCurrency ( "USD" )
            Currency eur = CurrencyFactory.getCurrency ( "EUR" )

            Currency limitCcy = CreditUtilC.getCounterpartyCreditLimitCurrency( lpOrg, ddOrg, lpTpForDd )
            if ( !limitCcy.isSameAs( CurrencyFactory.getCurrency( "USD" ) ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( lpOrg, CurrencyFactory.getCurrency( "USD" ) )
                creditAdminSvc.reinitialize( lpOrg )
            }

            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_NET_CASH_SETTLEMENT_CALCULATOR, 10000 )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, null, 10000 )

            CreditUtilization cu = getAggregateCreditUtilization ( lpLe, lpTpForDd, spotDate )
            CurrencyPositionCollection cpc = cu.getCurrencyPositions ()

            double tradeAmt = 1000
            DealingLimit dl = CreditLimitSubscriptionManagerC.getInstance ().getAvailableBaseCurrencyBilateralCreditLimit ( lpLe, ddLe, spotDate, eur, usd, true )
            assertNotNull ( dl )
            double bidLimit = dl.getBidLimit ()
            double offerLimit = dl.getOfferLimit ()
            log( "before trade. bidLimit=" + bidLimit + ",offerLimit=" + offerLimit + ",cpc=" + cpc )
            assertTrue ( bidLimit > 0.0 )
            assertTrue ( offerLimit > 0.0 )

            IdcTransaction tx = initTransaction ( true )
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd )
            assertTrue ( MessageStatus.SUCCESS.equals ( cwm.getStatus () ) )
            validateCreditUtilizationEvents( cwm )
            commitTransaction ( tx )

            dl = CreditLimitSubscriptionManagerC.getInstance ().getAvailableBaseCurrencyBilateralCreditLimit ( lpLe, ddLe, spotDate, eur, usd, true )
            assertNotNull ( dl )
            double bidLimit1 = dl.getBidLimit ()
            double offerLimit1 = dl.getOfferLimit ()
            log( "after trade. bidLimit=" + bidLimit1 + ",offerLimit1=" + offerLimit1 + ",cpc=" + cpc )
            assertTrue ( "new bid limit=" + bidLimit1 + ",old bid limit=" + bidLimit, bidLimit1 >= 0.0 && bidLimit1 > bidLimit  )
            assertTrue ( "new offer limit=" + offerLimit1 + ",old offer limit=" + offerLimit, offerLimit1 >= 0.0 && offerLimit1 < offerLimit )

            sleepFor ( 1000 )

            // now deposit USD amount
            creditAdminSvc.deposit ( lpOrg, lpTpForDd, usd, 900, "DEP" + System.currentTimeMillis (), null, null, "DEP" + System.currentTimeMillis () )

            DealingLimit dl1 = CreditLimitSubscriptionManagerC.getInstance ().getAvailableBaseCurrencyBilateralCreditLimit ( lpLe, ddLe, spotDate, eur, usd, true )
            assertNotNull ( dl1 )
            double bidLimit2 = dl1.getBidLimit ()
            double offerLimit2 = dl1.getOfferLimit ()
            log( "after deposit. bidLimit=" + bidLimit2 + ",offerLimit2=" + offerLimit2 + ",cpc=" + cpc )
            assertTrue ( "new bid limit=" + bidLimit2 + ",old bid limit=" + bidLimit1,bidLimit2 > 0.0 && bidLimit2 == bidLimit1 )
            assertTrue ( "new offer limit=" + offerLimit2 + ",old offer limit=" + offerLimit1,offerLimit2 > 0.0 && offerLimit2 > offerLimit1 )


            double tradeAmt1 = 30000
            Trade trade1 = prepareSingleLegTrade( tradeAmt1, false, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate )

            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd )
            assertTrue ( MessageStatus.FAILURE.equals ( cwm1.getStatus () ) )

        }
        catch ( Exception e )
        {
            fail ( "testAggregateNetCashSettlementWithLimitCurrencyTradesAndLimitCheckEnabledBid", e )
        }
        finally
        {
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 )
        }
    }

    void testAggregateNetCashSettlementWithLimitCurrencyTradesAndLimitCheckEnabledOffer()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )

            Currency usd = CurrencyFactory.getCurrency ( "USD" )
            Currency eur = CurrencyFactory.getCurrency ( "EUR" )

            Currency limitCcy = CreditUtilC.getCounterpartyCreditLimitCurrency( lpOrg, ddOrg, lpTpForDd )
            if ( !limitCcy.isSameAs( CurrencyFactory.getCurrency( "USD" ) ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( lpOrg, CurrencyFactory.getCurrency( "USD" ) )
                creditAdminSvc.reinitialize( lpOrg )
            }

            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_NET_CASH_SETTLEMENT_CALCULATOR, 10000 )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, null, 10000 )

            CreditUtilization cu = getAggregateCreditUtilization ( lpLe, lpTpForDd, spotDate )
            CurrencyPositionCollection cpc = cu.getCurrencyPositions ()

            double tradeAmt = 1000
            DealingLimit dl = CreditLimitSubscriptionManagerC.getInstance ().getAvailableBaseCurrencyBilateralCreditLimit ( lpLe, ddLe, spotDate, eur, usd, true )
            assertNotNull ( dl )
            double bidLimit = dl.getBidLimit ()
            double offerLimit = dl.getOfferLimit ()
            log( "before trade. bidLimit=" + bidLimit + ",offerLimit=" + offerLimit + ",cpc=" + cpc )
            assertTrue ( bidLimit > 0.0 )
            assertTrue ( offerLimit > 0.0 )

            IdcTransaction tx = initTransaction ( true )
            Trade trade = prepareSingleLegTrade( tradeAmt, false, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd )
            assertTrue ( MessageStatus.SUCCESS.equals ( cwm.getStatus () ) )
            validateCreditUtilizationEvents( cwm )
            commitTransaction ( tx )

            dl = CreditLimitSubscriptionManagerC.getInstance ().getAvailableBaseCurrencyBilateralCreditLimit ( lpLe, ddLe, spotDate, eur, usd, true )
            assertNotNull ( dl )
            double bidLimit1 = dl.getBidLimit ()
            double offerLimit1 = dl.getOfferLimit ()
            log( "after trade. bidLimit2=" + bidLimit1 + ",offerLimit1=" + offerLimit1 + ",cpc=" + cpc )
            assertTrue ( "new bid limit=" + bidLimit1 + ",old bid limit=" + bidLimit, bidLimit1 >= 0.0 && bidLimit1 < bidLimit  )
            assertTrue ( "new offer limit=" + offerLimit1 + ",old offer limit=" + offerLimit, offerLimit1 >= 0.0 && offerLimit1 > offerLimit )

            sleepFor ( 1000 )

            // now deposit USD amount
            creditAdminSvc.deposit ( lpOrg, lpTpForDd, eur, 900, "DEP" + System.currentTimeMillis (), null, null, "DEP" + System.currentTimeMillis () )

            DealingLimit dl1 = CreditLimitSubscriptionManagerC.getInstance ().getAvailableBaseCurrencyBilateralCreditLimit ( lpLe, ddLe, spotDate, eur, usd, true )
            assertNotNull ( dl1 )
            double bidLimit2 = dl1.getBidLimit ()
            double offerLimit2 = dl1.getOfferLimit ()
            log( "after deposit. bidLimit2=" + bidLimit2 + ",offerLimit2=" + offerLimit2 + ",cpc=" + cpc )
            assertTrue ( "new bid limit=" + bidLimit2 + ",old bid limit=" + bidLimit1,bidLimit2 > 0.0 && bidLimit2 > bidLimit1 )
            assertTrue ( "new offer limit=" + offerLimit2 + ",old offer limit=" + offerLimit1,offerLimit2 > 0.0 && offerLimit2 == offerLimit1 )

            double tradeAmt1 = 30000
            Trade trade1 = prepareSingleLegTrade( tradeAmt1, false, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate )

            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd )
            assertTrue ( MessageStatus.FAILURE.equals ( cwm1.getStatus () ) )

        }
        catch ( Exception e )
        {
            fail ( "testAggregateNetCashSettlementWithLimitCurrencyTradesAndLimitCheckEnabledOffer", e )
        }
        finally
        {
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 )
        }
    }

    void testAggregateNetCashSettlementWithNonLimitCurrencyTrades()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )

            Currency eur = CurrencyFactory.getCurrency ( "EUR" )
            Currency gbp = CurrencyFactory.getCurrency ( "GBP" )
            Currency jpy = CurrencyFactory.getCurrency ( "JPY" )

            Currency limitCcy = CreditUtilC.getCounterpartyCreditLimitCurrency( lpOrg, ddOrg, lpTpForDd )
            if ( !limitCcy.isSameAs( CurrencyFactory.getCurrency( "USD" ) ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( lpOrg, CurrencyFactory.getCurrency( "USD" ) )
                creditAdminSvc.reinitialize( lpOrg )
            }

            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_NET_CASH_SETTLEMENT_CALCULATOR, 0.0 )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, null, 10000 )


            double tradeAmt = 1000
            DealingLimit dl = CreditLimitSubscriptionManagerC.getInstance ().getAvailableBaseCurrencyBilateralCreditLimit ( lpLe, ddLe, spotDate, eur, jpy, true )
            assertNotNull ( dl )
            assertTrue ( dl.getBidLimit () <= 0.0 )
            assertTrue ( dl.getOfferLimit () <= 0.0 )

            // test trade on trade date. This should not have any impact on the utilization.
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, "EUR/JPY", lpOrg, lpTpForDd, bidRates[0], tradeDate )
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd )
            assertTrue ( MessageStatus.FAILURE.equals ( cwm.getStatus () ) )

            sleepFor ( 1000 )

            // now deposit EUR amount
            creditAdminSvc.deposit ( lpOrg, lpTpForDd, eur, 10000, "DEP" + System.currentTimeMillis (), null, null, "DEP" + System.currentTimeMillis () )

            DealingLimit dl1 = CreditLimitSubscriptionManagerC.getInstance ().getAvailableBaseCurrencyBilateralCreditLimit ( lpLe, ddLe, spotDate, eur, jpy, true )
            assertNotNull ( dl1 )
            assertTrue ( dl1.getBidLimit () > 0.0 )
            assertTrue ( dl1.getOfferLimit () <= 0.0 )


            // now do a trade in the bid side and it should be rejected.
            Trade trade1 = prepareSingleLegTrade( tradeAmt, false, true, "EUR/JPY", lpOrg, lpTpForDd, bidRates[0], spotDate )

            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd )
            assertTrue ( MessageStatus.FAILURE.equals ( cwm1.getStatus () ) )

            // Now take an offer side trade for more than what is available
            Trade trade2 = prepareSingleLegTrade( 100000, false, false, "EUR/JPY", lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowMessage cwm2 = creditMgr.takeCredit( trade2, lpLe, ddOrg, lpTpForDd )
            assertTrue ( MessageStatus.FAILURE.equals ( cwm2.getStatus () ) )

            // Now take an offer side trade within the available limit. This should go through.
            Trade trade3 = prepareSingleLegTrade( tradeAmt, true, true, "EUR/JPY", lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowMessage cwm3 = creditMgr.takeCredit( trade3, lpLe, ddOrg, lpTpForDd )
            assertTrue ( MessageStatus.SUCCESS.equals ( cwm3.getStatus () ) )
            validateCreditUtilizationEvents( cwm3 )

            // Now trade on a different currency pair
            Trade trade4 = prepareSingleLegTrade( tradeAmt, true, false, EURGBP, lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowMessage cwm4 = creditMgr.takeCredit( trade4, lpLe, ddOrg, lpTpForDd )
            assertTrue ( MessageStatus.FAILURE.equals ( cwm4.getStatus () ) )

            Trade trade5 = prepareSingleLegTrade( tradeAmt, true, true, "EUR/CHF", lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowMessage cwm5 = creditMgr.takeCredit( trade5, lpLe, ddOrg, lpTpForDd )
            assertTrue ( MessageStatus.SUCCESS.equals ( cwm5.getStatus () ) )
            validateCreditUtilizationEvents( cwm5 )

            DealingLimit dl2 = CreditLimitSubscriptionManagerC.getInstance ().getAvailableBaseCurrencyBilateralCreditLimit ( lpLe, ddLe, spotDate, eur, jpy, true )
            assertNotNull ( dl2 )
            assertTrue ( dl2.getBidLimit () > 0.0 )
            assertTrue ( dl2.getOfferLimit () > 0.0 )

            // should be able to sell part of CHF bought earlier
            Trade trade6 = prepareSingleLegTrade( tradeAmt, true, false, "CHF/JPY", lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowMessage cwm6 = creditMgr.takeCredit( trade6, lpLe, ddOrg, lpTpForDd )
            assertTrue ( MessageStatus.SUCCESS.equals ( cwm6.getStatus () ) )
            validateCreditUtilizationEvents( cwm6 )
        }
        catch ( Exception e )
        {
            fail ( "testAggregateNetCashSettlementWithNonLimitCurrencyTrades", e )
        }
        finally
        {
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 )
        }
    }

    void testAggregateNetCashSettlementWithNonLimitCurrencyTradesAndLimitCheckEnabledBid()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )

            Currency eur = CurrencyFactory.getCurrency ( "EUR" )
            Currency jpy = CurrencyFactory.getCurrency ( "JPY" )

            Currency limitCcy = CreditUtilC.getCounterpartyCreditLimitCurrency( lpOrg, ddOrg, lpTpForDd )
            if ( !limitCcy.isSameAs( CurrencyFactory.getCurrency( "USD" ) ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( lpOrg, CurrencyFactory.getCurrency( "USD" ) )
                creditAdminSvc.reinitialize( lpOrg )
            }

            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_NET_CASH_SETTLEMENT_CALCULATOR, 10000 )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, null, 10000 )

            CreditUtilization cu = getAggregateCreditUtilization ( lpLe, lpTpForDd, spotDate )
            CurrencyPositionCollection cpc = cu.getCurrencyPositions ()

            double tradeAmt = 1000
            DealingLimit dl = CreditLimitSubscriptionManagerC.getInstance ().getAvailableBaseCurrencyBilateralCreditLimit ( lpLe, ddLe, spotDate, eur, jpy, true )
            assertNotNull ( dl )
            double bidLimit = dl.getBidLimit ()
            double offerLimit = dl.getOfferLimit ()
            log( "before trade. bidLimit=" + bidLimit + ",offerLimit=" + offerLimit + ",cpc=" + cpc )
            assertTrue ( bidLimit > 0.0 )
            assertTrue ( offerLimit > 0.0 )

            IdcTransaction tx = initTransaction ( true )
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, "EUR/JPY", lpOrg, lpTpForDd, bidRates[4], spotDate )
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd )
            assertTrue ( MessageStatus.SUCCESS.equals ( cwm.getStatus () ) )
            validateCreditUtilizationEvents ( cwm )
            commitTransaction ( tx )

            dl = CreditLimitSubscriptionManagerC.getInstance ().getAvailableBaseCurrencyBilateralCreditLimit ( lpLe, ddLe, spotDate, eur, jpy, true )
            assertNotNull ( dl )
            double bidLimit1 = dl.getBidLimit ()
            double offerLimit1 = dl.getOfferLimit ()
            log( "after trade. bidLimit2=" + bidLimit1 + ",offerLimit1=" + offerLimit1 + ",cpc=" + cpc )
            assertTrue ( "new bid limit=" + bidLimit1 + ",old bid limit=" + bidLimit, bidLimit1 >= 0.0 && bidLimit1 > bidLimit  )
            assertTrue ( "new offer limit=" + offerLimit1 + ",old offer limit=" + offerLimit, offerLimit1 >= 0.0 && offerLimit1 < offerLimit )

            sleepFor ( 1000 )

            // now deposit EUR amount
            creditAdminSvc.deposit ( lpOrg, lpTpForDd, eur, 1000, "DEP" + System.currentTimeMillis (), null, null, "DEP" + System.currentTimeMillis () )

            DealingLimit dl1 = CreditLimitSubscriptionManagerC.getInstance ().getAvailableBaseCurrencyBilateralCreditLimit ( lpLe, ddLe, spotDate, eur, jpy, true )
            assertNotNull ( dl1 )
            double bidLimit2 = dl1.getBidLimit ()
            double offerLimit2 = dl1.getOfferLimit ()
            log( "after deposit. bidLimit2=" + bidLimit2 + ",offerLimit2=" + offerLimit2 + ",cpc=" + cpc )
            assertTrue ( "new bid limit=" + bidLimit2 + ",old bid limit=" + bidLimit1,bidLimit2 > 0.0 && bidLimit2 > bidLimit1 )
            assertTrue ( "new offer limit=" + offerLimit2 + ",old offer limit=" + offerLimit1,offerLimit2 > 0.0 && offerLimit2 == offerLimit1 )

            tradeAmt = 100000
            Trade trade1 = prepareSingleLegTrade( tradeAmt, false, true, "EUR/JPY", lpOrg, lpTpForDd, bidRates[4], spotDate )
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd )
            assertTrue ( MessageStatus.FAILURE.equals ( cwm1.getStatus () ) )
        }
        catch ( Exception e )
        {
            fail ( "testAggregateNetCashSettlementWithNonLimitCurrencyTradesAndLimitCheckEnabledBid", e )
        }
        finally
        {
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 )
        }
    }

    void testAggregateNetCashSettlementWithNonLimitCurrencyTradesAndLimitCheckEnabledOffer()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )

            Currency eur = CurrencyFactory.getCurrency ( "EUR" )
            Currency jpy = CurrencyFactory.getCurrency ( "JPY" )

            Currency limitCcy = CreditUtilC.getCounterpartyCreditLimitCurrency( lpOrg, ddOrg, lpTpForDd )
            if ( !limitCcy.isSameAs( CurrencyFactory.getCurrency( "USD" ) ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( lpOrg, CurrencyFactory.getCurrency( "USD" ) )
                creditAdminSvc.reinitialize( lpOrg )
            }

            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_NET_CASH_SETTLEMENT_CALCULATOR, 10000 )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, null, 10000 )

            CreditUtilization cu = getAggregateCreditUtilization ( lpLe, lpTpForDd, spotDate )
            CurrencyPositionCollection cpc = cu.getCurrencyPositions ()

            double tradeAmt = 1000
            DealingLimit dl = CreditLimitSubscriptionManagerC.getInstance ().getAvailableBaseCurrencyBilateralCreditLimit ( lpLe, ddLe, spotDate, eur, jpy, true )
            assertNotNull ( dl )
            double bidLimit = dl.getBidLimit ()
            double offerLimit = dl.getOfferLimit ()
            log( "before trade. bidLimit=" + bidLimit + ",offerLimit=" + offerLimit + ",cpc=" + cpc )
            assertTrue ( bidLimit > 0.0 )
            assertTrue ( offerLimit > 0.0 )

            IdcTransaction tx = initTransaction ( true )
            Trade trade = prepareSingleLegTrade( tradeAmt, false, true, "EUR/JPY", lpOrg, lpTpForDd, bidRates[4], spotDate )
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd )
            assertTrue ( MessageStatus.SUCCESS.equals ( cwm.getStatus () ) )
            validateCreditUtilizationEvents ( cwm )
            commitTransaction ( tx )

            dl = CreditLimitSubscriptionManagerC.getInstance ().getAvailableBaseCurrencyBilateralCreditLimit ( lpLe, ddLe, spotDate, eur, jpy, true )
            assertNotNull ( dl )
            double bidLimit1 = dl.getBidLimit ()
            double offerLimit1 = dl.getOfferLimit ()
            log( "after trade. bidLimit2=" + bidLimit1 + ",offerLimit1=" + offerLimit1 + ",cpc=" + cpc )
            assertTrue ( "new bid limit=" + bidLimit1 + ",old bid limit=" + bidLimit, bidLimit1 >= 0.0 && bidLimit1 > bidLimit  )
            assertTrue ( "new offer limit=" + offerLimit1 + ",old offer limit=" + offerLimit, offerLimit1 >= 0.0 && offerLimit1 < offerLimit )

            sleepFor ( 1000 )

            // now deposit EUR amount
            creditAdminSvc.deposit ( lpOrg, lpTpForDd, jpy, 100000, "DEP" + System.currentTimeMillis (), null, null, "DEP" + System.currentTimeMillis () )

            DealingLimit dl1 = CreditLimitSubscriptionManagerC.getInstance ().getAvailableBaseCurrencyBilateralCreditLimit ( lpLe, ddLe, spotDate, eur, jpy, true )
            assertNotNull ( dl1 )
            double bidLimit2 = dl1.getBidLimit ()
            double offerLimit2 = dl1.getOfferLimit ()
            log( "after deposit. bidLimit2=" + bidLimit2 + ",offerLimit2=" + offerLimit2 + ",cpc=" + cpc )
            assertTrue ( "new bid limit=" + bidLimit2 + ",old bid limit=" + bidLimit1,bidLimit2 > 0.0 && bidLimit2 == bidLimit1 )
            assertTrue ( "new offer limit=" + offerLimit2 + ",old offer limit=" + offerLimit1,offerLimit2 > 0.0 && offerLimit2 > offerLimit1 )

            tradeAmt = 100000
            Trade trade1 = prepareSingleLegTrade( tradeAmt, false, true, "EUR/JPY", lpOrg, lpTpForDd, bidRates[4], spotDate )
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd )
            assertTrue ( MessageStatus.FAILURE.equals ( cwm1.getStatus () ) )
        }
        catch ( Exception e )
        {
            fail ( "testAggregateNetCashSettlementWithNonLimitCurrencyTradesAndLimitCheckEnabledOffer", e )
        }
        finally
        {
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 )
        }
    }

    void testAggregateNetCashSettlementWithLimitCurrencyTradesAndTakerAsCreditProvider()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )
            removeExistingCreditUtilizationEvents ( fiOrg )

            Currency usd = CurrencyFactory.getCurrency ( "USD" )
            Currency eur = CurrencyFactory.getCurrency ( "EUR" )
            Currency gbp = CurrencyFactory.getCurrency ( "GBP" )
            Currency jpy = CurrencyFactory.getCurrency ( "JPY" )

            creditAdminSvc.setCreditEnabled ( lpOrg, false )

            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_NET_CASH_SETTLEMENT_CALCULATOR, 0.0 )
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, null, 10000 )

            double tradeAmt = 1000
            DealingLimit dl = CreditLimitSubscriptionManagerC.getInstance ().getAvailableBaseCurrencyBilateralCreditLimit ( lpLe, fiLe, spotDate, eur, usd, true )
            assertNotNull ( dl )
            assertTrue ( dl.getBidLimit () <= 0.0 )
            assertTrue ( dl.getOfferLimit () <= 0.0 )

            // test trade on trade date. This should not have any impact on the utilization.
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], tradeDate )
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit ( lpLe, fiLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS )
            assertTrue ( MessageStatus.FAILURE.equals ( cwm.getStatus () ) )

            sleepFor ( 1000 )

            // now deposit USD amount
            creditAdminSvc.deposit ( fiOrg, fiTpForLp, usd, 10000, "DEP" + System.currentTimeMillis (), null, null, "DEP" + System.currentTimeMillis () )

            DealingLimit dl1 = CreditLimitSubscriptionManagerC.getInstance ().getAvailableBaseCurrencyBilateralCreditLimit ( lpLe, fiLe, spotDate, eur, usd, true )
            assertNotNull ( dl1 )
            assertTrue ( dl1.getBidLimit () > 0.0 )
            assertTrue ( dl1.getOfferLimit () <= 0.0 )


            // now do a trade in the bid side and it should be rejected.
            Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate )

            CreditWorkflowMessage cwm1 = creditMgr.takeBilateralCredit ( lpLe, fiLe, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS )
            assertTrue ( MessageStatus.FAILURE.equals ( cwm1.getStatus () ) )

            // Now take an offer side trade for more than what is available
            Trade trade2 = prepareSingleLegTrade( 100000, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate )
            CreditWorkflowMessage cwm2 = creditMgr.takeBilateralCredit ( lpLe, fiLe, trade2, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS)
            assertTrue ( MessageStatus.FAILURE.equals ( cwm2.getStatus () ) )

            // Now take an offer side trade within the available limit. This should go through.
            Trade trade3 = prepareSingleLegTrade( tradeAmt, false, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate )
            CreditWorkflowMessage cwm3 = creditMgr.takeBilateralCredit ( lpLe, fiLe, trade3, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS )
            assertTrue ( MessageStatus.SUCCESS.equals ( cwm3.getStatus () ) )

            // Now trade on a different currency pair
            Trade trade4 = prepareSingleLegTrade( tradeAmt, false, false, EURGBP, lpOrg, lpTpForFi, bidRates[0], spotDate )
            CreditWorkflowMessage cwm4 = creditMgr.takeBilateralCredit ( lpLe, fiLe, trade4, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS )
            assertTrue ( MessageStatus.FAILURE.equals ( cwm4.getStatus () ) )

            Trade trade5 = prepareSingleLegTrade( tradeAmt, false, true, "USD/JPY", lpOrg, lpTpForFi, bidRates[0], spotDate )
            CreditWorkflowMessage cwm5 = creditMgr.takeBilateralCredit ( lpLe, fiLe, trade5, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS )
            assertTrue ( MessageStatus.SUCCESS.equals ( cwm5.getStatus () ) )

            DealingLimit dl2 = CreditLimitSubscriptionManagerC.getInstance ().getAvailableBaseCurrencyBilateralCreditLimit ( lpLe, fiLe, spotDate, usd, jpy, true )
            assertNotNull ( dl2 )
            assertTrue ( dl2.getBidLimit () > 0.0 )
            assertTrue ( dl2.getOfferLimit () > 0.0 )

            Trade trade6 = prepareSingleLegTrade( tradeAmt, false, false, "USD/JPY", lpOrg, lpTpForFi, bidRates[0], spotDate )
            CreditWorkflowMessage cwm6 = creditMgr.takeBilateralCredit ( lpLe, fiLe, trade6, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS )
            assertTrue ( MessageStatus.SUCCESS.equals ( cwm6.getStatus () ) )
        }
        catch ( Exception e )
        {
            fail ( "testAggregateNetCashSettlementWithLimitCurrencyTradesAndTakerAsCreditProvider", e )
        }
        finally
        {
            creditAdminSvc.setCreditEnabled ( lpOrg, true )
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 )
            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 )
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 )
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 )
        }
    }

    void testAggregateNetCashSettlementWithLimitCurrencyTradesAndTakerAsCreditProviderAndLimitCheckEnabledBid()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )
            removeExistingCreditUtilizationEvents ( fiOrg )

            Currency usd = CurrencyFactory.getCurrency ( "USD" )
            Currency eur = CurrencyFactory.getCurrency ( "EUR" )

            creditAdminSvc.setCreditEnabled ( lpOrg, false )

            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_NET_CASH_SETTLEMENT_CALCULATOR, 10000 )
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, null, 10000 )

            CreditUtilization cu = getAggregateCreditUtilization ( fiLe, fiTpForLp, spotDate )
            CurrencyPositionCollection cpc = cu.getCurrencyPositions ()

            double tradeAmt = 1000
            DealingLimit dl = CreditLimitSubscriptionManagerC.getInstance ().getAvailableBaseCurrencyBilateralCreditLimit ( lpLe, fiLe, spotDate, eur, usd, true )
            assertNotNull ( dl )
            double bidLimit = dl.getBidLimit ()
            double offerLimit = dl.getOfferLimit ()
            log( "before trade. bidLimit=" + bidLimit + ",offerLimit=" + offerLimit + ",cpc=" + cpc )
            assertTrue ( bidLimit > 0.0 )
            assertTrue ( offerLimit > 0.0 )

            IdcTransaction tx = initTransaction ( true )
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate )
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, fiLe, lpOrg, fiTpForLp )
            assertTrue ( MessageStatus.SUCCESS.equals ( cwm.getStatus () ) )
            validateCreditUtilizationEvents( cwm )
            commitTransaction ( tx )

            dl = CreditLimitSubscriptionManagerC.getInstance ().getAvailableBaseCurrencyBilateralCreditLimit ( lpLe, fiLe, spotDate, eur, usd, true )
            assertNotNull ( dl )
            double bidLimit1 = dl.getBidLimit ()
            double offerLimit1 = dl.getOfferLimit ()
            log( "after trade. bidLimit=" + bidLimit1 + ",offerLimit1=" + offerLimit1 + ",cpc=" + cpc )
            assertTrue ( "new bid limit=" + bidLimit1 + ",old bid limit=" + bidLimit, bidLimit1 >= 0.0 && bidLimit1 > bidLimit  )
            assertTrue ( "new offer limit=" + offerLimit1 + ",old offer limit=" + offerLimit, offerLimit1 >= 0.0 && offerLimit1 < offerLimit )

            // now deposit USD amount
            creditAdminSvc.deposit ( fiOrg, fiTpForLp, usd, 900, "DEP" + System.currentTimeMillis (), null, null, "DEP" + System.currentTimeMillis () )

            DealingLimit dl1 = CreditLimitSubscriptionManagerC.getInstance ().getAvailableBaseCurrencyBilateralCreditLimit ( lpLe, fiLe, spotDate, eur, usd, true )
            assertNotNull ( dl1 )
            double bidLimit2 = dl1.getBidLimit ()
            double offerLimit2 = dl1.getOfferLimit ()
            log( "after deposit. bidLimit=" + bidLimit2 + ",offerLimit2=" + offerLimit2 + ",cpc=" + cpc )
            assertTrue ( "new bid limit=" + bidLimit2 + ",old bid limit=" + bidLimit1,bidLimit2 > 0.0 && bidLimit2 > bidLimit1 )
            assertTrue ( "new offer limit=" + offerLimit2 + ",old offer limit=" + offerLimit1,offerLimit2 > 0.0 && offerLimit2 == offerLimit1 )


            double tradeAmt1 = 30000
            Trade trade1 = prepareSingleLegTrade( tradeAmt1, false, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate )

            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, fiLe, lpOrg, fiTpForLp )
            assertTrue ( MessageStatus.FAILURE.equals ( cwm1.getStatus () ) )

        }
        catch ( Exception e )
        {
            fail ( "testAggregateNetCashSettlementWithLimitCurrencyTradesAndTakerAsCreditProviderAndLimitCheckEnabledBid", e )
        }
        finally
        {
            creditAdminSvc.setCreditEnabled ( lpOrg, true )
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 )
            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 )
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 )
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 )
        }
    }

    void testAggregateNetCashSettlementWithLimitCurrencyTradesAndTakerAsCreditProviderAndLimitCheckEnabledOffer()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )
            removeExistingCreditUtilizationEvents ( fiOrg )

            Currency usd = CurrencyFactory.getCurrency ( "USD" )
            Currency eur = CurrencyFactory.getCurrency ( "EUR" )

            creditAdminSvc.setCreditEnabled ( lpOrg, false )

            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_NET_CASH_SETTLEMENT_CALCULATOR, 10000 )
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, null, 10000 )

            CreditUtilization cu = getAggregateCreditUtilization ( fiLe, fiTpForLp, spotDate )
            CurrencyPositionCollection cpc = cu.getCurrencyPositions ()

            double tradeAmt = 1000
            DealingLimit dl = CreditLimitSubscriptionManagerC.getInstance ().getAvailableBaseCurrencyBilateralCreditLimit ( lpLe, fiLe, spotDate, eur, usd, true )
            assertNotNull ( dl )
            double bidLimit = dl.getBidLimit ()
            double offerLimit = dl.getOfferLimit ()
            log( "before trade. bidLimit=" + bidLimit + ",offerLimit=" + offerLimit + ",cpc=" + cpc )
            assertTrue ( bidLimit > 0.0 )
            assertTrue ( offerLimit > 0.0 )

            IdcTransaction tx = initTransaction ( true )
            Trade trade = prepareSingleLegTrade( tradeAmt, false, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate )
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, fiLe, lpOrg, fiTpForLp )
            assertTrue ( MessageStatus.SUCCESS.equals ( cwm.getStatus () ) )
            validateCreditUtilizationEvents( cwm )
            commitTransaction ( tx )

            dl = CreditLimitSubscriptionManagerC.getInstance ().getAvailableBaseCurrencyBilateralCreditLimit ( lpLe, fiLe, spotDate, eur, usd, true )
            assertNotNull ( dl )
            double bidLimit1 = dl.getBidLimit ()
            double offerLimit1 = dl.getOfferLimit ()
            log( "after trade. bidLimit=" + bidLimit1 + ",offerLimit1=" + offerLimit1 + ",cpc=" + cpc )
            assertTrue ( "new bid limit=" + bidLimit1 + ",old bid limit=" + bidLimit, bidLimit1 >= 0.0 && bidLimit1 < bidLimit  )
            assertTrue ( "new offer limit=" + offerLimit1 + ",old offer limit=" + offerLimit, offerLimit1 >= 0.0 && offerLimit1 > offerLimit )

            sleepFor ( 1000 )

            // now deposit USD amount
            creditAdminSvc.deposit ( fiOrg, fiTpForLp, usd, 900, "DEP" + System.currentTimeMillis (), null, null, "DEP" + System.currentTimeMillis () )

            DealingLimit dl1 = CreditLimitSubscriptionManagerC.getInstance ().getAvailableBaseCurrencyBilateralCreditLimit ( lpLe, fiLe, spotDate, eur, usd, true )
            assertNotNull ( dl1 )
            double bidLimit2 = dl1.getBidLimit ()
            double offerLimit2 = dl1.getOfferLimit ()
            log( "after deposit. bidLimit=" + bidLimit2 + ",offerLimit2=" + offerLimit2 + ",cpc=" + cpc )
            assertTrue ( "new bid limit=" + bidLimit2 + ",old bid limit=" + bidLimit1,bidLimit2 > 0.0 && bidLimit2 >  bidLimit1 )
            assertTrue ( "new offer limit=" + offerLimit2 + ",old offer limit=" + offerLimit1,offerLimit2 > 0.0 && offerLimit2 == offerLimit1 )


            double tradeAmt1 = 30000
            Trade trade1 = prepareSingleLegTrade( tradeAmt1, false, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate )

            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, fiLe, lpOrg, fiTpForLp )
            assertTrue ( MessageStatus.FAILURE.equals ( cwm1.getStatus () ) )

        }
        catch ( Exception e )
        {
            fail ( "testAggregateNetCashSettlementWithLimitCurrencyTradesAndTakerAsCreditProviderAndLimitCheckEnabledOffer", e )
        }
        finally
        {
            creditAdminSvc.setCreditEnabled ( lpOrg, true )
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 )
            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 )
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 )
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 )
        }
    }

    void testAggregateNetCashSettlementWithNonLimitCurrencyTradesAndTakerAsCreditProvider()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )
            removeExistingCreditUtilizationEvents ( fiOrg )

            Currency eur = CurrencyFactory.getCurrency ( "EUR" )
            Currency gbp = CurrencyFactory.getCurrency ( "GBP" )
            Currency jpy = CurrencyFactory.getCurrency ( "JPY" )

            creditAdminSvc.setCreditEnabled ( lpOrg, false )

            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_NET_CASH_SETTLEMENT_CALCULATOR, 0.0 )
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, null, 10000 )

            double tradeAmt = 1000
            DealingLimit dl = CreditLimitSubscriptionManagerC.getInstance ().getAvailableBaseCurrencyBilateralCreditLimit ( lpLe, fiLe, spotDate, eur, jpy, true )
            assertNotNull ( dl )
            assertTrue ( dl.getBidLimit () <= 0.0 )
            assertTrue ( dl.getOfferLimit () <= 0.0 )

            // test trade on trade date. This should not have any impact on the utilization.
            Trade trade = prepareSingleLegTrade( tradeAmt, false, false, "EUR/JPY", lpOrg, lpTpForFi, bidRates[0], tradeDate )
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit ( lpLe, fiLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS )
            assertTrue ( MessageStatus.FAILURE.equals ( cwm.getStatus () ) )

            sleepFor ( 1000 )

            // now deposit EUR amount
            creditAdminSvc.deposit ( fiOrg, fiTpForLp, eur, 10000, "DEP" + System.currentTimeMillis (), null, null, "DEP" + System.currentTimeMillis () )

            DealingLimit dl1 = CreditLimitSubscriptionManagerC.getInstance ().getAvailableBaseCurrencyBilateralCreditLimit ( lpLe, fiLe, spotDate, eur, jpy, true )
            assertNotNull ( dl1 )
            assertTrue ( dl1.getBidLimit () <= 0.0 )
            assertTrue ( dl1.getOfferLimit () > 0.0 )


            // now do a trade in the bid side and it should be rejected.
            Trade trade1 = prepareSingleLegTrade( tradeAmt, true, true, "EUR/JPY", lpOrg, lpTpForFi, bidRates[0], spotDate )

            CreditWorkflowMessage cwm1 = creditMgr.takeBilateralCredit ( lpLe, fiLe, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS )
            assertTrue ( MessageStatus.FAILURE.equals ( cwm1.getStatus () ) )

            // Now take an offer side trade for more than what is available
            Trade trade2 = prepareSingleLegTrade( 100000, false, false, "EUR/JPY", lpOrg, lpTpForFi, bidRates[0], spotDate )
            CreditWorkflowMessage cwm2 = creditMgr.takeBilateralCredit ( lpLe, fiLe, trade2, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS )
            assertTrue ( MessageStatus.FAILURE.equals ( cwm2.getStatus () ) )

            // Now take an offer side trade within the available limit. This should go through.
            Trade trade3 = prepareSingleLegTrade( tradeAmt, false, true, "EUR/JPY", lpOrg, lpTpForFi, bidRates[0], spotDate )
            CreditWorkflowMessage cwm3 = creditMgr.takeBilateralCredit ( lpLe, fiLe, trade3, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS )
            assertTrue ( MessageStatus.SUCCESS.equals ( cwm3.getStatus () ) )

            // Now trade on a different currency pair
            Trade trade4 = prepareSingleLegTrade( tradeAmt, false, false, EURGBP, lpOrg, lpTpForFi, bidRates[0], spotDate )
            CreditWorkflowMessage cwm4 = creditMgr.takeBilateralCredit ( lpLe, fiLe, trade4, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS )
            assertTrue ( MessageStatus.FAILURE.equals ( cwm4.getStatus () ) )

            Trade trade5 = prepareSingleLegTrade( tradeAmt, false, true, "EUR/CHF", lpOrg, lpTpForFi, bidRates[0], spotDate )
            CreditWorkflowMessage cwm5 = creditMgr.takeBilateralCredit ( lpLe, fiLe, trade5, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS )
            assertTrue ( MessageStatus.SUCCESS.equals ( cwm5.getStatus () ) )

            DealingLimit dl2 = CreditLimitSubscriptionManagerC.getInstance ().getAvailableBaseCurrencyBilateralCreditLimit ( lpLe, fiLe, spotDate, eur, jpy, true )
            assertNotNull ( dl2 )
            assertTrue ( dl2.getBidLimit () > 0.0 )
            assertTrue ( dl2.getOfferLimit () > 0.0 )

            // should be able to sell part of CHF bought earlier
            Trade trade6 = prepareSingleLegTrade( tradeAmt, false, false, "CHF/JPY", lpOrg, lpTpForFi, bidRates[0], spotDate )
            CreditWorkflowMessage cwm6 = creditMgr.takeBilateralCredit ( lpLe, fiLe, trade6, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS )
            assertTrue ( MessageStatus.SUCCESS.equals ( cwm6.getStatus () ) )
        }
        catch ( Exception e )
        {
            fail ( "testAggregateNetCashSettlementWithNonLimitCurrencyTradesAndTakerAsCreditProvider", e )
        }
        finally
        {
            creditAdminSvc.setCreditEnabled ( lpOrg, true )
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 )
            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 )
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 )
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 )
        }
    }

    void testAggregateNetCashSettlementWithNonLimitCurrencyTradesAndTakerAsCreditProviderAndLimitCheckEnabledBid()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )
            removeExistingCreditUtilizationEvents ( fiOrg )

            Currency eur = CurrencyFactory.getCurrency ( "EUR" )
            Currency jpy = CurrencyFactory.getCurrency ( "JPY" )

            creditAdminSvc.setCreditEnabled ( lpOrg, false )

            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_NET_CASH_SETTLEMENT_CALCULATOR, 10000 )
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, null, 10000 )

            CreditUtilization cu = getAggregateCreditUtilization ( fiLe, fiTpForLp, spotDate )
            CurrencyPositionCollection cpc = cu.getCurrencyPositions ()

            double tradeAmt = 1000
            DealingLimit dl = CreditLimitSubscriptionManagerC.getInstance ().getAvailableBaseCurrencyBilateralCreditLimit ( lpLe, fiLe, spotDate, eur, jpy, true )
            assertNotNull ( dl )
            double bidLimit = dl.getBidLimit ()
            double offerLimit = dl.getOfferLimit ()
            log( "before trade. bidLimit=" + bidLimit + ",offerLimit=" + offerLimit + ",cpc=" + cpc )
            assertTrue ( bidLimit > 0.0 )
            assertTrue ( offerLimit > 0.0 )

            IdcTransaction tx = initTransaction ( true )
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, "EUR/JPY", lpOrg, lpTpForFi, bidRates[4], spotDate )
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, fiLe, lpOrg, fiTpForLp )
            assertTrue ( MessageStatus.SUCCESS.equals ( cwm.getStatus () ) )
            validateCreditUtilizationEvents ( cwm )
            commitTransaction ( tx )

            dl = CreditLimitSubscriptionManagerC.getInstance ().getAvailableBaseCurrencyBilateralCreditLimit ( lpLe, fiLe, spotDate, eur, jpy, true )
            assertNotNull ( dl )
            double bidLimit1 = dl.getBidLimit ()
            double offerLimit1 = dl.getOfferLimit ()
            log( "after trade. bidLimit2=" + bidLimit1 + ",offerLimit1=" + offerLimit1 + ",cpc=" + cpc )
            assertTrue ( "new bid limit=" + bidLimit1 + ",old bid limit=" + bidLimit, bidLimit1 >= 0.0 && bidLimit1 > bidLimit  )
            assertTrue ( "new offer limit=" + offerLimit1 + ",old offer limit=" + offerLimit, offerLimit1 >= 0.0 && offerLimit1 < offerLimit )

            sleepFor ( 1000 )

            // now deposit EUR amount
            creditAdminSvc.deposit ( fiOrg, fiTpForLp, jpy, 100000, "DEP" + System.currentTimeMillis (), null, null, "DEP" + System.currentTimeMillis () )

            DealingLimit dl1 = CreditLimitSubscriptionManagerC.getInstance ().getAvailableBaseCurrencyBilateralCreditLimit ( lpLe, fiLe, spotDate, eur, jpy, true )
            assertNotNull ( dl1 )
            double bidLimit2 = dl1.getBidLimit ()
            double offerLimit2 = dl1.getOfferLimit ()
            log( "after deposit. bidLimit2=" + bidLimit2 + ",offerLimit2=" + offerLimit2 + ",cpc=" + cpc )
            assertTrue ( "new bid limit=" + bidLimit2 + ",old bid limit=" + bidLimit1,bidLimit2 > 0.0 && bidLimit2 > bidLimit1 )
            assertTrue ( "new offer limit=" + offerLimit2 + ",old offer limit=" + offerLimit1,offerLimit2 > 0.0 && offerLimit2 == offerLimit1 )

            tradeAmt = 100000
            Trade trade1 = prepareSingleLegTrade( tradeAmt, false, true, "EUR/JPY", lpOrg, lpTpForFi, bidRates[4], spotDate )
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, fiLe, lpOrg, fiTpForLp )
            assertTrue ( MessageStatus.FAILURE.equals ( cwm1.getStatus () ) )
        }
        catch ( Exception e )
        {
            fail ( "testAggregateNetCashSettlementWithNonLimitCurrencyTradesAndTakerAsCreditProviderAndLimitCheckEnabledBid", e )
        }
        finally
        {
            creditAdminSvc.setCreditEnabled ( lpOrg, true )
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 )
            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 )
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 )
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 )
        }
    }

    void testAggregateNetCashSettlementWithNonLimitCurrencyTradesAndTakerAsCreditProviderAndLimitCheckEnabledOffer()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )
            removeExistingCreditUtilizationEvents ( fiOrg )

            Currency eur = CurrencyFactory.getCurrency ( "EUR" )
            Currency jpy = CurrencyFactory.getCurrency ( "JPY" )

            creditAdminSvc.setCreditEnabled ( lpOrg, false )

            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_NET_CASH_SETTLEMENT_CALCULATOR, 10000 )
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, null, 10000 )

            CreditUtilization cu = getAggregateCreditUtilization ( fiLe, fiTpForLp, spotDate )
            CurrencyPositionCollection cpc = cu.getCurrencyPositions ()

            double tradeAmt = 1000
            DealingLimit dl = CreditLimitSubscriptionManagerC.getInstance ().getAvailableBaseCurrencyBilateralCreditLimit ( lpLe, fiLe, spotDate, eur, jpy, true )
            assertNotNull ( dl )
            double bidLimit = dl.getBidLimit ()
            double offerLimit = dl.getOfferLimit ()
            log( "before trade. bidLimit=" + bidLimit + ",offerLimit=" + offerLimit + ",cpc=" + cpc )
            assertTrue ( bidLimit > 0.0 )
            assertTrue ( offerLimit > 0.0 )

            IdcTransaction tx = initTransaction ( true )
            Trade trade = prepareSingleLegTrade( tradeAmt, false, false, "EUR/JPY", lpOrg, lpTpForFi, bidRates[4], spotDate )
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, fiLe, lpOrg, fiTpForLp )
            assertTrue ( MessageStatus.SUCCESS.equals ( cwm.getStatus () ) )
            validateCreditUtilizationEvents ( cwm )
            commitTransaction ( tx )

            dl = CreditLimitSubscriptionManagerC.getInstance ().getAvailableBaseCurrencyBilateralCreditLimit ( lpLe, fiLe, spotDate, eur, jpy, true )
            assertNotNull ( dl )
            double bidLimit1 = dl.getBidLimit ()
            double offerLimit1 = dl.getOfferLimit ()
            log( "after trade. bidLimit2=" + bidLimit1 + ",offerLimit1=" + offerLimit1 + ",cpc=" + cpc )
            assertTrue ( "new bid limit=" + bidLimit1 + ",old bid limit=" + bidLimit, bidLimit1 >= 0.0 && bidLimit1 < bidLimit  )
            assertTrue ( "new offer limit=" + offerLimit1 + ",old offer limit=" + offerLimit, offerLimit1 >= 0.0 && offerLimit1 > offerLimit )

            sleepFor ( 1000 )

            // now deposit EUR amount
            creditAdminSvc.deposit ( fiOrg, fiTpForLp, eur, 1000, "DEP" + System.currentTimeMillis (), null, null, "DEP" + System.currentTimeMillis () )

            DealingLimit dl1 = CreditLimitSubscriptionManagerC.getInstance ().getAvailableBaseCurrencyBilateralCreditLimit ( lpLe, fiLe, spotDate, eur, jpy, true )
            assertNotNull ( dl1 )
            double bidLimit2 = dl1.getBidLimit ()
            double offerLimit2 = dl1.getOfferLimit ()
            log( "after deposit. bidLimit2=" + bidLimit2 + ",offerLimit2=" + offerLimit2 + ",cpc=" + cpc )
            assertTrue ( "new bid limit=" + bidLimit2 + ",old bid limit=" + bidLimit1,bidLimit2 > 0.0 && bidLimit2 == bidLimit1 )
            assertTrue ( "new offer limit=" + offerLimit2 + ",old offer limit=" + offerLimit1,offerLimit2 > 0.0 && offerLimit2 > offerLimit1 )

            tradeAmt = 100000
            Trade trade1 = prepareSingleLegTrade( tradeAmt, false, true, "EUR/JPY", lpOrg, lpTpForFi, bidRates[4], spotDate )
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, fiLe, lpOrg, fiTpForLp )
            assertTrue ( MessageStatus.FAILURE.equals ( cwm1.getStatus () ) )
        }
        catch ( Exception e )
        {
            fail ( "testAggregateNetCashSettlementWithNonLimitCurrencyTradesAndTakerAsCreditProviderAndLimitCheckEnabledOffer", e )
        }
        finally
        {
            creditAdminSvc.setCreditEnabled ( lpOrg, true )
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 )
            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 )
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 )
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 )
        }
    }

    void testSkipAggregateNetCashSettlement()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )

            Currency usd = CurrencyFactory.getCurrency ( "USD" )
            Currency eur = CurrencyFactory.getCurrency ( "EUR" )

            Currency limitCcy = CreditUtilC.getCounterpartyCreditLimitCurrency( lpOrg, ddOrg, lpTpForDd )
            if ( !limitCcy.isSameAs( CurrencyFactory.getCurrency( "USD" ) ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( lpOrg, CurrencyFactory.getCurrency( "USD" ) )
                creditAdminSvc.reinitialize( lpOrg )
            }

            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_NET_CASH_SETTLEMENT_CALCULATOR, 0.0 )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, null, 10000 )


            double tradeAmt = 1000
            DealingLimit dl = CreditLimitSubscriptionManagerC.getInstance ().getAvailableBaseCurrencyBilateralCreditLimit ( lpLe, ddLe, spotDate, eur, usd, true )
            assertNotNull ( dl )
            assertTrue ( dl.getBidLimit () <= 0.0 )
            assertTrue ( dl.getOfferLimit () <= 0.0 )

            // test trade on trade date. This should not have any impact on the utilization.
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], tradeDate )
            CreditWorkflowRiders riders = new CreditWorkflowRiders()
            riders.setSkipAccountCheck ( true )
            CreditWorkflowMessage cwm = CreditLimitRuleServiceFactory.getCreditLimitRuleService ().takeBilateralCredit ( lpLe, ddLe, trade, riders  )
            assertTrue ( MessageStatus.SUCCESS.equals ( cwm.getStatus () ) )

            sleepFor ( 1000 )

            // now deposit USD amount
            creditAdminSvc.deposit ( lpOrg, lpTpForDd, usd, 10000, "DEP" + System.currentTimeMillis (), null, null, "DEP" + System.currentTimeMillis () )

            DealingLimit dl1 = CreditLimitSubscriptionManagerC.getInstance ().getAvailableBaseCurrencyBilateralCreditLimit ( lpLe, ddLe, spotDate, eur, usd, true )
            assertNotNull ( dl1 )
            assertTrue ( dl1.getBidLimit () <= 0.0 )
            assertTrue ( dl1.getOfferLimit () > 0.0 )

            // now do a trade in the bid side and it should be rejected if were not skip account type
            Trade trade1 = prepareSingleLegTrade( tradeAmt, false, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate )

            CreditWorkflowMessage cwm1 = CreditLimitRuleServiceFactory.getCreditLimitRuleService ().takeBilateralCredit ( lpLe, ddLe, trade1, riders )
            assertTrue ( MessageStatus.SUCCESS.equals ( cwm1.getStatus () ) )

            // Now take an offer side trade for more than what is available
            Trade trade2 = prepareSingleLegTrade( 100000, false, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowMessage cwm2 = CreditLimitRuleServiceFactory.getCreditLimitRuleService ().takeBilateralCredit ( lpLe, ddLe, trade2, riders  )
            assertTrue ( MessageStatus.SUCCESS.equals ( cwm2.getStatus () ) )

            DealingLimit dl2 = CreditLimitSubscriptionManagerC.getInstance ().getAvailableBaseCurrencyBilateralCreditLimit ( lpLe, ddLe, spotDate, eur, usd, true )
            assertNotNull ( dl2 )
            assertTrue ( dl2.getBidLimit () <= 0.0 )
            assertTrue ( dl2.getOfferLimit () > 0.0 )

            // Now take an offer side trade for more than what is available
            Trade trade3 = prepareSingleLegTrade( 100000, false, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowMessage cwm3 = CreditLimitRuleServiceFactory.getCreditLimitRuleService ().takeBilateralCredit ( lpLe, ddLe, trade3, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS  )
            assertTrue ( MessageStatus.FAILURE.equals ( cwm3.getStatus () ) )
        }
        catch ( Exception e )
        {
            fail ( "testSkipAggregateNetCashSettlement", e )
        }
        finally
        {
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 )
        }
    }

    void testSkipAggregateNetCashSettlementWithLimitCheckEnabled()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )

            Currency usd = CurrencyFactory.getCurrency ( "USD" )
            Currency eur = CurrencyFactory.getCurrency ( "EUR" )

            Currency limitCcy = CreditUtilC.getCounterpartyCreditLimitCurrency( lpOrg, ddOrg, lpTpForDd )
            if ( !limitCcy.isSameAs( CurrencyFactory.getCurrency( "USD" ) ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( lpOrg, CurrencyFactory.getCurrency( "USD" ) )
                creditAdminSvc.reinitialize( lpOrg )
            }

            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_NET_CASH_SETTLEMENT_CALCULATOR, 10000 )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, null, 10000 )

            double tradeAmt = 100000
            DealingLimit dl = CreditLimitSubscriptionManagerC.getInstance ().getAvailableBaseCurrencyBilateralCreditLimit ( lpLe, ddLe, spotDate, eur, usd, true )
            assertNotNull ( dl )
            assertTrue ( dl.getBidLimit () > 0.0 )
            assertTrue ( dl.getOfferLimit () > 0.0 )

            // test trade on trade date. This should not have any impact on the utilization.
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowRiders riders = new CreditWorkflowRiders()
            riders.setSkipAccountCheck ( true )
            CreditWorkflowMessage cwm = CreditLimitRuleServiceFactory.getCreditLimitRuleService ().takeBilateralCredit ( lpLe, ddLe, trade, riders  )
            assertTrue ( MessageStatus.SUCCESS.equals ( cwm.getStatus () ) )

            Trade trade3 = prepareSingleLegTrade( tradeAmt, false, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowMessage cwm3 = CreditLimitRuleServiceFactory.getCreditLimitRuleService ().takeBilateralCredit ( lpLe, ddLe, trade3, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS  )
            assertTrue ( MessageStatus.FAILURE.equals ( cwm3.getStatus () ) )
        }
        catch ( Exception e )
        {
            fail ( "testSkipAggregateNetCashSettlementWithLimitCheckEnabled", e )
        }
        finally
        {
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 )
        }
    }

    void testSkipAggregateNetCashSettlementWithDailyCreditEnabled()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )

            Currency usd = CurrencyFactory.getCurrency ( "USD" )
            Currency eur = CurrencyFactory.getCurrency ( "EUR" )

            Currency limitCcy = CreditUtilC.getCounterpartyCreditLimitCurrency( lpOrg, ddOrg, lpTpForDd )
            if ( !limitCcy.isSameAs( CurrencyFactory.getCurrency( "USD" ) ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( lpOrg, CurrencyFactory.getCurrency( "USD" ) )
                creditAdminSvc.reinitialize( lpOrg )
            }

            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_NET_CASH_SETTLEMENT_CALCULATOR, 0.0 )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 10000000 )


            double tradeAmt = 1000
            DealingLimit dl = CreditLimitSubscriptionManagerC.getInstance ().getAvailableBaseCurrencyBilateralCreditLimit ( lpLe, ddLe, spotDate, eur, usd, true )
            assertNotNull ( dl )
            assertTrue ( dl.getBidLimit () <= 0.0 )
            assertTrue ( dl.getOfferLimit () <= 0.0 )

            // test trade on trade date. This should not have any impact on the utilization.
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowRiders riders = new CreditWorkflowRiders()
            riders.setSkipAccountCheck ( true )
            CreditWorkflowMessage cwm = CreditLimitRuleServiceFactory.getCreditLimitRuleService ().takeBilateralCredit ( lpLe, ddLe, trade, riders  )
            assertTrue ( MessageStatus.SUCCESS.equals ( cwm.getStatus () ) )
            validateCreditUtilizationEvents ( cwm )

            sleepFor ( 1000 )

            // now deposit USD amount
            creditAdminSvc.deposit ( lpOrg, lpTpForDd, usd, 10000, "DEP" + System.currentTimeMillis (), null, null, "DEP" + System.currentTimeMillis () )

            DealingLimit dl1 = CreditLimitSubscriptionManagerC.getInstance ().getAvailableBaseCurrencyBilateralCreditLimit ( lpLe, ddLe, spotDate, eur, usd, true )
            assertNotNull ( dl1 )
            assertTrue ( dl1.getBidLimit () <= 0.0 )
            assertTrue ( dl1.getOfferLimit () > 0.0 )

            // now do a trade in the bid side and it should be rejected if were not skip account type
            Trade trade1 = prepareSingleLegTrade( tradeAmt, false, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate )

            CreditWorkflowMessage cwm1 = CreditLimitRuleServiceFactory.getCreditLimitRuleService ().takeBilateralCredit ( lpLe, ddLe, trade1, riders  )
            assertTrue ( MessageStatus.SUCCESS.equals ( cwm1.getStatus () ) )
            validateCreditUtilizationEvents ( cwm1 )

            // Now take an offer side trade for more than what is available
            Trade trade2 = prepareSingleLegTrade( 100000, false, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowMessage cwm2 = CreditLimitRuleServiceFactory.getCreditLimitRuleService ().takeBilateralCredit ( lpLe, ddLe, trade2, riders  )
            assertTrue ( MessageStatus.SUCCESS.equals ( cwm2.getStatus () ) )
            validateCreditUtilizationEvents ( cwm2 )

            DealingLimit dl2 = CreditLimitSubscriptionManagerC.getInstance ().getAvailableBaseCurrencyBilateralCreditLimit ( lpLe, ddLe, spotDate, eur, usd, true )
            assertNotNull ( dl2 )
            assertTrue ( dl2.getBidLimit () <= 0.0 )
            assertTrue ( dl2.getOfferLimit () > 0.0 )

            // Now take an offer side trade for more than what is available
            Trade trade3 = prepareSingleLegTrade( 100000, false, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowMessage cwm3 = CreditLimitRuleServiceFactory.getCreditLimitRuleService ().takeBilateralCredit ( lpLe, ddLe, trade3, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS  )
            assertTrue ( MessageStatus.FAILURE.equals ( cwm3.getStatus () ) )
        }
        catch ( Exception e )
        {
            fail ( "testSkipAggregateNetCashSettlementWithDailyCreditEnabled", e )
        }
        finally
        {
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 )
        }
    }

    void testSkipAggregateNetCashSettlementWithDailyCreditEnabledAndLimitCheckEnabled()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )

            Currency usd = CurrencyFactory.getCurrency ( "USD" )
            Currency eur = CurrencyFactory.getCurrency ( "EUR" )

            Currency limitCcy = CreditUtilC.getCounterpartyCreditLimitCurrency( lpOrg, ddOrg, lpTpForDd )
            if ( !limitCcy.isSameAs( CurrencyFactory.getCurrency( "USD" ) ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( lpOrg, CurrencyFactory.getCurrency( "USD" ) )
                creditAdminSvc.reinitialize( lpOrg )
            }

            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_NET_CASH_SETTLEMENT_CALCULATOR, 10000 )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 10000000 )


            double tradeAmt = 100000
            DealingLimit dl = CreditLimitSubscriptionManagerC.getInstance ().getAvailableBaseCurrencyBilateralCreditLimit ( lpLe, ddLe, spotDate, eur, usd, true )
            assertNotNull ( dl )
            assertTrue ( dl.getBidLimit () > 0.0 )
            assertTrue ( dl.getOfferLimit () > 0.0 )

            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowRiders riders = new CreditWorkflowRiders()
            riders.setSkipAccountCheck ( true )
            CreditWorkflowMessage cwm = CreditLimitRuleServiceFactory.getCreditLimitRuleService ().takeBilateralCredit ( lpLe, ddLe, trade, riders  )
            assertTrue ( MessageStatus.SUCCESS.equals ( cwm.getStatus () ) )
            validateCreditUtilizationEvents ( cwm )

            sleepFor ( 1000 )

            Trade trade3 = prepareSingleLegTrade( tradeAmt, false, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowRiders riders1 = new CreditWorkflowRiders()
            riders1.setSkipAccountCheck ( true )
            CreditWorkflowMessage cwm3 = CreditLimitRuleServiceFactory.getCreditLimitRuleService ().takeBilateralCredit ( lpLe, ddLe, trade3 , CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS  )
            assertTrue ( MessageStatus.FAILURE.equals ( cwm3.getStatus () ) )
        }
        catch ( Exception e )
        {
            fail ( "testSkipAggregateNetCashSettlementWithDailyCreditEnabledAndLimitCheckEnabled", e )
        }
        finally
        {
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 )
        }
    }

    void testAggregateNetCashSettlementWithExcessUtilizationAllowed()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )

            Currency usd = CurrencyFactory.getCurrency ( "USD" )
            Currency eur = CurrencyFactory.getCurrency ( "EUR" )

            Currency limitCcy = CreditUtilC.getCounterpartyCreditLimitCurrency( lpOrg, ddOrg, lpTpForDd )
            if ( !limitCcy.isSameAs( CurrencyFactory.getCurrency( "USD" ) ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( lpOrg, CurrencyFactory.getCurrency( "USD" ) )
                creditAdminSvc.reinitialize( lpOrg )
            }

            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_NET_CASH_SETTLEMENT_CALCULATOR, 0.0 )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, null, 10000 )


            double tradeAmt = 1000
            DealingLimit dl = CreditLimitSubscriptionManagerC.getInstance ().getAvailableBaseCurrencyBilateralCreditLimit ( lpLe, ddLe, spotDate, eur, usd, true )
            assertNotNull ( dl )
            assertTrue ( dl.getBidLimit () <= 0.0 )
            assertTrue ( dl.getOfferLimit () <= 0.0 )

            // test trade prior to addign any balance
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], tradeDate )
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd )
            assertTrue ( MessageStatus.FAILURE.equals ( cwm.getStatus () ) )

            sleepFor ( 1000 )

            // now deposit USD amount
            creditAdminSvc.deposit ( lpOrg, lpTpForDd, usd, 10000, "DEP" + System.currentTimeMillis (), null, null, "DEP" + System.currentTimeMillis () )

            DealingLimit dl1 = CreditLimitSubscriptionManagerC.getInstance ().getAvailableBaseCurrencyBilateralCreditLimit ( lpLe, ddLe, spotDate, eur, usd, true )
            assertNotNull ( dl1 )
            assertTrue ( dl1.getBidLimit () <= 0.0 )
            assertTrue ( dl1.getOfferLimit () > 0.0 )


            // now do a trade in the bid side and it should be rejected.
            Trade trade1 = prepareSingleLegTrade( tradeAmt, false, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd )
            assertTrue ( MessageStatus.FAILURE.equals ( cwm1.getStatus () ) )

            // Now take an offer side trade for more than what is available
            Trade trade2 = prepareSingleLegTrade( 200000, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowRiders riders = new CreditWorkflowRiders()
            riders.setBypassTenorRestrictions ( true )
            riders.setExcessCreditUtilizationAllowed ( true )
            CreditWorkflowMessage cwm2 = creditMgr.takeCredit( trade2, lpLe, ddOrg, lpTpForDd, true, true, true, riders )
            assertTrue ( MessageStatus.SUCCESS.equals ( cwm2.getStatus () ) )
            validateCreditUtilizationEvents( cwm2 )

            // now do a bid trade
            Trade trade3 = prepareSingleLegTrade( 100000, false, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowMessage cwm3 = creditMgr.takeCredit( trade3, lpLe, ddOrg, lpTpForDd )
            assertTrue ( MessageStatus.SUCCESS.equals ( cwm3.getStatus () ) )
            validateCreditUtilizationEvents( cwm3 )

            // now check the balance USD amount and do a trade just more than the available USD amount
            CurrencyPosition usdPos = CreditUtilC.getCurrencyAccountBalance ( lpOrg, lpTpForDd, usd  )
            double amt = Math.abs( usdPos.getNetAccountAmountBigDecimal ().doubleValue () )
            log( "USD position=" + amt )

            Trade trade4 = prepareSingleLegTrade( amt * 1.1, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowMessage cwm4 = creditMgr.takeCredit( trade4, lpLe, ddOrg, lpTpForDd )
            assertTrue ( MessageStatus.FAILURE.equals ( cwm4.getStatus () ) )

            // now do a bid trade within the balance
            Trade trade5 = prepareSingleLegTrade( amt * 0.9, false, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowMessage cwm5 = creditMgr.takeCredit( trade5, lpLe, ddOrg, lpTpForDd )
            assertTrue ( MessageStatus.SUCCESS.equals ( cwm5.getStatus () ) )
            validateCreditUtilizationEvents( cwm5 )

            CurrencyPosition usdPos1 = CreditUtilC.getCurrencyAccountBalance ( lpOrg, lpTpForDd, usd  )
            double amt1 = Math.abs( usdPos1.getNetAccountAmountBigDecimal ().doubleValue () )
            log( "USD position1=" + amt1 )
        }
        catch ( Exception e )
        {
            fail ( "testAggregateNetCashSettlementWithExcessUtilizationAllowed", e )
        }
        finally
        {
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 )
        }
    }

    void testAggregateNetCashSettlementWithExcessUtilizationAllowedAndLimitCheckEnabled()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )

            Currency usd = CurrencyFactory.getCurrency ( "USD" )
            Currency eur = CurrencyFactory.getCurrency ( "EUR" )

            Currency limitCcy = CreditUtilC.getCounterpartyCreditLimitCurrency( lpOrg, ddOrg, lpTpForDd )
            if ( !limitCcy.isSameAs( CurrencyFactory.getCurrency( "USD" ) ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( lpOrg, CurrencyFactory.getCurrency( "USD" ) )
                creditAdminSvc.reinitialize( lpOrg )
            }

            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_NET_CASH_SETTLEMENT_CALCULATOR, 10000 )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, null, 10000 )


            double tradeAmt = 100000
            DealingLimit dl = CreditLimitSubscriptionManagerC.getInstance ().getAvailableBaseCurrencyBilateralCreditLimit ( lpLe, ddLe, spotDate, eur, usd, true )
            assertNotNull ( dl )
            assertTrue ( dl.getBidLimit () > 0.0 )
            assertTrue ( dl.getOfferLimit () > 0.0 )

            // test trade prior to addign any balance
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd )
            assertTrue ( MessageStatus.FAILURE.equals ( cwm.getStatus () ) )

            sleepFor ( 1000 )

            Trade trade2 = prepareSingleLegTrade( 200000, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowRiders riders = new CreditWorkflowRiders()
            riders.setExcessCreditUtilizationAllowed ( true )
            CreditWorkflowMessage cwm2 = creditMgr.takeCredit( trade2, lpLe, ddOrg, lpTpForDd, true, true, true, riders )
            assertTrue ( MessageStatus.SUCCESS.equals ( cwm2.getStatus () ) )
            validateCreditUtilizationEvents( cwm2 )
        }
        catch ( Exception e )
        {
            fail ( "testAggregateNetCashSettlementWithExcessUtilizationAllowedAndLimitCheckEnabled", e )
        }
        finally
        {
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 )
        }
    }

    void testSimpleAggregateNetCashSettlementWithLimitCheckEnabled()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )

            Currency usd = CurrencyFactory.getCurrency ( "USD" )
            Currency eur = CurrencyFactory.getCurrency ( "EUR" )

            Currency limitCcy = CreditUtilC.getCounterpartyCreditLimitCurrency( lpOrg, ddOrg, lpTpForDd )
            if ( !limitCcy.isSameAs( CurrencyFactory.getCurrency( "USD" ) ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( lpOrg, CurrencyFactory.getCurrency( "USD" ) )
                creditAdminSvc.reinitialize( lpOrg )
            }

            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_NET_CASH_SETTLEMENT_CALCULATOR, 10000 )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, null, 10000 )

            CreditUtilization cu = getAggregateCreditUtilization ( lpLe, lpTpForDd, spotDate )
            CurrencyPositionCollection cpc = cu.getCurrencyPositions ()

            double tradeAmt = 1000
            DealingLimit dl = CreditLimitSubscriptionManagerC.getInstance ().getAvailableBaseCurrencyBilateralCreditLimit ( lpLe, ddLe, spotDate, eur, usd, true )
            assertNotNull ( dl )
            double bidLimit = dl.getBidLimit ()
            double offerLimit = dl.getOfferLimit ()
            log( "before trade. bidLimit=" + bidLimit + ",offerLimit=" + offerLimit + ",cpc=" + cpc )
            assertTrue ( bidLimit > 0.0 )
            assertTrue ( offerLimit > 0.0 )

            IdcTransaction tx = initTransaction ( true )
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd )
            assertTrue ( MessageStatus.SUCCESS.equals ( cwm.getStatus () ) )
            validateCreditUtilizationEvents( cwm )
            commitTransaction ( tx )

            double usedAmt = cu.getUsedAmount()
            assertTrue( "used amount=" + usedAmt, Math.abs ( usedAmt - tradeAmt ) < MINIMUM )
            assertEquals( "available amount=" + cu.getAvailableMarginReserveAmount(), cu.getAdjustedLimit() - usedAmt, cu.getAvailableMarginReserveAmount() )

            sleepFor ( 1000 )

            // now deposit USD amount
            creditAdminSvc.deposit ( lpOrg, lpTpForDd, usd, 900, "DEP" + System.currentTimeMillis (), null, null, "DEP" + System.currentTimeMillis () )

            usedAmt = cu.getUsedAmount()
            assertEquals( "used amount=" + usedAmt, tradeAmt - 900, usedAmt )
            assertEquals( "available amount=" + cu.getAvailableMarginReserveAmount(), cu.getAdjustedLimit() - usedAmt, cu.getAvailableMarginReserveAmount() )
        }
        catch ( Exception e )
        {
            fail ( "testSimpleAggregateNetCashSettlementWithLimitCheckEnabled", e )
        }
        finally
        {
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 )
        }
    }

    void testAggregateNetCashSettlementWithSkipUpdateReceivable_Bid()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )

            Currency usd = CurrencyFactory.getCurrency ( "USD" )
            Currency eur = CurrencyFactory.getCurrency ( "EUR" )

            Currency limitCcy = CreditUtilC.getCounterpartyCreditLimitCurrency( lpOrg, ddOrg, lpTpForDd )
            if ( !limitCcy.isSameAs( CurrencyFactory.getCurrency( "USD" ) ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( lpOrg, CurrencyFactory.getCurrency( "USD" ) )
                creditAdminSvc.reinitialize( lpOrg )
            }

            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_NET_CASH_SETTLEMENT_CALCULATOR, 0.0 )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, null, 10000 )


            double tradeAmt = 1000

            // test trade on trade date. This should not have any impact on the utilization.
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd )
            assertTrue ( MessageStatus.FAILURE.equals ( cwm.getStatus () ) )

            sleepFor ( 1000 )

            // now deposit USD amount
            double depositAmt = 10000
            creditAdminSvc.deposit ( lpOrg, lpTpForDd, usd, 10000, "DEP" + System.currentTimeMillis (), null, null, "DEP" + System.currentTimeMillis () )

            Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowRiders riders = new CreditWorkflowRiders ()
            riders.setSkipUpdateAccountNetReceivable ( true )
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd, true, true, true, riders )
            assertTrue ( MessageStatus.SUCCESS.equals ( cwm1.getStatus () ) )
            validateCreditUtilizationEvents( cwm1 )

            CreditUtilization aggCu = getAggregateCreditUtilization ( lpLe, lpTpForDd, spotDate )
            assertNotNull ( aggCu )
            CurrencyPositionCollection cpc = aggCu.getCurrencyPositions ()
            assertNotNull ( cpc )
            CurrencyPosition eurCp = cpc.getCurrencyPosition ( eur )
            CurrencyPosition usdCp = cpc.getCurrencyPosition ( usd )
            assertTrue ( "EUR position should be zero.", eurCp.getNetAmount () == 0.0 )
            assertTrue ( "USD position should not be zero.", Math.abs( usdCp.getNetAmount () ) != depositAmt )

            CreditWorkflowMessage cwm2 = creditMgr.updateBilateralCreditUtilizationAmount ( lpLe, ddLe, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS )
            assertTrue ( MessageStatus.SUCCESS.equals ( cwm2.getStatus () ) )
            assertTrue ( "EUR position should not be zero.", eurCp.getNetAmount () != 0.0 )
            assertTrue ( "USD position should not be zero.", Math.abs( usdCp.getNetAmount () ) != depositAmt )
        }
        catch ( Exception e )
        {
            fail ( "testAggregateNetCashSettlementWithSkipUpdateReceivable_Bid", e )
        }
        finally
        {
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 )
        }
    }

    void testAggregateNetCashSettlementWithSkipUpdateReceivable_Offer()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )

            Currency usd = CurrencyFactory.getCurrency ( "USD" )
            Currency eur = CurrencyFactory.getCurrency ( "EUR" )

            Currency limitCcy = CreditUtilC.getCounterpartyCreditLimitCurrency( lpOrg, ddOrg, lpTpForDd )
            if ( !limitCcy.isSameAs( CurrencyFactory.getCurrency( "USD" ) ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( lpOrg, CurrencyFactory.getCurrency( "USD" ) )
                creditAdminSvc.reinitialize( lpOrg )
            }

            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_NET_CASH_SETTLEMENT_CALCULATOR, 0.0 )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, null, 10000 )


            double tradeAmt = 1000

            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd )
            assertTrue ( MessageStatus.FAILURE.equals ( cwm.getStatus () ) )

            sleepFor ( 1000 )

            // now deposit USD amount
            double depositAmt = 10000
            creditAdminSvc.deposit ( lpOrg, lpTpForDd, eur, 10000, "DEP" + System.currentTimeMillis (), null, null, "DEP" + System.currentTimeMillis () )

            Trade trade1 = prepareSingleLegTrade( tradeAmt, false, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowRiders riders = new CreditWorkflowRiders ()
            riders.setSkipUpdateAccountNetReceivable ( true )
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd, true, true, true, riders )
            assertTrue ( MessageStatus.SUCCESS.equals ( cwm1.getStatus () ) )
            validateCreditUtilizationEvents( cwm1 )

            CreditUtilization aggCu = getAggregateCreditUtilization ( lpLe, lpTpForDd, spotDate )
            assertNotNull ( aggCu )
            CurrencyPositionCollection cpc = aggCu.getCurrencyPositions ()
            assertNotNull ( cpc )
            CurrencyPosition eurCp = cpc.getCurrencyPosition ( eur )
            CurrencyPosition usdCp = cpc.getCurrencyPosition ( usd )
            assertTrue ( "EUR position should not be zero.", Math.abs ( eurCp.getNetAmount () ) != depositAmt )
            assertTrue ( "USD position should be zero.", Math.abs( usdCp.getNetAmount () ) == 0.0 )

            CreditWorkflowMessage cwm2 = creditMgr.updateBilateralCreditUtilizationAmount ( lpLe, ddLe, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS )
            assertTrue ( MessageStatus.SUCCESS.equals ( cwm2.getStatus () ) )
            assertTrue ( "EUR position should not be zero.", eurCp.getNetAmount () != depositAmt )
            assertTrue ( "USD position should not be zero.", Math.abs( usdCp.getNetAmount () ) != 0.0 )
        }
        catch ( Exception e )
        {
            fail ( "testAggregateNetCashSettlementWithSkipUpdateReceivable_Offer", e )
        }
        finally
        {
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 )
        }
    }

    void testAggregateNetCashSettlementWithSkipUpdateReceivable_Bid_LimitCheckEnabled()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )

            Currency usd = CurrencyFactory.getCurrency ( "USD" )
            Currency eur = CurrencyFactory.getCurrency ( "EUR" )

            Currency limitCcy = CreditUtilC.getCounterpartyCreditLimitCurrency( lpOrg, ddOrg, lpTpForDd )
            if ( !limitCcy.isSameAs( CurrencyFactory.getCurrency( "USD" ) ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( lpOrg, CurrencyFactory.getCurrency( "USD" ) )
                creditAdminSvc.reinitialize( lpOrg )
            }

            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_NET_CASH_SETTLEMENT_CALCULATOR, 10000 )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, null, 10000 )


            double tradeAmt = 1000

            Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowRiders riders = new CreditWorkflowRiders ()
            riders.setSkipUpdateAccountNetReceivable ( true )
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd, true, true, true, riders )
            assertTrue ( MessageStatus.SUCCESS.equals ( cwm1.getStatus () ) )
            validateCreditUtilizationEvents( cwm1 )

            CreditUtilization aggCu = getAggregateCreditUtilization ( lpLe, lpTpForDd, spotDate )
            assertNotNull ( aggCu )
            CurrencyPositionCollection cpc = aggCu.getCurrencyPositions ()
            assertNotNull ( cpc )
            CurrencyPosition eurCp = cpc.getCurrencyPosition ( eur )
            CurrencyPosition usdCp = cpc.getCurrencyPosition ( usd )
            assertTrue ( "EUR position should be zero.", eurCp.getNetAmount () == 0.0 )
            assertTrue ( "USD position should not be zero.", Math.abs( usdCp.getNetAmount () ) != 0.0 )

            CreditWorkflowMessage cwm2 = creditMgr.updateBilateralCreditUtilizationAmount ( lpLe, ddLe, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS )
            assertTrue ( MessageStatus.SUCCESS.equals ( cwm2.getStatus () ) )
            assertTrue ( "EUR position should not be zero.", eurCp.getNetAmount () != 0.0 )
            assertTrue ( "USD position should not be zero.", Math.abs( usdCp.getNetAmount () ) != 0.0 )
        }
        catch ( Exception e )
        {
            fail ( "testAggregateNetCashSettlementWithSkipUpdateReceivable_Bid", e )
        }
        finally
        {
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 )
        }
    }

    void testAggregateNetCashSettlementWithSkipUpdateReceivable_Offer_LimitCheckEnabled()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )

            Currency usd = CurrencyFactory.getCurrency ( "USD" )
            Currency eur = CurrencyFactory.getCurrency ( "EUR" )

            Currency limitCcy = CreditUtilC.getCounterpartyCreditLimitCurrency( lpOrg, ddOrg, lpTpForDd )
            if ( !limitCcy.isSameAs( CurrencyFactory.getCurrency( "USD" ) ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( lpOrg, CurrencyFactory.getCurrency( "USD" ) )
                creditAdminSvc.reinitialize( lpOrg )
            }

            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_NET_CASH_SETTLEMENT_CALCULATOR, 10000 )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, null, 10000 )


            double tradeAmt = 1000
            Trade trade1 = prepareSingleLegTrade( tradeAmt, false, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowRiders riders = new CreditWorkflowRiders ()
            riders.setSkipUpdateAccountNetReceivable ( true )
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd, true, true, true, riders )
            assertTrue ( MessageStatus.SUCCESS.equals ( cwm1.getStatus () ) )
            validateCreditUtilizationEvents( cwm1 )

            CreditUtilization aggCu = getAggregateCreditUtilization ( lpLe, lpTpForDd, spotDate )
            assertNotNull ( aggCu )
            CurrencyPositionCollection cpc = aggCu.getCurrencyPositions ()
            assertNotNull ( cpc )
            CurrencyPosition eurCp = cpc.getCurrencyPosition ( eur )
            CurrencyPosition usdCp = cpc.getCurrencyPosition ( usd )
            assertTrue ( "EUR position should not be zero.", Math.abs ( eurCp.getNetAmount () ) != 0.0 )
            assertTrue ( "USD position should be zero.", Math.abs( usdCp.getNetAmount () ) == 0.0 )

            CreditWorkflowMessage cwm2 = creditMgr.updateBilateralCreditUtilizationAmount ( lpLe, ddLe, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS )
            assertTrue ( MessageStatus.SUCCESS.equals ( cwm2.getStatus () ) )
            assertTrue ( "EUR position should not be zero.", eurCp.getNetAmount () != 0.0 )
            assertTrue ( "USD position should not be zero.", Math.abs( usdCp.getNetAmount () ) != 0.0 )
        }
        catch ( Exception e )
        {
            fail ( "testAggregateNetCashSettlementWithSkipUpdateReceivable_Offer_LimitCheckEnabled", e )
        }
        finally
        {
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 )
        }
    }

    void testAggregateNetCashSettlementWithSkipUpdateReceivableMultiFill_Bid()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )

            Currency usd = CurrencyFactory.getCurrency ( "USD" )
            Currency eur = CurrencyFactory.getCurrency ( "EUR" )

            Currency limitCcy = CreditUtilC.getCounterpartyCreditLimitCurrency( lpOrg, ddOrg, lpTpForDd )
            if ( !limitCcy.isSameAs( CurrencyFactory.getCurrency( "USD" ) ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( lpOrg, CurrencyFactory.getCurrency( "USD" ) )
                creditAdminSvc.reinitialize( lpOrg )
            }

            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_NET_CASH_SETTLEMENT_CALCULATOR, 0.0 )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, null, 10000 )


            double tradeAmt = 1000

            // test trade on trade date. This should not have any impact on the utilization.
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd )
            assertTrue ( MessageStatus.FAILURE.equals ( cwm.getStatus () ) )

            sleepFor ( 1000 )

            // now deposit USD amount
            double depositAmt = 10000
            creditAdminSvc.deposit ( lpOrg, lpTpForDd, usd, 10000, "DEP" + System.currentTimeMillis (), null, null, "DEP" + System.currentTimeMillis () )

            Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowRiders riders = new CreditWorkflowRiders ()
            riders.setSkipUpdateAccountNetReceivable ( true )
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd, true, true, true, riders )
            assertTrue ( MessageStatus.SUCCESS.equals ( cwm1.getStatus () ) )
            validateCreditUtilizationEvents( cwm1 )

            CreditUtilization aggCu = getAggregateCreditUtilization ( lpLe, lpTpForDd, spotDate )
            assertNotNull ( aggCu )
            CurrencyPositionCollection cpc = aggCu.getCurrencyPositions ()
            assertNotNull ( cpc )
            CurrencyPosition eurCp = cpc.getCurrencyPosition ( eur )
            CurrencyPosition usdCp = cpc.getCurrencyPosition ( usd )
            assertTrue ( "EUR position should be zero.", eurCp.getNetAmount () == 0.0 )
            assertTrue ( "USD position should not be zero.", Math.abs( usdCp.getNetAmount () ) != depositAmt )

            Collection<Trade> fillTrades = new ArrayList<Trade> ()
            fillTrades.add ( trade1 )

            CreditWorkflowMessage cwm2 = creditMgr.updateBilateralMultiFillCredit ( lpLe, ddLe, trade1, fillTrades, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS )
            assertTrue ( MessageStatus.SUCCESS.equals ( cwm2.getStatus () ) )
            assertTrue ( "EUR position should not be zero.", eurCp.getNetAmount () != 0.0 )
            assertTrue ( "USD position should not be zero.", Math.abs( usdCp.getNetAmount () ) != depositAmt )
        }
        catch ( Exception e )
        {
            fail ( "testAggregateNetCashSettlementWithSkipUpdateReceivableMultiFill_Bid", e )
        }
        finally
        {
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 )
        }
    }

    void testAggregateNetCashSettlementWithSkipUpdateReceivableMultiFill_Offer()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )

            Currency usd = CurrencyFactory.getCurrency ( "USD" )
            Currency eur = CurrencyFactory.getCurrency ( "EUR" )

            Currency limitCcy = CreditUtilC.getCounterpartyCreditLimitCurrency( lpOrg, ddOrg, lpTpForDd )
            if ( !limitCcy.isSameAs( CurrencyFactory.getCurrency( "USD" ) ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( lpOrg, CurrencyFactory.getCurrency( "USD" ) )
                creditAdminSvc.reinitialize( lpOrg )
            }

            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_NET_CASH_SETTLEMENT_CALCULATOR, 0.0 )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, null, 10000 )


            double tradeAmt = 1000

            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd )
            assertTrue ( MessageStatus.FAILURE.equals ( cwm.getStatus () ) )

            sleepFor ( 1000 )

            // now deposit USD amount
            double depositAmt = 10000
            creditAdminSvc.deposit ( lpOrg, lpTpForDd, eur, 10000, "DEP" + System.currentTimeMillis (), null, null, "DEP" + System.currentTimeMillis () )

            Trade trade1 = prepareSingleLegTrade( tradeAmt, false, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowRiders riders = new CreditWorkflowRiders ()
            riders.setSkipUpdateAccountNetReceivable ( true )
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd, true, true, true, riders )
            assertTrue ( MessageStatus.SUCCESS.equals ( cwm1.getStatus () ) )
            validateCreditUtilizationEvents( cwm1 )

            CreditUtilization aggCu = getAggregateCreditUtilization ( lpLe, lpTpForDd, spotDate )
            assertNotNull ( aggCu )
            CurrencyPositionCollection cpc = aggCu.getCurrencyPositions ()
            assertNotNull ( cpc )
            CurrencyPosition eurCp = cpc.getCurrencyPosition ( eur )
            CurrencyPosition usdCp = cpc.getCurrencyPosition ( usd )
            assertTrue ( "EUR position should not be zero.", Math.abs ( eurCp.getNetAmount () ) != depositAmt )
            assertTrue ( "USD position should be zero.", Math.abs( usdCp.getNetAmount () ) == 0.0 )

            Collection<Trade> fillTrades = new ArrayList<Trade> ()
            fillTrades.add ( trade1 )

            CreditWorkflowMessage cwm2 = creditMgr.updateBilateralMultiFillCredit ( lpLe, ddLe, trade1, fillTrades, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS )
            assertTrue ( MessageStatus.SUCCESS.equals ( cwm2.getStatus () ) )
            assertTrue ( "EUR position should not be zero.", eurCp.getNetAmount () != depositAmt )
            assertTrue ( "USD position should not be zero.", Math.abs( usdCp.getNetAmount () ) != 0.0 )
        }
        catch ( Exception e )
        {
            fail ( "testAggregateNetCashSettlementWithSkipUpdateReceivableMultiFill_Offer", e )
        }
        finally
        {
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 )
        }
    }

    void testAggregateNetCashSettlementWithSkipUpdateReceivableMultiFill_Bid_LimitCheckEnabled()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )

            Currency usd = CurrencyFactory.getCurrency ( "USD" )
            Currency eur = CurrencyFactory.getCurrency ( "EUR" )

            Currency limitCcy = CreditUtilC.getCounterpartyCreditLimitCurrency( lpOrg, ddOrg, lpTpForDd )
            if ( !limitCcy.isSameAs( CurrencyFactory.getCurrency( "USD" ) ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( lpOrg, CurrencyFactory.getCurrency( "USD" ) )
                creditAdminSvc.reinitialize( lpOrg )
            }

            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_NET_CASH_SETTLEMENT_CALCULATOR, 10000 )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, null, 10000 )


            double tradeAmt = 1000

            Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowRiders riders = new CreditWorkflowRiders ()
            riders.setSkipUpdateAccountNetReceivable ( true )
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd, true, true, true, riders )
            assertTrue ( MessageStatus.SUCCESS.equals ( cwm1.getStatus () ) )
            validateCreditUtilizationEvents( cwm1 )

            CreditUtilization aggCu = getAggregateCreditUtilization ( lpLe, lpTpForDd, spotDate )
            assertNotNull ( aggCu )
            CurrencyPositionCollection cpc = aggCu.getCurrencyPositions ()
            assertNotNull ( cpc )
            CurrencyPosition eurCp = cpc.getCurrencyPosition ( eur )
            CurrencyPosition usdCp = cpc.getCurrencyPosition ( usd )
            assertTrue ( "EUR position should be zero.", eurCp.getNetAmount () == 0.0 )
            assertTrue ( "USD position should not be zero.", Math.abs( usdCp.getNetAmount () ) != 0.0 )

            Collection<Trade> fillTrades = new ArrayList<Trade> ()
            fillTrades.add ( trade1 )

            CreditWorkflowMessage cwm2 = creditMgr.updateBilateralMultiFillCredit ( lpLe, ddLe, trade1, fillTrades, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS )
            assertTrue ( MessageStatus.SUCCESS.equals ( cwm2.getStatus () ) )
            assertTrue ( "EUR position should not be zero.", eurCp.getNetAmount () != 0.0 )
            assertTrue ( "USD position should not be zero.", Math.abs( usdCp.getNetAmount () ) != 0.0 )
        }
        catch ( Exception e )
        {
            fail ( "testAggregateNetCashSettlementWithSkipUpdateReceivableMultiFill_Bid_LimitCheckEnabled", e )
        }
        finally
        {
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 )
        }
    }

    void testAggregateNetCashSettlementWithSkipUpdateReceivableMultiFill_Offer_LimitCheckEnabled()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )

            Currency usd = CurrencyFactory.getCurrency ( "USD" )
            Currency eur = CurrencyFactory.getCurrency ( "EUR" )

            Currency limitCcy = CreditUtilC.getCounterpartyCreditLimitCurrency( lpOrg, ddOrg, lpTpForDd )
            if ( !limitCcy.isSameAs( CurrencyFactory.getCurrency( "USD" ) ) )
            {
                creditAdminSvc.setDefaultLimitCurrency( lpOrg, CurrencyFactory.getCurrency( "USD" ) )
                creditAdminSvc.reinitialize( lpOrg )
            }

            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_NET_CASH_SETTLEMENT_CALCULATOR, 10000 )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, null, 10000 )


            double tradeAmt = 1000
            Trade trade1 = prepareSingleLegTrade( tradeAmt, false, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowRiders riders = new CreditWorkflowRiders ()
            riders.setSkipUpdateAccountNetReceivable ( true )
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd, true, true, true, riders )
            assertTrue ( MessageStatus.SUCCESS.equals ( cwm1.getStatus () ) )
            validateCreditUtilizationEvents( cwm1 )

            CreditUtilization aggCu = getAggregateCreditUtilization ( lpLe, lpTpForDd, spotDate )
            assertNotNull ( aggCu )
            CurrencyPositionCollection cpc = aggCu.getCurrencyPositions ()
            assertNotNull ( cpc )
            CurrencyPosition eurCp = cpc.getCurrencyPosition ( eur )
            CurrencyPosition usdCp = cpc.getCurrencyPosition ( usd )
            assertTrue ( "EUR position should not be zero.", Math.abs ( eurCp.getNetAmount () ) != 0.0 )
            assertTrue ( "USD position should be zero.", Math.abs( usdCp.getNetAmount () ) == 0.0 )

            Collection<Trade> fillTrades = new ArrayList<Trade> ()
            fillTrades.add ( trade1 )

            CreditWorkflowMessage cwm2 = creditMgr.updateBilateralMultiFillCredit ( lpLe, ddLe, trade1, fillTrades, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS )
            assertTrue ( MessageStatus.SUCCESS.equals ( cwm2.getStatus () ) )
            assertTrue ( "EUR position should not be zero.", eurCp.getNetAmount () != 0.0 )
            assertTrue ( "USD position should not be zero.", Math.abs( usdCp.getNetAmount () ) != 0.0 )
        }
        catch ( Exception e )
        {
            fail ( "testAggregateNetCashSettlementWithSkipUpdateReceivableMultiFill_Offer_LimitCheckEnabled", e )
        }
        finally
        {
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 )
        }
    }

    void testSimpleAggregateSettlementReceivableNetting()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, null, 10000 )
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_NET_SETTLEMENT_RECEIVABLE_CALCULATOR, 10000 )
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "Credit limit before first bid trade take Credit : " + limit0 )
            double tradeAmt = 1000
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd )
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "Credit limit after first bid trade take Credit : " + limit1 + " success : " + cwm.getStatus() )
            assertEquals( "before limit=" + limit0 + ",limitAfter=" + limit1, true, Math.abs( limit0 - limit1 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM )
            validateCreditUtilizationEvents( cwm )

            // Now take an offer side trade.
            Trade trade1 = prepareSingleLegTrade( tradeAmt, false, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd )
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "Credit limit after second offer trade take Credit : " + limit2 + " success : " + cwm1.getStatus() )
            validateCreditUtilizationEvents( cwm1 )
            assertEquals( "before limit=" + limit1 + ",after limit=" + limit2, true, Math.abs( limit0 - limit2 ) < CREDIT_CALCULATION_MINIMUM )
        }
        catch ( Exception e )
        {
            e.printStackTrace()
            fail()
        }
        finally
        {
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 )
        }
    }

    void testAggregateSettlementReceivableNettingWithDifferentValueDates()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, null, 10000 )
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_NET_SETTLEMENT_RECEIVABLE_CALCULATOR, 10000 )
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "On Spot date, credit limit before take Credit : " + limit0 )
            double tradeAmt = 1000
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd )
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "On spot date, credit limit after take Credit : " + limit1 + " success : " + cwm.getStatus() )
            assertEquals( "before limit=" + limit0 + ",limit after=" + limit1, true, Math.abs( limit0 - limit1 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM )
            validateCreditUtilizationEvents( cwm )

            // Now do a trade on spot next date
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate.addDays( 7 ), GROSS_NOTIONAL_CLASSIFICATION )
            log( "On 1w date, credit limit before take Credit : " + limit2 )
            Trade trade1 = prepareSingleLegTrade( tradeAmt, false, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate.addDays( 7 ) )
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd )
            double limit3 = getAvailableCreditLimit( lpTpForDd, spotDate.addDays( 7 ), GROSS_NOTIONAL_CLASSIFICATION )
            log( "On 1w date, credit limit  after take Credit : " + limit3 + " success : " + cwm1.getStatus() )
            assertEquals( "before limit=" + limit2 + ",limit after=" + limit3, true, Math.abs( limit3 - limit0 ) < CREDIT_CALCULATION_MINIMUM )
            validateCreditUtilizationEvents( cwm1 )
        }
        catch ( Exception e )
        {
            e.printStackTrace()
            fail()
        }
        finally
        {
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 )
        }
    }

    void testAggregateSettlementReceivableNettingWithFXRateConversion()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, null, 10000 )
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_NET_SETTLEMENT_RECEIVABLE_CALCULATOR, 10000 )
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "Credit limit before first bid trade take Credit : " + limit0 )
            double tradeAmt = 1000
            Trade trade = prepareSingleLegTrade( tradeAmt, true, true, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd )
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            double usedAmt1 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "EUR", tradeAmt, true )
            log( "Credit limit after first bid trade take Credit : " + limit1 + " success : " + cwm.getStatus() + ",utilizedAmt=" + usedAmt1 )
            assertEquals( "before limit=" + limit0 + ",limit after=" + limit1 + ",usedAmt=" + usedAmt1, true, Math.abs( limit0 - limit1 - usedAmt1 ) < CREDIT_CALCULATION_MINIMUM )
            validateCreditUtilizationEvents( cwm )

            // Now take an offer side trade.
            Trade trade1 = prepareSingleLegTrade( tradeAmt, false, true, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd )
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "Credit limit after second offer trade take Credit : " + limit2 + " success : " + cwm1.getStatus() )
            validateCreditUtilizationEvents( cwm1 )
            assertEquals( "before limit=" + limit1 + ",limit after=" + limit2, true, Math.abs( limit0 - limit2 ) < CREDIT_CALCULATION_MINIMUM )
        }
        catch ( Exception e )
        {
            e.printStackTrace()
            fail()
        }
        finally
        {
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 )
        }
    }

    void testAggregateSettlementReceivableNettingWithNoCreditLimitCurrency()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, null, 10000 )
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_NET_SETTLEMENT_RECEIVABLE_CALCULATOR, 10000 )
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "Credit limit before first bid trade take Credit : " + limit0 )
            double tradeAmt = 1000
            Trade trade = prepareSingleLegTrade( tradeAmt, true, true, EURGBP, lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd )
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            double usedAmt1 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "EUR", tradeAmt, true )
            log( "Credit limit after first bid trade take Credit : " + limit1 + " success : " + cwm.getStatus() + ",utilizedAmt=" + usedAmt1 )
            printCurrencyPositions( cwm )
            assertEquals( "before limit=" + limit0 + ",limit after=" + limit1 + ",usedAmt=" + usedAmt1, true, Math.abs( limit0 - limit1 - usedAmt1 ) < CREDIT_CALCULATION_MINIMUM )
            validateCreditUtilizationEvents( cwm )

            // Now take an offer side trade.
            Trade trade1 = prepareSingleLegTrade( tradeAmt, false, true, EURGBP, lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd )
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "Credit limit after second offer trade take Credit : " + limit2 + " success : " + cwm1.getStatus() )
            printCurrencyPositions( cwm1 )
            validateCreditUtilizationEvents( cwm1 )
            assertEquals( "before limit=" + limit1 + ",limit after=" + limit2, true, Math.abs( limit0 - limit2 ) < CREDIT_CALCULATION_MINIMUM )
        }
        catch ( Exception e )
        {
            e.printStackTrace()
            fail()
        }
        finally
        {
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 )
        }
    }

    void testAggregateSettlementReceivableReserve()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, null, 10000 )
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_NET_SETTLEMENT_RECEIVABLE_CALCULATOR, 10000 )
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "Credit limit before first bid reserve Credit : " + limit0 )
            double tradeAmt = 1000
            Request request = prepareSingleLegRequest( prepareSingleLegTrade( tradeAmt, true, true, EURGBP, lpOrg, lpTpForDd, bidRates[0], spotDate ) )
            CreditWorkflowMessage cwm = creditMgr.reserveCredit( request, lpLe, ddOrg, lpTpForDd )
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            double usedAmt1 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "EUR", tradeAmt, true )
            log( "Credit limit after first reserve Credit : " + limit1 + " success : " + cwm.getStatus() + ",utilizedAmt=" + usedAmt1 )
            printCurrencyPositions( cwm )
            assertEquals( "before limit=" + limit0 + ",limit after=" + limit1 + ",usedAmt=" + usedAmt1, true, Math.abs( limit0 - limit1 - usedAmt1 ) < CREDIT_CALCULATION_MINIMUM )
            assertEquals( "success=" + cwm.getStatusName(), cwm.getStatus().equals( MessageStatus.SUCCESS ), true )
            validateCreditUtilizationEvents( cwm )

            // now release the credit.
            CreditWorkflowMessage cwm1 = creditMgr.undoReserve( request, lpLe, ddOrg, lpTpForDd )
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "Credit limit after undoing reserve : " + limit2 + " success : " + cwm1.getStatus() )
            printCurrencyPositions( cwm1 )
            assertEquals( "Reserve amount should be zero. limit0=" + limit0 + ",limit2=" + limit2, true, Math.abs( limit2 - limit0 ) < CREDIT_CALCULATION_MINIMUM )
            assertEquals( "success=" + cwm1.getStatusName(), cwm1.getStatus().equals( MessageStatus.SUCCESS ), true )
        }
        catch ( Exception e )
        {
            e.printStackTrace()
            fail()
        }
        finally
        {
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 )
        }
    }

    void testAggregateSettlementReceivableReserveFailure()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, null, 10000 )
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_NET_SETTLEMENT_RECEIVABLE_CALCULATOR, 10000 )
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "Credit limit before first bid reserve Credit : " + limit0 )
            double tradeAmt = limit0 + 100000
            Request request = prepareSingleLegRequest( prepareSingleLegTrade( tradeAmt, true, true, EURGBP, lpOrg, lpTpForDd, bidRates[0], spotDate ) )
            CreditWorkflowMessage cwm = creditMgr.reserveCredit( request, lpLe, ddOrg, lpTpForDd )
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "Credit limit after first reserve Credit : " + limit1 + " success : " + cwm.getStatus() + ",before=" + limit0 )
            printCurrencyPositions( cwm )
            assertEquals( "before limit=" + limit0 + ",limit after=" + limit1, true, Math.abs( limit0 - limit1 ) < CREDIT_CALCULATION_MINIMUM )
            assertEquals( "success=" + cwm.getStatusName(), cwm.getStatus().equals( MessageStatus.SUCCESS ), false )
            validateCreditUtilizationEvents( cwm )
        }
        catch ( Exception e )
        {
            e.printStackTrace()
            fail()
        }
        finally
        {
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 )
        }
    }

    void testAggregateSettlementReceivableUndoCredit()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, null, 10000 )
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_NET_SETTLEMENT_RECEIVABLE_CALCULATOR, 10000 )

            // first do a trade to buy 1k USD vs EUR
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "Credit limit before first bid trade take Credit : " + limit1 )
            double tradeAmt = 1000
            Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd )
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "Credit limit after first bid trade take Credit : " + limit2 + " success : " + cwm1.getStatus() )
            assertEquals( "before limit=" + limit1 + ",limit after=" + limit2, true, Math.abs( limit1 - limit2 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM )
            assertEquals( "Message Status=" + cwm1.getStatusName(), cwm1.getStatus().equals( MessageStatus.SUCCESS ), true )
            validateCreditUtilizationEvents( cwm1 )

            // do another trade to sell 1k USD vs EUR. Now the net utilization should be zero.
            log( "Credit limit before second offer trade take Credit : " + limit2 )
            Trade trade2 = prepareSingleLegTrade( tradeAmt, false, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowMessage cwm2 = creditMgr.takeCredit( trade2, lpLe, ddOrg, lpTpForDd )
            double limit3 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "Credit limit after second offer trade take Credit : " + limit3 + " success : " + cwm2.getStatus() )
            assertEquals( "before limit=" + limit2 + ",limit after=" + limit3, true, Math.abs( limit3 - limit1 ) < CREDIT_CALCULATION_MINIMUM )
            assertEquals( "Message Status=" + cwm2.getStatusName(), cwm2.getStatus().equals( MessageStatus.SUCCESS ), true )
            validateCreditUtilizationEvents( cwm2 )

            // undo the last trade so that net utilization becomes that of first trade.
            CreditWorkflowMessage cwm3 = creditMgr.undoCredit( trade2, lpLe, ddOrg, lpTpForDd )
            double limit4 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "Credit limit after second offer trade undo Credit : " + limit4 + " success : " + cwm3.getStatus() )
            assertEquals( "before limit=" + limit3 + ",limit after=" + limit4, true, Math.abs( limit1 - limit4 - tradeAmt ) < CREDIT_CALCULATION_MINIMUM )
            assertEquals( "Message Status=" + cwm3.getStatusName(), cwm3.getStatus().equals( MessageStatus.SUCCESS ), true )
            validateCreditUtilizationEvents( cwm3 )
        }
        catch ( Exception e )
        {
            e.printStackTrace()
            fail()
        }
        finally
        {
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 )
        }
    }

    void testAggregateSettlementReceivableMultipleTradesWithDifferentCurrenciesBuyTerm()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, null, 10000 )
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_NET_SETTLEMENT_RECEIVABLE_CALCULATOR, 10000 )

            // first do a trade to buy 1k GBP vs EUR
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "Credit limit before first bid trade take Credit : " + limit1 )
            double tradeAmt = 1000
            Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURGBP, lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd )
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "Credit limit after first bid trade take Credit : " + limit2 + " success : " + cwm1.getStatus() )

            // in this case net receivable would be EURO 1k
            double usedAmt1 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "GBP", getTradeAmount( trade1, "GBP" ), true )
            assertEquals( "before limit=" + limit1 + ",limit after=" + limit2 + ",usedAmt=" + usedAmt1, true, Math.abs( limit1 - limit2 - usedAmt1 ) < CREDIT_CALCULATION_MINIMUM )
            assertEquals( "Message Status=" + cwm1.getStatusName(), cwm1.getStatus().equals( MessageStatus.SUCCESS ), true )
            validateCreditUtilizationEvents( cwm1 )

            // do a trade to buy 1k JPY vs CHF
            double limit3 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "Credit limit before second bid trade take Credit : " + limit3 )
            Trade trade2 = prepareSingleLegTrade( tradeAmt, true, false, "CHF/JPY", lpOrg, lpTpForDd, bidRates[4], spotDate )
            CreditWorkflowMessage cwm2 = creditMgr.takeCredit( trade2, lpLe, ddOrg, lpTpForDd )
            double limit4 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )

            // in this case, there will be two net receivables GBP xk and JPY xk.
            double usedAmt2 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "JPY", getTradeAmount( trade2, "JPY" ), true )
            log( "Credit limit after second bid trade take Credit : " + limit4 + " success : " + cwm2.getStatus() )
            assertEquals( "before limit=" + limit3 + ",limit after=" + limit4 + ",usedAmt=" + usedAmt2, true, Math.abs( limit3 - limit4 - usedAmt2 ) < CREDIT_CALCULATION_MINIMUM )
            assertEquals( "Message Status=" + cwm2.getStatusName(), cwm2.getStatus().equals( MessageStatus.SUCCESS ), true )
            validateCreditUtilizationEvents( cwm2 )

            // do a trade to buy 1k DKK vs AUD
            double limit5 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "Credit limit before second bid trade take Credit : " + limit5 )
            Trade trade3 = prepareSingleLegTrade( tradeAmt, true, false, "AUD/DKK", lpOrg, lpTpForDd, bidRates[4], spotDate )
            CreditWorkflowMessage cwm3 = creditMgr.takeCredit( trade3, lpLe, ddOrg, lpTpForDd )
            assertTrue( MessageStatus.SUCCESS.equals( cwm3.getStatus() ) )
        }
        catch ( Exception e )
        {
            e.printStackTrace()
            fail()
        }
        finally
        {
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 )
        }
    }

    void testAggregateSettlementReceivableMultipleTradesWithDifferentCurrenciesSellTerm()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, null, 100000 )
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_NET_SETTLEMENT_RECEIVABLE_CALCULATOR, 100000 )

            // first do a trade to sell 1k  GBP vs EUR
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "Credit limit before first bid trade take Credit : " + limit1 )
            double tradeAmt = 10000
            Trade trade1 = prepareSingleLegTrade( tradeAmt, false, false, EURGBP, lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd )
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "Credit limit after first bid trade take Credit : " + limit2 + " success : " + cwm1.getStatus() )

            // in this case net receivable would be EUR 1k
            double usedAmt1 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "EUR", getTradeAmount( trade1, "EUR" ), true )
            assertEquals( "before limit=" + limit1 + ",limit after=" + limit2 + ",usedAmt=" + usedAmt1, true, Math.abs( limit1 - limit2 - usedAmt1 ) < CREDIT_CALCULATION_MINIMUM )
            assertEquals( "Message Status=" + cwm1.getStatusName(), cwm1.getStatus().equals( MessageStatus.SUCCESS ), true )
            validateCreditUtilizationEvents( cwm1 )

            // do a trade to sell 1k JPY vs CHF
            double limit3 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "Credit limit before second bid trade take Credit : " + limit3 )
            Trade trade2 = prepareSingleLegTrade( tradeAmt, false, false, "CHF/JPY", lpOrg, lpTpForDd, bidRates[4], spotDate )
            CreditWorkflowMessage cwm2 = creditMgr.takeCredit( trade2, lpLe, ddOrg, lpTpForDd )
            double limit4 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )

            // in this case, there will be two net receivables EUR 1k and CHF 1k.
            double usedAmt2 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "CHF", getTradeAmount( trade2, "CHF" ), true )
            log( "Credit limit after second bid trade take Credit : " + limit4 + " success : " + cwm2.getStatus() )
            assertEquals( "before limit=" + limit3 + ",limit after=" + limit4 + ",usedAmt=" + usedAmt2, true, Math.abs( limit3 - limit4 - usedAmt2 ) < CREDIT_CALCULATION_MINIMUM )
            assertEquals( "Message Status=" + cwm2.getStatusName(), cwm2.getStatus().equals( MessageStatus.SUCCESS ), true )
            validateCreditUtilizationEvents( cwm2 )

            // do a trade to sell 1k DKK vs AUD
            double limit5 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "Credit limit before second bid trade take Credit : " + limit5 )
            Trade trade3 = prepareSingleLegTrade( tradeAmt, false, false, "AUD/DKK", lpOrg, lpTpForDd, bidRates[4], spotDate )
            CreditWorkflowMessage cwm3 = creditMgr.takeCredit( trade3, lpLe, ddOrg, lpTpForDd )
            double limit6 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )

            // in this case, there will be 3 receivable amounts EUR 1k, CHF 1k, and AUD 1k
            double usedAmt3 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "AUD", getTradeAmount( trade3, "AUD" ), true )
            log( "Credit limit after second bid trade take Credit : " + limit6 + " success : " + cwm3.getStatus() )
            assertEquals( "before limit=" + limit5 + ",limit after=" + limit6 + ",usedAmt=" + usedAmt3, true, Math.abs( limit5 - limit6 - usedAmt3 ) < CREDIT_CALCULATION_MINIMUM )
            assertEquals( "Message Status=" + cwm3.getStatusName(), cwm3.getStatus().equals( MessageStatus.SUCCESS ), true )
            validateCreditUtilizationEvents( cwm3 )
        }
        catch ( Exception e )
        {
            e.printStackTrace()
            fail()
        }
        finally
        {
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 )
        }
    }

    void testAggregateSettlementReceivableMultipleTradesWithDifferentCurrenciesBuyBase()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, null, 10000 )
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_NET_SETTLEMENT_RECEIVABLE_CALCULATOR, 10000 )

            // first do a trade to buy 1k EUR vs GBP
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "Credit limit before first bid trade take Credit : " + limit1 )
            double tradeAmt = 1000
            Trade trade1 = prepareSingleLegTrade( tradeAmt, true, true, EURGBP, lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd )
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "Credit limit after first bid trade take Credit : " + limit2 + " success : " + cwm1.getStatus() )

            // in this case net receivable would be EUR 1k
            double usedAmt1 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "EUR", tradeAmt, true )
            assertEquals( "before limit=" + limit1 + ",limit after=" + limit2 + ",usedAmt=" + usedAmt1, true, Math.abs( limit1 - limit2 - usedAmt1 ) < CREDIT_CALCULATION_MINIMUM )
            assertEquals( "Message Status=" + cwm1.getStatusName(), cwm1.getStatus().equals( MessageStatus.SUCCESS ), true )
            validateCreditUtilizationEvents( cwm1 )

            // do a trade to buy 1k CHF vs JPY
            double limit3 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "Credit limit before second bid trade take Credit : " + limit3 )
            Trade trade2 = prepareSingleLegTrade( tradeAmt, true, true, "CHF/JPY", lpOrg, lpTpForDd, bidRates[4], spotDate )
            CreditWorkflowMessage cwm2 = creditMgr.takeCredit( trade2, lpLe, ddOrg, lpTpForDd )
            double limit4 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )

            // in this case, there will be two net receivables EUR 1k and CHF 1k.
            double usedAmt2 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "CHF", tradeAmt, true )
            log( "Credit limit after second bid trade take Credit : " + limit4 + " success : " + cwm2.getStatus() )
            assertEquals( "before limit=" + limit3 + ",limit after=" + limit4 + ",usedAmt=" + usedAmt2, true, Math.abs( limit3 - limit4 - usedAmt2 ) < CREDIT_CALCULATION_MINIMUM )
            assertEquals( "Message Status=" + cwm2.getStatusName(), cwm2.getStatus().equals( MessageStatus.SUCCESS ), true )
            validateCreditUtilizationEvents( cwm2 )

            // do a trade to buy 1k AUD vs DKK
            double limit5 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "Credit limit before second bid trade take Credit : " + limit5 )
            Trade trade3 = prepareSingleLegTrade( tradeAmt, true, true, "AUD/DKK", lpOrg, lpTpForDd, bidRates[4], spotDate )
            CreditWorkflowMessage cwm3 = creditMgr.takeCredit( trade3, lpLe, ddOrg, lpTpForDd )
            double limit6 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )

            // in this case, there will be 3 receivable amounts EUR 1k, CHF 1k, and AUD 1k
            double usedAmt3 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "AUD", tradeAmt, true )
            log( "Credit limit after second bid trade take Credit : " + limit6 + " success : " + cwm3.getStatus() )
            assertEquals( "before limit=" + limit5 + ",limit after=" + limit6 + ",usedAmt=" + usedAmt3, true, Math.abs( limit5 - limit6 - usedAmt3 ) < CREDIT_CALCULATION_MINIMUM )
            assertEquals( "Message Status=" + cwm3.getStatusName(), cwm3.getStatus().equals( MessageStatus.SUCCESS ), true )
            validateCreditUtilizationEvents( cwm3 )
        }
        catch ( Exception e )
        {
            e.printStackTrace()
            fail()
        }
        finally
        {
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 )
        }
    }

    void testAggregateSettlementReceivableMultipleTradesWithDifferentCurrenciesSellBase()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, null, 100000 )
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_NET_SETTLEMENT_RECEIVABLE_CALCULATOR, 100000 )

            // first do a trade to sell 1k EUR vs GBP
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "Credit limit before first bid trade take Credit : " + limit1 )
            double tradeAmt = 1000
            Trade trade1 = prepareSingleLegTrade( tradeAmt, false, true, EURGBP, lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd )
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "Credit limit after first bid trade take Credit : " + limit2 + " success : " + cwm1.getStatus() )

            // in this case net receivable would be USD 1k
            double usedAmt1 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "GBP", getTradeAmount( trade1, "GBP" ), true )
            assertEquals( "before limit=" + limit1 + ",limit after=" + limit2 + ",usedAmt=" + usedAmt1, true, Math.abs( limit1 - limit2 - usedAmt1 ) < CREDIT_CALCULATION_MINIMUM )
            assertEquals( "Message Status=" + cwm1.getStatusName(), cwm1.getStatus().equals( MessageStatus.SUCCESS ), true )
            validateCreditUtilizationEvents( cwm1 )

            // do a trade to sell 1k CHF vs JPY
            double limit3 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "Credit limit before second bid trade take Credit : " + limit3 )
            Trade trade2 = prepareSingleLegTrade( tradeAmt, false, true, "CHF/JPY", lpOrg, lpTpForDd, bidRates[4], spotDate )
            CreditWorkflowMessage cwm2 = creditMgr.takeCredit( trade2, lpLe, ddOrg, lpTpForDd )
            double limit4 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )

            // in this case, there will be two net receivables GBP 1k and JPY 1k.
            double usedAmt2 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "JPY", getTradeAmount( trade2, "JPY" ), true )
            log( "Credit limit after second bid trade take Credit : " + limit4 + " success : " + cwm2.getStatus() )
            assertEquals( "before limit=" + limit3 + ",limit after=" + limit4 + ",usedAmt=" + usedAmt2, true, Math.abs( limit3 - limit4 - usedAmt2 ) < CREDIT_CALCULATION_MINIMUM )
            assertEquals( "Message Status=" + cwm2.getStatusName(), cwm2.getStatus().equals( MessageStatus.SUCCESS ), true )
            validateCreditUtilizationEvents( cwm2 )

            // do a trade to sell 1k AUD vs DKK
            double limit5 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "Credit limit before second bid trade take Credit : " + limit5 )
            Trade trade3 = prepareSingleLegTrade( tradeAmt, false, true, "AUD/DKK", lpOrg, lpTpForDd, bidRates[4], spotDate )
            CreditWorkflowMessage cwm3 = creditMgr.takeCredit( trade3, lpLe, ddOrg, lpTpForDd )
            double limit6 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )

            // in this case, there will be 3 receivable amounts GBP 1k, JPY 1k, and DKK 1k
            double usedAmt3 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "DKK", getTradeAmount( trade3, "DKK" ), true )
            log( "Credit limit after second bid trade take Credit : " + limit6 + " success : " + cwm3.getStatus() )
            assertEquals( "before limit=" + limit5 + ",limit after=" + limit6 + ",usedAmt=" + usedAmt3, true, Math.abs( limit5 - limit6 - usedAmt3 ) < CREDIT_CALCULATION_MINIMUM )
            assertEquals( "Message Status=" + cwm3.getStatusName(), cwm3.getStatus().equals( MessageStatus.SUCCESS ), true )
            validateCreditUtilizationEvents( cwm3 )
        }
        catch ( Exception e )
        {
            e.printStackTrace()
            fail()
        }
        finally
        {
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 )
        }
    }

    void testAggregateSettlementReceivableMultipleTradesCancelsOut()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, null, 100000 )
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_NET_SETTLEMENT_RECEIVABLE_CALCULATOR, 100000 )

            // first do a trade to buy 1k EUR vs GBP
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "Credit limit before first bid trade take Credit : " + limit1 )
            double tradeAmt = 1000
            Trade trade1 = prepareSingleLegTrade( tradeAmt, true, true, EURGBP, lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, ddOrg, lpTpForDd )
            double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "Credit limit after first bid trade take Credit : " + limit2 + " success : " + cwm1.getStatus() )

            // in this case net receivable would be EURO 1k
            double usedAmt1 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "EUR", tradeAmt, true )
            assertEquals( "before limit=" + limit1 + ",limit after=" + limit2 + ",usedAmt=" + usedAmt1, true, Math.abs( limit1 - limit2 - usedAmt1 ) < CREDIT_CALCULATION_MINIMUM )
            assertEquals( "Message Status=" + cwm1.getStatusName(), cwm1.getStatus().equals( MessageStatus.SUCCESS ), true )
            validateCreditUtilizationEvents( cwm1 )

            // do a trade to buy 1k CHF vs JPY
            double limit3 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "Credit limit before second bid trade take Credit : " + limit3 )
            Trade trade2 = prepareSingleLegTrade( tradeAmt, true, true, "CHF/JPY", lpOrg, lpTpForDd, bidRates[4], spotDate )
            CreditWorkflowMessage cwm2 = creditMgr.takeCredit( trade2, lpLe, ddOrg, lpTpForDd )
            double limit4 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )

            // in this case, there will be two net receivables EUR 1k and CHF 1k.
            double usedAmt2 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "CHF", tradeAmt, true )
            log( "Credit limit after second bid trade take Credit : " + limit4 + " success : " + cwm2.getStatus() )
            assertEquals( "before limit=" + limit3 + ",limit after=" + limit4 + ",usedAmt=" + usedAmt2, true, Math.abs( limit3 - limit4 - usedAmt2 ) < CREDIT_CALCULATION_MINIMUM )
            assertEquals( "Message Status=" + cwm2.getStatusName(), cwm2.getStatus().equals( MessageStatus.SUCCESS ), true )
            validateCreditUtilizationEvents( cwm2 )

            // do a trade to buy 1k AUD/DKK
            double limit5 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "Credit limit before third bid trade take Credit : " + limit5 )
            Trade trade3 = prepareSingleLegTrade( tradeAmt, true, true, "AUD/DKK", lpOrg, lpTpForDd, bidRates[4], spotDate )
            CreditWorkflowMessage cwm3 = creditMgr.takeCredit( trade3, lpLe, ddOrg, lpTpForDd )
            double limit6 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )

            // in this case, there will be 3 receivable amounts EUR 1k, CHF 1k, and AUD 1k
            double usedAmt3 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "AUD", tradeAmt, true )
            log( "Credit limit after third bid trade take Credit : " + limit6 + " success : " + cwm3.getStatus() )
            assertEquals( "before limit=" + limit5 + ",limit after=" + limit6 + ",usedAmt=" + usedAmt3, true, Math.abs( limit5 - limit6 - usedAmt3 ) < CREDIT_CALCULATION_MINIMUM )
            assertEquals( "Message Status=" + cwm3.getStatusName(), cwm3.getStatus().equals( MessageStatus.SUCCESS ), true )
            validateCreditUtilizationEvents( cwm3 )

            // first do a trade to sell 1k EUR vs GBP
            double limit7 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "Credit limit before first bid trade take Credit : " + limit7 )
            Trade trade4 = prepareSingleLegTrade( tradeAmt, false, true, EURGBP, lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowMessage cwm4 = creditMgr.takeCredit( trade4, lpLe, ddOrg, lpTpForDd )
            double limit8 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "Credit limit after first bid trade take Credit : " + limit8 + " success : " + cwm4.getStatus() )

            // in this case net receivable would be EURO 1k
            double usedAmt4 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "EUR", tradeAmt, true )
            assertEquals( "before limit=" + limit7 + ",limit after=" + limit8 + ",usedAmt=" + usedAmt4, true, Math.abs( limit8 - limit7 - usedAmt4 ) < CREDIT_CALCULATION_MINIMUM )
            assertEquals( "Message Status=" + cwm4.getStatusName(), cwm4.getStatus().equals( MessageStatus.SUCCESS ), true )
            validateCreditUtilizationEvents( cwm4 )

            // do a trade to sell 1k CHF vs JPY
            double limit9 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "Credit limit before second bid trade take Credit : " + limit9 )
            Trade trade5 = prepareSingleLegTrade( tradeAmt, false, true, "CHF/JPY", lpOrg, lpTpForDd, bidRates[4], spotDate )
            CreditWorkflowMessage cwm5 = creditMgr.takeCredit( trade5, lpLe, ddOrg, lpTpForDd )
            double limit10 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )

            // in this case, there will be two net receivables EUR 1k and CHF 1k.
            double usedAmt5 = getLimitCurrencyAmount( lpOrg, lpTpForDd, "CHF", tradeAmt, true )
            log( "Credit limit after second bid trade take Credit : " + limit10 + " success : " + cwm5.getStatus() )
            assertEquals( "before limit=" + limit9 + ",limit after=" + limit10 + ",usedAmt=" + usedAmt5, true, Math.abs( limit10 - limit9 - usedAmt5 ) < CREDIT_CALCULATION_MINIMUM )
            assertEquals( "Message Status=" + cwm5.getStatusName(), cwm5.getStatus().equals( MessageStatus.SUCCESS ), true )
            validateCreditUtilizationEvents( cwm5 )

            // do a trade to sell 1k AUD vs DKK
            double limit11 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "Credit limit before second bid trade take Credit : " + limit11 )
            Trade trade6 = prepareSingleLegTrade( tradeAmt, false, true, "AUD/DKK", lpOrg, lpTpForDd, bidRates[4], spotDate )
            CreditWorkflowMessage cwm6 = creditMgr.takeCredit( trade6, lpLe, ddOrg, lpTpForDd )
            double limit12 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION )

            // in this case, all the net receivables got cancelled due to opposite direction trades.
            log( "Credit limit after second bid trade take Credit : " + limit12 + " success : " + cwm6.getStatus() )
            assertEquals( "before limit=" + limit11 + ",limit after=" + limit12, true, Math.abs( limit12 - limit1 ) < CREDIT_CALCULATION_MINIMUM )
            assertEquals( "Message Status=" + cwm6.getStatusName(), cwm6.getStatus().equals( MessageStatus.SUCCESS ), true )
            validateCreditUtilizationEvents( cwm6 )
        }
        catch ( Exception e )
        {
            e.printStackTrace()
            fail()
        }
        finally
        {
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 )
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 10000 )
        }
    }
}
