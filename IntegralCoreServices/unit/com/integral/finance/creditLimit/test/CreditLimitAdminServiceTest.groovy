package com.integral.finance.creditLimit.test

// Copyright (c) 2014 Integral Development Corporation.  All Rights Reserved.

import com.integral.businessCenter.BusinessCenter
import com.integral.businessCenter.BusinessCenterFactory
import com.integral.finance.businessCenter.EndOfDayServiceFactory
import com.integral.finance.counterparty.CounterpartyFactory
import com.integral.finance.counterparty.CounterpartyUtilC
import com.integral.finance.counterparty.LegalEntity
import com.integral.finance.counterparty.TradingParty
import com.integral.finance.creditLimit.*
import com.integral.finance.creditLimit.configuration.CreditConfigurationFactory
import com.integral.finance.currency.Currency
import com.integral.finance.currency.CurrencyFactory
import com.integral.finance.currency.CurrencyPairGroup
import com.integral.finance.currency.MockCurrencyPairGroup
import com.integral.finance.instrument.AmountOfInstrument
import com.integral.finance.marketData.MarketDataSet
import com.integral.finance.trade.Tenor
import com.integral.finance.trade.Trade
import com.integral.message.MessageStatus
import com.integral.persistence.Entity
import com.integral.persistence.EntityFactory
import com.integral.persistence.Namespace
import com.integral.time.DatePeriod
import com.integral.time.DateTimeFactory
import com.integral.time.IdcDate
import com.integral.user.Organization
import com.integral.user.UserFactory

/**
 * Tests the credit limit admin service API
 *
 * <AUTHOR> Development Corp.
 */
public class CreditLimitAdminServiceTest extends CreditLimitServiceBaseTestCase
{
    public void testChangeCreditExposureWithEvents()
    {
        try
        {
            init( fiUser );

            // now do a trade.
            setCalcAndLimit( fiOrg, fiTpForCpty, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_LIMIT_CALCULATOR, 10000 );
            double limit0 = getAvailableCreditLimit( fiTpForCpty, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double tradeAmt = 1000;
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, fiOrg, fiTpForCpty, bidRates[0], spotDate );
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, fiLe, cptyOrg, fiTpForCpty );
            double limit1 = getAvailableCreditLimit( fiTpForCpty, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit after first bid trade take Credit : " + limit1 + " success : " + cwm.getStatus() + ",events=" + cwm.getCreditUtilizationEvents() );
            assertEquals( "before limit=" + limit0 + ",limitAfter=" + limit1, true, Math.abs( limit0 - limit1 - tradeAmt ) < MINIMUM );
            validateCreditUtilizationEvents( cwm );
            CreditUtilizationEvent cue = ( CreditUtilizationEvent ) cwm.getCreditUtilizationEvents().toArray()[0];
            log( "CreditUtilizationEvent details. object id=" + cue.getObjectID() + ",cu object id=" + cue.getCreditUtilization().getObjectID() + ",cu=" + cue.getCreditUtilization() );

            // execute various credit workflows here.
            executeCreditWorkflows( fiOrg, fiLe, cptyOrg, fiTpForCpty );
            boolean isOrgLevel = CreditUtilC.isOrgLevelCredit( fiOrg, cptyOrg );
            if ( !isOrgLevel )
            {
                log( "setting org level credit exposure. org=" + fiOrg );
                testSetOrgCreditExposure();
            }
            else
            {
                log( "setting le level credit exposure. org=" + fiOrg );
                testSetLegalEntityCreditExposure();
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            //todo fail();
        }
    }


    public void testOrgCreditNettingMethodologyChangeWithEvents()
    {
        try
        {
            init( fiUser );
            removeExistingCreditUtilizationEvents( fiOrg );
            boolean isOrgLevel = CreditUtilC.isOrgLevelCredit( fiOrg, cptyOrg );
            if ( !isOrgLevel )
            {
                testSetOrgCreditExposure();
            }

            // now do a trade.
            setCalcAndLimit( fiOrg, fiTpForCpty, GROSS_NOTIONAL_CLASSIFICATION, NET_OPEN_POSITION_CALCULATOR, 1000000 );
            setCalcAndLimit( fiOrg, fiTpForCpty, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 100000 );
            double limit0 = getAvailableCreditLimit( fiTpForCpty, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            double tradeAmt = 1000;
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, fiOrg, fiTpForCpty, bidRates[0], spotDate );
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, fiLe, cptyOrg, fiTpForCpty );
            double limit1 = getAvailableCreditLimit( fiTpForCpty, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit after first bid trade take Credit : " + limit1 + " success : " + cwm.getStatus() + ",events=" + cwm.getCreditUtilizationEvents() );
            assertEquals( "before limit=" + limit0 + ",limitAfter=" + limit1, true, Math.abs( limit0 - limit1 - tradeAmt ) < 1 );
            validateCreditUtilizationEvents( cwm );
            CreditUtilizationEvent cue = ( CreditUtilizationEvent ) cwm.getCreditUtilizationEvents().toArray()[0];
            log( "CreditUtilizationEvent details. object id=" + cue.getObjectID() + ",cu object id=" + cue.getCreditUtilization().getObjectID() + ",cu=" + cue.getCreditUtilization() );

            // execute various credit workflows here.
            executeCreditWorkflows( fiOrg, fiLe, cptyOrg, fiTpForCpty );

            // sleep for 5 seconds to get all used amounts saved.
            sleepFor( 5000 );

            // now do all the netting methodology changes.
            creditAdminSvc.setNettingMethodology( fiOrg, fiTpForCpty, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR );
            creditAdminSvc.setNettingMethodology( fiOrg, fiTpForCpty, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_LIMIT_CALCULATOR );

            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( fiOrg, fiTpForCpty );
            Iterator iter = cclr.getChildrenRules().iterator();
            while ( iter.hasNext() )
            {
                CreditLimitRule clr = ( CreditLimitRule ) iter.next();
                if ( clr instanceof SingleCreditLimitRule )
                {
                    assertEquals( "calc name=" + clr.getCreditUtilizationCalculator().getShortName(), clr.getCreditUtilizationCalculator(), AGGREGATE_LIMIT_CALCULATOR );
                }
                else if ( clr instanceof DailyCreditLimitRule )
                {
                    assertEquals( "calc name=" + clr.getCreditUtilizationCalculator().getShortName(), clr.getCreditUtilizationCalculator(), GROSS_DAILY_LIMIT_CALCULATOR );
                }
            }

            // now change the single type to NOP and daily type to Daily settlement.
            creditAdminSvc.setNettingMethodology( fiOrg, fiTpForCpty, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR );
            creditAdminSvc.setNettingMethodology( fiOrg, fiTpForCpty, GROSS_NOTIONAL_CLASSIFICATION, NET_OPEN_POSITION_CALCULATOR );

            CounterpartyCreditLimitRule cclr1 = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( fiOrg, fiTpForCpty );
            Iterator iter1 = cclr1.getChildrenRules().iterator();
            while ( iter1.hasNext() )
            {
                CreditLimitRule clr = ( CreditLimitRule ) iter1.next();
                if ( clr instanceof SingleCreditLimitRule )
                {
                    assertEquals( "calc name=" + clr.getCreditUtilizationCalculator().getShortName(), clr.getCreditUtilizationCalculator(), NET_OPEN_POSITION_CALCULATOR );
                }
                else if ( clr instanceof DailyCreditLimitRule )
                {
                    assertEquals( "calc name=" + clr.getCreditUtilizationCalculator().getShortName(), clr.getCreditUtilizationCalculator(), DAILY_SETTLEMENT_LIMIT_CALCULATOR );
                }
            }


            assertEquals( "checkSanity", sanityCheck( fiOrg ), true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            //todo fail();
        }
    }

    public void testCreditExposure()
    {
        try
        {
            init( lpUser );
            boolean isOrgLevel = CreditUtilC.isOrgLevelCredit( lpOrg, ddOrg );
            if ( !isOrgLevel )
            {
                CounterpartyCreditLimitRule activeCclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTpForDd );
                assertEquals( "Active rule should not be null=" + activeCclr, activeCclr != null, true );
                assertEquals( "Active rule status should be org level=" + activeCclr.isOrgLevel(), activeCclr.isOrgLevel(), false );
                assertEquals( "Active rule status should have a tporg=" + activeCclr.getTradingPartyOrganization(), activeCclr.getTradingPartyOrganization() != null, true );

                CounterpartyCreditLimitRule orgCclr = CreditUtilC.getOrgLevelCounterpartyCreditLimitRule( lpOrg, lpTpForDd );
                assertEquals( "orgCclr should have null trading party=" + orgCclr.getTradingParty(), orgCclr.getTradingParty() == null, true );
                assertEquals( "orgCclr should be inactive=" + orgCclr.isActive(), orgCclr.isActive(), false );

                CounterpartyCreditLimitRule tpCclr = CreditUtilC.getTradingPartyLevelCounterpartyCreditLimitRule( lpOrg, lpTpForDd );
                assertEquals( "cptyRule should have a tp=" + tpCclr.getTradingParty(), tpCclr.getTradingParty() != null, true );
                assertEquals( "cptyRule should be active=" + tpCclr.isActive(), tpCclr.isActive(), true );
                assertEquals( "cptyRule status should be tp level=" + tpCclr.isOrgLevel(), tpCclr.isOrgLevel(), false );
            }
            else
            {
                CounterpartyCreditLimitRule activeCclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTpForDd );
                assertEquals( "Active rule should not be null=" + activeCclr, activeCclr != null, true );
                assertEquals( "Active rule status should be org level=" + activeCclr.isOrgLevel(), activeCclr.isOrgLevel(), true );
                assertEquals( "Active rule status should have a tporg=" + activeCclr.getTradingPartyOrganization(), activeCclr.getTradingPartyOrganization() != null, true );

                CounterpartyCreditLimitRule orgCclr = CreditUtilC.getOrgLevelCounterpartyCreditLimitRule( lpOrg, lpTpForDd );
                assertEquals( "orgCclr should have null trading party=" + orgCclr.getTradingParty(), orgCclr.getTradingParty() == null, true );
                assertEquals( "orgCclr should be active=" + orgCclr.isActive(), orgCclr.isActive(), true );

                CounterpartyCreditLimitRule tpCclr = CreditUtilC.getTradingPartyLevelCounterpartyCreditLimitRule( lpOrg, lpTpForDd );
                assertEquals( "cptyRule should have a tp=" + tpCclr.getTradingParty(), tpCclr.getTradingParty() != null, true );
                assertEquals( "cptyRule should be inactive=" + tpCclr.isActive(), tpCclr.isActive(), false );
                assertEquals( "cptyRule status should be org level=" + tpCclr.isOrgLevel(), tpCclr.isOrgLevel(), true );
            }
            assertEquals( "checkSanity", sanityCheck( lpOrg ), true );
        }
        catch ( Exception e )
        {
            log.error( "testCreditExposure", e );
            fail();
        }
    }


    public void testSetOrgCreditExposure()
    {
        try
        {
            init( lpUser );
            creditAdminSvc.setOrganizationExposureLevel( lpOrg, ddOrg );
            boolean isOrgLevel = CreditUtilC.isOrgLevelCredit( lpOrg, ddOrg );
            assertEquals( "isOrglevel : " + isOrgLevel, isOrgLevel, true );
            assertEquals( "checkSanity", sanityCheck( lpOrg ), true );
        }
        catch ( Exception e )
        {
            log.error( "testSetOrgCreditExposure", e );
            fail();
        }
    }

    public void testSetLegalEntityCreditExposure()
    {
        try
        {
            init( lpUser );
            creditAdminSvc.setLegalEntityExposureLevel( lpOrg, ddOrg );
            boolean isOrgLevel = CreditUtilC.isOrgLevelCredit( lpOrg, ddOrg );
            assertEquals( "isOrglevel : " + isOrgLevel, isOrgLevel, false );
            assertEquals( "checkSanity", sanityCheck( lpOrg ), true );
        }
        catch ( Exception e )
        {
            log.error( "testSetLegalEntityCreditExposure", e );
            fail();
        }
    }

    public void testNettingCalculatorChanges()
    {
        try
        {
            init( lpUser );
            creditAdminSvc.setNettingMethodology( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR );
            creditAdminSvc.setNettingMethodology( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_LIMIT_CALCULATOR );

            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTpForDd );
            Iterator iter = cclr.getChildrenRules().iterator();
            while ( iter.hasNext() )
            {
                CreditLimitRule clr = ( CreditLimitRule ) iter.next();
                if ( clr instanceof SingleCreditLimitRule )
                {
                    assertEquals( "calc name=" + clr.getCreditUtilizationCalculator().getShortName(), clr.getCreditUtilizationCalculator(), AGGREGATE_LIMIT_CALCULATOR );
                }
                else if ( clr instanceof DailyCreditLimitRule )
                {
                    assertEquals( "calc name=" + clr.getCreditUtilizationCalculator().getShortName(), clr.getCreditUtilizationCalculator(), GROSS_DAILY_LIMIT_CALCULATOR );
                }
            }

            // now change the single type to NOP and daily type to Daily settlement.
            creditAdminSvc.setNettingMethodology( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR );
            creditAdminSvc.setNettingMethodology( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, NET_OPEN_POSITION_CALCULATOR );

            CounterpartyCreditLimitRule cclr1 = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTpForDd );
            Iterator iter1 = cclr1.getChildrenRules().iterator();
            while ( iter1.hasNext() )
            {
                CreditLimitRule clr = ( CreditLimitRule ) iter1.next();
                if ( clr instanceof SingleCreditLimitRule )
                {
                    assertEquals( "calc name=" + clr.getCreditUtilizationCalculator().getShortName(), clr.getCreditUtilizationCalculator(), NET_OPEN_POSITION_CALCULATOR );
                }
                else if ( clr instanceof DailyCreditLimitRule )
                {
                    assertEquals( "calc name=" + clr.getCreditUtilizationCalculator().getShortName(), clr.getCreditUtilizationCalculator(), DAILY_SETTLEMENT_LIMIT_CALCULATOR );
                }
            }

            assertEquals( "checkSanity", sanityCheck( lpOrg ), true );
        }
        catch ( Exception e )
        {
            log.error( "testNettingCalculatorChanges", e );
            fail();
        }
    }

    public void testNettingMethodologyChangeWithEvents()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );

            Currency ccy = CreditUtilC.getLimitCurrency( lpOrg );
            if ( !ccy.isSameAs( CurrencyFactory.getCurrency( "USD" ) ) )
            {
                return;
            }

            // do a trade
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, NET_OPEN_POSITION_CALCULATOR, 10000 );
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 10000 );
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            creditAdminSvc.setNettingMethodology( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR );
            creditAdminSvc.setNettingMethodology( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR );
            double tradeAmt = 1000;
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd );
            sleepFor( 4000 );
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, GROSS_NOTIONAL_CLASSIFICATION );
            log( "Credit limit after first bid trade take Credit : " + limit1 + " success : " + cwm.getStatus() + ",events=" + cwm.getCreditUtilizationEvents() );
            assertEquals( "before limit=" + limit0 + ",limitAfter=" + limit1, true, Math.abs( limit0 - limit1 - tradeAmt ) < MINIMUM );
            validateCreditUtilizationEvents( cwm );
            CreditUtilizationEvent cue = ( CreditUtilizationEvent ) cwm.getCreditUtilizationEvents().toArray()[0];
            log( "CreditUtilizationEvent details. object id=" + cue.getObjectID() + ",cu object id=" + cue.getCreditUtilization().getObjectID() + ",cu=" + cue.getCreditUtilization() );

            creditAdminSvc.setNettingMethodology( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_LIMIT_CALCULATOR );
            creditAdminSvc.setNettingMethodology( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR );

            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTpForDd );
            log( "cclr children rule size=" + cclr.getChildrenRules().size() );
            Iterator iter = cclr.getChildrenRules().iterator();
            while ( iter.hasNext() )
            {
                CreditLimitRule clr = ( CreditLimitRule ) iter.next();
                log( "clr=" + clr + "clsf=" + clr.getClassification() + ",calc=" + clr.getCreditUtilizationCalculator() );
                if ( clr instanceof DailyCreditLimitRule )
                {
                    assertEquals( "calc name=" + clr.getCreditUtilizationCalculator().getShortName(), clr.getCreditUtilizationCalculator().getShortName(), DAILY_SETTLEMENT_LIMIT_CALCULATOR.getShortName() );
                }
                else if ( clr instanceof SingleCreditLimitRule )
                {
                    assertEquals( "calc name=" + clr.getCreditUtilizationCalculator().getShortName(), clr.getCreditUtilizationCalculator().getShortName(), AGGREGATE_LIMIT_CALCULATOR.getShortName() );
                }
            }

            // inspect the credit utilization event again.
            log( "After netting methodology changes. CreditUtilizationEvent details. object id=" + cue.getObjectID() + ",cu object id=" + cue.getCreditUtilization().getObjectID() + ",cu=" + cue.getCreditUtilization() );
            log( "After refreshing. CreditUtilizationEvent details. object id=" + cue.getObjectID() + ",cu object id=" + cue.getCreditUtilization().getObjectID() + ",cu=" + cue.getCreditUtilization() );

            //todo assertEquals( "checkSanity", sanityCheck( lpOrg ), true );
        }
        catch ( Exception e )
        {
            log.error( "testNettingMethodologyChangeWithEvents", e );
            fail();
        }
    }

    public void testCreditEnableOrgLevel()
    {
        try
        {
            init( lpUser );
            boolean enable = CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().isCreditEnabled( lpOrg, lpTpForDd );
            log( "Credit enabled for org=" + lpOrg + ",enabled=" + enable );
            if ( enable )
            {
                double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate );
                AmountOfInstrument aoi = CreditUtilizationManagerC.getInstance().getAvailableLimitAmountOfInstrument( lpLe, lpTpForDd, ddOrg, spotDate );
                assertEquals( "aoi should not be null. aoi=" + aoi, aoi != null, true );

                Double availableLimit = CreditUtilizationManagerC.getInstance().getAvailableCreditLimit( lpLe, lpTpForDd, ddOrg, spotDate );
                assertEquals( "availableLimit should not be null. availableLimit=" + aoi, availableLimit != null, true );

                // now disable the credit for provider.
                creditAdminSvc.setCreditEnabled( lpOrg, false );

                double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate );
                double tradeAmt = 1000;
                Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
                CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd );
                double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate );
                log( "Credit limit after first bid trade take Credit : " + limit2 + " success : " + cwm.getStatus() + ",events=" + cwm.getCreditUtilizationEvents() );

                boolean enable1 = CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().isCreditEnabled( lpOrg, lpTpForDd );

                // put it back to enabled for other test cases.
                creditAdminSvc.setCreditEnabled( lpOrg, true );

                assertEquals( "before limit=" + limit0 + ",limitAfter=" + limit2, true, Math.abs( limit2 - limit1 ) < MINIMUM );
                assertEquals( "Credit should be success. status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS );
                assertEquals( "Should be disabled. enable=" + enable1, enable1, false );

            }
            else
            {
                double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate );
                AmountOfInstrument aoi = CreditUtilizationManagerC.getInstance().getAvailableLimitAmountOfInstrument( lpLe, lpTpForDd, ddOrg, spotDate );
                assertEquals( "aoi should be null. aoi=" + aoi, aoi == null, true );

                Double availableLimit = CreditUtilizationManagerC.getInstance().getAvailableCreditLimit( lpLe, lpTpForDd, ddOrg, spotDate );
                assertEquals( "availableLimit should be null. availableLimit=" + aoi, availableLimit == null, true );

                // now enable the credit for provider.
                creditAdminSvc.setCreditEnabled( lpOrg, true );

                double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate );
                double tradeAmt = 1000;
                Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
                CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd );
                double limit2 = getAvailableCreditLimit( lpTpForDd, spotDate );
                log( "Credit limit after first bid trade take Credit : " + limit2 + " success : " + cwm.getStatus() + ",events=" + cwm.getCreditUtilizationEvents() );
                assertEquals( "before limit=" + limit0 + ",limitAfter=" + limit2, true, Math.abs( limit1 - limit2 - tradeAmt ) < MINIMUM );
                assertEquals( "Credit should be success. status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS );

                boolean enable1 = CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().isCreditEnabled( lpOrg, lpTpForDd );
                assertEquals( "Should be enabled. enable=" + enable1, enable1, true );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testCreditEnableTradingPartyLevel()
    {
        try
        {
            init( fiUser );
            boolean enable = CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().isCreditEnabled( fiOrg, fiTpForCpty );
            log( "Credit enabled for org=" + fiOrg + ",enabled=" + enable );
            if ( enable )
            {
                double limit0 = getAvailableCreditLimit( fiTpForCpty, spotDate );
                AmountOfInstrument aoi = CreditUtilizationManagerC.getInstance().getAvailableLimitAmountOfInstrument( fiLe, fiTpForCpty, cptyOrg, spotDate );
                assertEquals( "aoi should not be null. aoi=" + aoi, aoi != null, true );

                Double availableLimit = CreditUtilizationManagerC.getInstance().getAvailableCreditLimit( fiLe, fiTpForCpty, cptyOrg, spotDate );
                assertEquals( "availableLimit should not be null. availableLimit=" + aoi, availableLimit != null, true );

                // now disable the credit for provider.
                creditAdminSvc.setCreditEnabled( fiOrg, fiTpForCpty, false );

                boolean isCclrEnabled = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( fiOrg, fiTpForCpty ).isEnabled();
                log( "cclr enabled=" + isCclrEnabled );
                assertEquals( "cpty limit rule should be disabled=" + isCclrEnabled, isCclrEnabled, false );

                boolean isEnabled = CreditUtilizationManagerC.getInstance().isCreditEnabled( fiOrg, fiTpForCpty );
                log( "enable cache value=" + isEnabled );
                assertEquals( "credit should be disabled=" + isEnabled, isEnabled, false );

                double limit1 = getAvailableCreditLimit( fiTpForCpty, spotDate );
                double tradeAmt = 1000;
                Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, fiOrg, fiTpForCpty, bidRates[0], spotDate );
                CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, fiLe, cptyOrg, fiTpForCpty );
                double limit2 = getAvailableCreditLimit( fiTpForCpty, spotDate );
                log( "Credit limit after first bid trade take Credit : " + limit2 + " success : " + cwm.getStatus() + ",events=" + cwm.getCreditUtilizationEvents() );
                boolean enable1 = CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().isCreditEnabled( fiOrg, fiTpForCpty );

                // put it back to enabled for other test cases.
                creditAdminSvc.setCreditEnabled( fiOrg, fiTpForCpty, true );

                assertEquals( "before limit=" + limit0 + ",limitAfter=" + limit2, true, Math.abs( limit2 - limit1 ) < MINIMUM );
                assertEquals( "Credit should be success. status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS );

                assertEquals( "Should be disabled. enable=" + enable1, enable1, false );

            }
            else
            {
                double limit0 = getAvailableCreditLimit( fiTpForCpty, spotDate );
                AmountOfInstrument aoi = CreditUtilizationManagerC.getInstance().getAvailableLimitAmountOfInstrument( fiLe, fiTpForCpty, cptyOrg, spotDate );
                assertEquals( "aoi should be null. aoi=" + aoi, aoi == null, true );

                Double availableLimit = CreditUtilizationManagerC.getInstance().getAvailableCreditLimit( fiLe, fiTpForCpty, cptyOrg, spotDate );
                assertEquals( "availableLimit should be null. availableLimit=" + aoi, availableLimit == null, true );

                // now enable the credit for provider.
                creditAdminSvc.setCreditEnabled( fiOrg, fiTpForCpty, true );
                boolean isEnabled = CreditUtilizationManagerC.getInstance().isCreditEnabled( fiOrg, fiTpForCpty );
                assertEquals( "credit should be disabled=" + isEnabled, isEnabled, true );

                double limit1 = getAvailableCreditLimit( fiTpForCpty, spotDate );
                double tradeAmt = 1000;
                Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, fiOrg, fiTpForCpty, bidRates[0], spotDate );
                CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, fiLe, cptyOrg, fiTpForCpty );
                double limit2 = getAvailableCreditLimit( fiTpForCpty, spotDate );
                log( "Credit limit after first bid trade take Credit : " + limit2 + " success : " + cwm.getStatus() + ",events=" + cwm.getCreditUtilizationEvents() );
                assertEquals( "before limit=" + limit0 + ",limitAfter=" + limit2, true, Math.abs( limit1 - limit2 - tradeAmt ) < MINIMUM );
                assertEquals( "Credit should be success. status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS );

                boolean enable1 = CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().isCreditEnabled( fiOrg, fiTpForCpty );
                assertEquals( "Should be enabled. enable=" + enable1, enable1, true );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testSetupCreditProvider()
    {
        try
        {
            init( ddUser );

            if ( CreditUtilC.getCreditLimitRuleSet( ddOrg ) != null )
            {
                return;
            }

            creditAdminSvc.setupCreditProvider( ddOrg, staticMds );

            // check whether we have a valid credit limit org function and credit limit ruleset.
            CreditLimitOrgFunction orgFunc = CreditUtilC.getCreditLimitOrgFunction( ddOrg );
            log( "orgFunc=" + orgFunc );
            assertEquals( "credit limit org funciton=" + orgFunc, orgFunc != null, true );
            log( "supported credit types=" + orgFunc.getSupportedCreditLimitClassifications() );
            assertEquals( "org function supported credit types=" + orgFunc.getSupportedCreditLimitClassifications(), orgFunc.getSupportedCreditLimitClassifications().isEmpty(), false );
            log( "org func credit utilization period=" + orgFunc.getCreditUtilizationPeriod() );
            assertEquals( "org function credit utilization period=" + orgFunc.getCreditUtilizationPeriod(), orgFunc.getCreditUtilizationPeriod() != null, true );
            CreditLimitRuleSet clrs = orgFunc.getCreditLimitRuleSet();
            log( "clrs=" + clrs );
            assertEquals( "credit limit rule set=" + clrs, clrs != null, true );
            assertEquals( "credit limit ruleset is indexed=" + clrs.isRulesIndexed(), clrs.isRulesIndexed(), true );
            if ( CreditConfigurationFactory.getCreditConfigurationMBean().isCreditProviderEnabled() )
            {
                assertEquals( "credit limit ruleset is enabled=" + clrs.isEnabled(), clrs.isEnabled(), true );
            }
            else
            {
                assertEquals( "credit limit ruleset is enabled=" + clrs.isEnabled(), clrs.isEnabled(), false );
            }
            assertEquals( "namespace should match. org ns=" + ddOrg.getNamespace() + ",clrs.ns=" + clrs.getNamespace(), ddOrg.getNamespace().isSameAs( clrs.getNamespace() ), true );
            log( "ns=" + ddOrg.getNamespace() + ",clrs.mds=" + clrs.getMarketDataSet() + ",orgFunc.mds=" + orgFunc.getMarketDataSet() );
            MarketDataSet mds = clrs.getMarketDataSet();
            assertEquals( "market data set=" + mds, mds != null, true );
            assertEquals( "org func has same mds=" + orgFunc.getMarketDataSet(), orgFunc.getMarketDataSet() != null, true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testSetupCreditProviderExternalTx()
    {
        try
        {
            init( fiUser );
            Organization org = UserFactory.newOrganization();
            org.setShortName( "NewOrg" + System.currentTimeMillis() );
            org.setLongName( "LongName" );
            Namespace ns = EntityFactory.newNamespace();
            ns.setShortName( "NewNs" + System.currentTimeMillis() );
            ns.setDescription( "FI" );
            org.setNamespace( ns );

            // create and set the new legal entity.
            LegalEntity le = CounterpartyFactory.newLegalEntity();
            le.setShortName( "NewLEe" + System.currentTimeMillis() );
            le.setNamespace( ns );
            le.setOrganization( org );
            org.getLegalEntities().add( le );
            org.putCustomField( DEFAULT_DEALING_ENTITY_CUSTOM_FIELD_NAME, le );

            // set the business center reporting currency.
            BusinessCenter bs = BusinessCenterFactory.newBusinessCenter();
            bs.setShortName( le.getShortName() );
            bs.setReportingCurrency( CurrencyFactory.getCurrency( "USD" ) );
            le.setBusinessCenter( bs );

            creditAdminSvc.setupCreditProvider( org, staticMds );

            // check whether we have a valid credit limit org function and credit limit ruleset.
            CreditLimitOrgFunction orgFunc = CreditUtilC.getCreditLimitOrgFunction( org );
            log( "orgFunc=" + orgFunc );
            assertEquals( "credit limit org funciton=" + orgFunc, orgFunc != null, true );
            log( "supported credit types=" + orgFunc.getSupportedCreditLimitClassifications() );
            assertEquals( "org function supported credit types=" + orgFunc.getSupportedCreditLimitClassifications(), orgFunc.getSupportedCreditLimitClassifications().isEmpty(), false );
            assertEquals( "org function should have limit currency=" + orgFunc.getCreditLimitCurrency(), orgFunc.getCreditLimitCurrency() != null, true );
            log( "org func credit utilization period=" + orgFunc.getCreditUtilizationPeriod() );
            assertEquals( "org function credit utilization period=" + orgFunc.getCreditUtilizationPeriod(), orgFunc.getCreditUtilizationPeriod() != null, true );
            CreditLimitRuleSet clrs = orgFunc.getCreditLimitRuleSet();
            log( "clrs=" + clrs );
            assertEquals( "credit limit rule set=" + clrs, clrs != null, true );
            assertEquals( "credit limit ruleset is indexed=" + clrs.isRulesIndexed(), clrs.isRulesIndexed(), true );
            assertEquals( "credit limit ruleset is matchfirst=" + clrs.isMatchFirst(), clrs.isMatchFirst(), true );
            if ( CreditConfigurationFactory.getCreditConfigurationMBean().isCreditProviderEnabled() )
            {
                assertEquals( "credit limit ruleset is enabled=" + clrs.isEnabled(), clrs.isEnabled(), true );
            }
            else
            {
                assertEquals( "credit limit ruleset is enabled=" + clrs.isEnabled(), clrs.isEnabled(), false );
            }
            assertEquals( "namespace should match. org ns=" + ns + ",clrs.ns=" + clrs.getNamespace(), ns.isSameAs( clrs.getNamespace() ), true );
            log( "ns=" + ns + ",clrs.mds=" + clrs.getMarketDataSet() + ",orgFunc.mds=" + orgFunc.getMarketDataSet() );
            MarketDataSet mds = clrs.getMarketDataSet();
            assertEquals( "market data set=" + mds, mds != null, true );
            assertEquals( "org func has same mds=" + orgFunc.getMarketDataSet(), orgFunc.getMarketDataSet() != null, true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testSetCreditRelationshipAtOrgLevel()
    {
        try
        {
            init( lpUser );

            // tweak the credit limit org function to set at org level.
            CreditLimitOrgFunction orgFunc = CreditUtilC.getCreditLimitOrgFunction( lpOrg );
            boolean exposureLevel = orgFunc.isOrgLevelCreditExposure();
            orgFunc.setOrgLevelCreditExposure( true );
            creditAdminSvc.establishCreditRelationship( lpOrg, lpTpForFi );
            orgFunc.setOrgLevelCreditExposure( exposureLevel );
            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTpForFi );
            assertEquals( "cclr is not null", cclr != null, true );
            if ( cclr != null )
            {
                log( "cclr=" + cclr + ",cclr.enabled=" + cclr.isEnabled() + ",status=" + cclr.getStatus() );
                assertEquals( "cclr active ", cclr.isActive(), true );
                log( "children rules=" + cclr.getChildrenRules() );
                assertEquals( "cclr children rules not empty", cclr.getChildrenRules().isEmpty(), false );

                // check the spot date credit utilization.
                Double limit = CreditUtilizationManagerC.getInstance().getAvailableCreditLimit( lpLe, lpTpForFi, fiOrg, spotDate );
                log( "spot date limit=" + limit );
                assertEquals( "limit is not null. ", limit != null, true );

                // now remove the trading relationship.
                boolean isOrgLevel = CreditUtilC.isOrgLevelCredit( lpOrg, fiOrg );
                log( "exposure level at org=" + isOrgLevel );
                creditAdminSvc.removeCreditRelationship( lpOrg, lpTpForFi );
                CounterpartyCreditLimitRule cclr1 = CreditUtilC.getOrgLevelCounterpartyCreditLimitRule( lpOrg, lpTpForFi );
                log( "cclr1=" + cclr1 + ",cclr1.enabled=" + cclr1.isEnabled() + ",status=" + cclr1.getStatus() );
                assertEquals( "cclr1 is not null", cclr1 != null, true );
                if ( isOrgLevel )
                {
                    assertEquals( "cclr1 active ", cclr1.isActive(), true );
                }
                else
                {
                    assertEquals( "cclr1 active ", cclr1.isActive(), false );
                }
                log( "children rules=" + cclr1.getChildrenRules() );
                assertEquals( "cclr1 children rules not empty", cclr1.getChildrenRules().isEmpty(), false );

                // now re-establish credit so that other test cases can work fine.
                creditAdminSvc.establishCreditRelationship( lpOrg, lpTpForFi );
                CounterpartyCreditLimitRule activeCclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTpForFi );
                log( "cclr=" + activeCclr + ",cclr.enabled=" + activeCclr.isEnabled() + ",status=" + activeCclr.getStatus() );
                assertEquals( "cclr is not null", activeCclr != null, true );
                assertEquals( "cclr active ", activeCclr.isActive(), true );
                log( "children rules=" + activeCclr.getChildrenRules() );
                assertEquals( "cclr children rules not empty", activeCclr.getChildrenRules().isEmpty(), false );
                assertEquals( "checkSanity", sanityCheck( lpOrg ), true );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testSetCreditRelationshipAtTradingPartyLevel()
    {
        try
        {
            init( lpUser );

            CreditLimitOrgFunction orgFunc = CreditUtilC.getCreditLimitOrgFunction( lpOrg );
            boolean exposure = orgFunc.isOrgLevelCreditExposure();
            orgFunc.setOrgLevelCreditExposure( false );
            creditAdminSvc.establishCreditRelationship( lpOrg, lpTpForFi );
            orgFunc.setOrgLevelCreditExposure( exposure );
            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTpForFi );
            log( "cclr=" + cclr + ",cclr.enabled=" + cclr.isEnabled() + ",status=" + cclr.getStatus() );
            assertEquals( "cclr is not null", cclr != null, true );
            assertEquals( "cclr active ", cclr.isActive(), true );
            log( "children rules=" + cclr.getChildrenRules() );
            assertEquals( "cclr children rules not empty", cclr.getChildrenRules().isEmpty(), false );

            // check the spot date credit utilization.
            Double limit = CreditUtilizationManagerC.getInstance().getAvailableCreditLimit( lpLe, lpTpForFi, fiOrg, spotDate );
            log( "spot date limit=" + limit );
            assertEquals( "limit is not null. ", limit != null, true );

            // now remove the trading relationship.
            creditAdminSvc.removeCreditRelationship( lpOrg, lpTpForFi );
            CounterpartyCreditLimitRule cclr1 = CreditUtilC.getTradingPartyLevelCounterpartyCreditLimitRule( lpOrg, lpTpForFi );
            log( "cclr1=" + cclr1 + ",cclr1.enabled=" + cclr1.isEnabled() + ",status=" + cclr1.getStatus() );
            assertEquals( "cclr1 is not null", cclr1 != null, true );
            assertEquals( "cclr1 active ", cclr1.isActive(), false );
            log( "children rules=" + cclr1.getChildrenRules() );
            assertEquals( "cclr1 children rules not empty", cclr1.getChildrenRules().isEmpty(), false );

            // now re-establish credit so that other test cases can work fine.
            creditAdminSvc.establishCreditRelationship( lpOrg, lpTpForFi );
            CounterpartyCreditLimitRule activeCclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTpForFi );
            log( "cclr=" + activeCclr + ",cclr.enabled=" + activeCclr.isEnabled() + ",status=" + activeCclr.getStatus() );
            assertEquals( "cclr is not null", activeCclr != null, true );
            assertEquals( "cclr active ", activeCclr.isActive(), true );
            log( "children rules=" + activeCclr.getChildrenRules() );
            assertEquals( "cclr children rules not empty", activeCclr.getChildrenRules().isEmpty(), false );
            assertEquals( "checkSanity", sanityCheck( lpOrg ), true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testSetCreditLimitForDate()
    {
        try
        {
            init( lpUser );
            double limit0 = creditAdminSvc.getCreditLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, spotDate );
            log( "limit on spot date=" + limit0 );
            double newLimitOnSpotPlusTwo = limit0 + 100000000;
            Currency limitCcy = CreditUtilC.getCounterpartyCreditLimitCurrency( lpOrg, ddOrg, lpTpForDd );
            creditAdminSvc.setCreditLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, newLimitOnSpotPlusTwo, limitCcy, spotDate.addDays( 2 ) );
            double limitOnSpotPlusTwo = creditAdminSvc.getCreditLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, spotDate.addDays( 2 ) );
            log( "limit on spot date=" + limit0 + ",limit on spot+2=" + limitOnSpotPlusTwo );
            assertEquals( "new limit", Math.abs( newLimitOnSpotPlusTwo - limitOnSpotPlusTwo ) < MINIMUM, true );

            // now the set the limit globally
            double newLimit = limit0 + 1000000;
            creditAdminSvc.setCreditLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, newLimit, limitCcy );
            double limitOnSpot = creditAdminSvc.getCreditLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, spotDate );
            double spotPlusTwoLimit = creditAdminSvc.getCreditLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, spotDate.addDays( 2 ) );
            log( "limitOnSpot=" + limitOnSpot + ",spotPlusTwoLimit=" + spotPlusTwoLimit );
            assertEquals( "limit should be same on all days.", spotPlusTwoLimit, limitOnSpotPlusTwo );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testResetCreditLimitForDate()
    {
        try
        {
            init( lpUser );
            Currency limitCcy = CreditUtilC.getCounterpartyCreditLimitCurrency( lpOrg, ddOrg, lpTpForDd );
            creditAdminSvc.setCreditLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, null, limitCcy, spotDate );
            double limit0 = creditAdminSvc.getCreditLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, spotDate );
            log( "limit on spot date=" + limit0 );
            double newLimitOnSpotPlusTwo = limit0 + 100000000;
            creditAdminSvc.setCreditLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, newLimitOnSpotPlusTwo, limitCcy, spotDate.addDays( 2 ) );
            double limitOnSpotPlusTwo = creditAdminSvc.getCreditLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, spotDate.addDays( 2 ) );
            log( "limit on spot date=" + limit0 + ",limit on spot+2=" + limitOnSpotPlusTwo );
            assertEquals( "new limit", Math.abs( newLimitOnSpotPlusTwo - limitOnSpotPlusTwo ) < MINIMUM, true );

            // now reset the date specific limit to null.
            creditAdminSvc.setCreditLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, null, limitCcy, spotDate.addDays( 2 ) );
            double limitOnSpot = creditAdminSvc.getCreditLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, spotDate );
            double spotPlusTwoLimit = creditAdminSvc.getCreditLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, spotDate.addDays( 2 ) );
            log( "limitOnSpot=" + limitOnSpot + ",spotPlusTwoLimit=" + spotPlusTwoLimit );
            assertEquals( "limit should be same on all days.", Math.abs( limitOnSpot - spotPlusTwoLimit ) < MINIMUM, true );
            assertEquals( "limit should be same as new limit.", Math.abs( limitOnSpot - limit0 ) < MINIMUM, true );
            CreditUtilizationCacheEntry cce = CreditUtilizationManagerC.getInstance().getCreditUtilizationCacheEntry( lpLe, ddOrg, lpTpForDd, spotDate.addDays( 2 ), SUBSCRIBE_EVENT );
            Collection<CreditUtilization> creditUtils = cce.getCreditUtilizations();
            for ( CreditUtilization cu : creditUtils )
            {
                assertEquals( "limit on cu should be null. cu.limit=" + cu.getLimitAmount(), cu.getLimitAmount() == null, true );
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testSetDefaultAggregateNettingMethodology()
    {
        try
        {
            init( lpUser );
            CreditUtilizationCalculator existingCalc = creditAdminSvc.getDefaultAggregateNettingMethodology( lpOrg );
            CreditUtilizationCalculator newCalc;
            if ( existingCalc != null && existingCalc.isSameAs( AGGREGATE_LIMIT_CALCULATOR ) )
            {
                newCalc = NET_OPEN_POSITION_CALCULATOR;
            }
            else
            {
                newCalc = AGGREGATE_LIMIT_CALCULATOR;
            }
            log( "existing calc=" + existingCalc + ",new calc=" + newCalc );
            creditAdminSvc.setDefaultAggregateNettingMethodology( lpOrg, newCalc );
            CreditUtilizationCalculator checkCalc = creditAdminSvc.getDefaultAggregateNettingMethodology( lpOrg );
            log( "newly set calc=" + checkCalc + ",new calc=" + newCalc );
            assertEquals( "check newly set calc=" + checkCalc, checkCalc.isSameAs( newCalc ), true );
            CreditLimitOrgFunction orgFunc = CreditUtilC.getCreditLimitOrgFunction( lpOrg );
            assertEquals( "check new default aggregate calc=" + orgFunc.getDefaultAggregateCreditUtilizationCalculator(), checkCalc.isSameAs( orgFunc.getDefaultAggregateCreditUtilizationCalculator() ), true );

            Organization org = UserFactory.newOrganization();
            org.setShortName( "NewOrg" + System.currentTimeMillis() );
            org.setLongName( "LongName" );
            Namespace ns = EntityFactory.newNamespace();
            ns.setShortName( "NewNs" + System.currentTimeMillis() );
            ns.setDescription( "FI" );
            org.setNamespace( ns );

            // create and set the new legal entity.
            LegalEntity le = CounterpartyFactory.newLegalEntity();
            le.setShortName( "NewLEe" + System.currentTimeMillis() );
            le.setNamespace( ns );
            le.setOrganization( org );
            org.getLegalEntities().add( le );
            org.putCustomField( DEFAULT_DEALING_ENTITY_CUSTOM_FIELD_NAME, le );

            // set the business center reporting currency.
            BusinessCenter bs = BusinessCenterFactory.newBusinessCenter();
            bs.setShortName( le.getShortName() );
            bs.setReportingCurrency( CurrencyFactory.getCurrency( "USD" ) );
            le.setBusinessCenter( bs );

            // set the new org trading party.
            TradingParty orgTp = CounterpartyFactory.newTradingParty();
            orgTp.setLegalEntity( lpLe );
            orgTp.setShortName( lpLe.getShortName() );
            orgTp.setOrganization( org );
            orgTp.setNamespace( org.getNamespace() );
            org.getTradingParties().add( orgTp );

            // set the new lp trading party.
            TradingParty lpTp = CounterpartyFactory.newTradingParty();
            lpTp.setLegalEntity( le );
            lpTp.setShortName( le.getShortName() );
            lpTp.setOrganization( lpOrg );
            lpTp.setNamespace( org.getNamespace() );
            lpOrg.getTradingParties().add( lpTp );

            log( "le=" + le );
            log( "org=" + org );
            log( "org.tp=" + orgTp );
            log( "lp.tp=" + lpTp );
            log( "lp.tp.le.org=" + lpTp.getLegalEntityOrganization() );
            boolean exposure = orgFunc.isOrgLevelCreditExposure();
            orgFunc.setOrgLevelCreditExposure( true );
            creditAdminSvc.establishCreditRelationship( lpOrg, lpTp );
            orgFunc.setOrgLevelCreditExposure( exposure );
            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTp );
            log( "cclr=" + cclr + ",cclr.enabled=" + cclr.isEnabled() + ",status=" + cclr.getStatus() );
            assertEquals( "cclr is not null", cclr != null, true );
            assertEquals( "cclr active ", cclr.isActive(), true );
            log( "children rules=" + cclr.getChildrenRules() );
            assertEquals( "cclr children rules not empty", cclr.getChildrenRules().isEmpty(), false );

            CreditLimitRule clr = CreditUtilC.getCreditLimitRule( cclr, GROSS_NOTIONAL_CLASSIFICATION );
            assertEquals( "clr calc should be new calc=" + clr.getCreditUtilizationCalculator(), clr.getCreditUtilizationCalculator().isSameAs( newCalc ), true );
            boolean sanityCheck = sanityCheck( lpOrg );
            assertEquals( "sanityCheck on lpOrg=" + sanityCheck, sanityCheck, true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testSetDefaultDailyNettingMethodology()
    {
        try
        {
            init( lpUser );
            CreditUtilizationCalculator existingCalc = creditAdminSvc.getDefaultDailyNettingMethodology( lpOrg );
            CreditUtilizationCalculator newCalc;
            if ( existingCalc != null && existingCalc.isSameAs( GROSS_DAILY_LIMIT_CALCULATOR ) )
            {
                newCalc = DAILY_SETTLEMENT_LIMIT_CALCULATOR;
            }
            else
            {
                newCalc = GROSS_DAILY_LIMIT_CALCULATOR;
            }
            log( "existing calc=" + existingCalc + ",new calc=" + newCalc );
            creditAdminSvc.setDefaultDailyNettingMethodology( lpOrg, newCalc );
            CreditUtilizationCalculator checkCalc = creditAdminSvc.getDefaultDailyNettingMethodology( lpOrg );
            log( "newly set calc=" + checkCalc + ",new calc=" + newCalc );
            assertEquals( "check newly set calc=" + checkCalc, checkCalc.isSameAs( newCalc ), true );
            CreditLimitOrgFunction orgFunc = CreditUtilC.getCreditLimitOrgFunction( lpOrg );
            assertEquals( "check new default aggregate calc=" + orgFunc.getDefaultDailyCreditUtilizationCalculator(), checkCalc.isSameAs( orgFunc.getDefaultDailyCreditUtilizationCalculator() ), true );

            Organization org = UserFactory.newOrganization();
            org.setShortName( "NewOrg" + System.currentTimeMillis() );
            org.setLongName( "LongName" );
            Namespace ns = EntityFactory.newNamespace()
            ns.setShortName( "NewNs" + System.currentTimeMillis() );
            ns.setDescription( "FI" );
            org.setNamespace( ns );

            // create and set the new legal entity.
            LegalEntity le = CounterpartyFactory.newLegalEntity();
            le.setShortName( "NewLEe" + System.currentTimeMillis() );
            le.setNamespace( ns );
            le.setOrganization( org );
            org.getLegalEntities().add( le );
            org.putCustomField( DEFAULT_DEALING_ENTITY_CUSTOM_FIELD_NAME, le );

            // set the business center reporting currency.
            BusinessCenter bs = BusinessCenterFactory.newBusinessCenter();
            bs.setShortName( le.getShortName() );
            bs.setReportingCurrency( CurrencyFactory.getCurrency( "USD" ) );
            le.setBusinessCenter( bs );

            // set the new org trading party.
            TradingParty orgTp = ( TradingParty ) CounterpartyFactory.newTradingParty();
            orgTp.setLegalEntity( lpLe );
            orgTp.setShortName( lpLe.getShortName() );
            orgTp.setOrganization( org );
            orgTp.setNamespace( ( Namespace ) org.getNamespace() );
            org.getTradingParties().add( orgTp );

            // set the new lp trading party.
            TradingParty lpTp = CounterpartyFactory.newTradingParty();
            lpTp.setLegalEntity( le );
            lpTp.setShortName( le.getShortName() );
            lpTp.setOrganization( lpOrg );
            lpTp.setNamespace( ( Namespace ) lpOrg.getNamespace() );
            lpOrg.getTradingParties().add( lpTp );

            log( "le=" + le );
            log( "org=" + org );
            log( "org.tp=" + orgTp );
            log( "lp.tp=" + lpTp );
            log( "lp.tp.le.org=" + lpTp.getLegalEntityOrganization() );
            boolean exposure = orgFunc.isOrgLevelCreditExposure();
            orgFunc.setOrgLevelCreditExposure( true );
            creditAdminSvc.establishCreditRelationship( lpOrg, lpTp );
            orgFunc.setOrgLevelCreditExposure( exposure );
            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTp );
            log( "cclr=" + cclr + ",cclr.enabled=" + cclr.isEnabled() + ",status=" + cclr.getStatus() );
            assertEquals( "cclr is not null", cclr != null, true );
            assertEquals( "cclr active ", cclr.isActive(), true );
            log( "children rules=" + cclr.getChildrenRules() );
            assertEquals( "cclr children rules not empty", cclr.getChildrenRules().isEmpty(), false );

            CreditLimitRule clr = CreditUtilC.getCreditLimitRule( cclr, DAILY_SETTLEMENT_CLASSIFICATION );
            assertEquals( "clr calc should be new calc=" + clr.getCreditUtilizationCalculator(), clr.getCreditUtilizationCalculator().isSameAs( newCalc ), true );
            boolean sanityCheck = sanityCheck( lpOrg );
            assertEquals( "sanityCheck on lpOrg=" + sanityCheck, sanityCheck, true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testSetDefaultLimitCurrency()
    {
        try
        {
            init( lpUser );
            Currency existingCcy = creditAdminSvc.getDefaultLimitCurrency( lpOrg );
            Currency newCcy;
            Currency eur = CurrencyFactory.getCurrency( "EUR" );
            Currency usd = CurrencyFactory.getCurrency( "USD" );
            if ( existingCcy != null && existingCcy.isSameAs( eur ) )
            {
                newCcy = usd;
            }
            else
            {
                newCcy = eur;
            }
            log( "existing ccy=" + existingCcy + ",new ccy=" + newCcy );
            creditAdminSvc.setDefaultLimitCurrency( lpOrg, newCcy );
            Currency checkCcy = creditAdminSvc.getDefaultLimitCurrency( lpOrg );
            log( "newly set ccy=" + checkCcy + ",new ccy=" + newCcy );
            assertEquals( "check newly set ccy=" + checkCcy, checkCcy.isSameAs( newCcy ), true );
            CreditLimitOrgFunction orgFunc = CreditUtilC.getCreditLimitOrgFunction( lpOrg );
            assertEquals( "check new default ccy=" + orgFunc.getCreditLimitCurrency(), checkCcy.isSameAs( orgFunc.getCreditLimitCurrency() ), true );

            Organization org = UserFactory.newOrganization();
            org.setShortName( "NewOrg" + System.currentTimeMillis() );
            org.setLongName( "LongName" );
            Namespace ns = EntityFactory.newNamespace();
            ns.setShortName( "NewNs" + System.currentTimeMillis() );
            ns.setDescription( "FI" );
            org.setNamespace( ns );

            // create and set the new legal entity.
            LegalEntity le = CounterpartyFactory.newLegalEntity();
            le.setShortName( "NewLEe" + System.currentTimeMillis() );
            le.setNamespace( ns );
            le.setOrganization( org );
            org.getLegalEntities().add( le );
            org.putCustomField( DEFAULT_DEALING_ENTITY_CUSTOM_FIELD_NAME, le );

            // set the business center reporting currency.
            BusinessCenter bs = BusinessCenterFactory.newBusinessCenter();
            bs.setShortName( le.getShortName() );
            bs.setReportingCurrency( CurrencyFactory.getCurrency( "USD" ) );
            le.setBusinessCenter( bs );

            // set the new org trading party.
            TradingParty orgTp = CounterpartyFactory.newTradingParty();
            orgTp.setLegalEntity( lpLe );
            orgTp.setShortName( lpLe.getShortName() );
            orgTp.setOrganization( org );
            orgTp.setNamespace( org.getNamespace() );
            org.getTradingParties().add( orgTp );

            // set the new lp trading party.
            TradingParty lpTp = CounterpartyFactory.newTradingParty();
            lpTp.setLegalEntity( le );
            lpTp.setShortName( le.getShortName() );
            lpTp.setOrganization( lpOrg );
            lpTp.setNamespace( lpOrg.getNamespace() );
            lpOrg.getTradingParties().add( lpTp );

            log( "le=" + le );
            log( "org=" + org );
            log( "org.tp=" + orgTp );
            log( "lp.tp=" + lpTp );
            log( "lp.tp.le.org=" + lpTp.getLegalEntityOrganization() );
            boolean exposure = orgFunc.isOrgLevelCreditExposure();
            orgFunc.setOrgLevelCreditExposure( true );
            creditAdminSvc.establishCreditRelationship( lpOrg, lpTp );
            orgFunc.setOrgLevelCreditExposure( exposure );
            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTp );
            log( "cclr=" + cclr + ",cclr.enabled=" + cclr.isEnabled() + ",status=" + cclr.getStatus() );
            assertEquals( "cclr is not null", cclr != null, true );
            assertEquals( "cclr active ", cclr.isActive(), true );
            log( "children rules=" + cclr.getChildrenRules() );
            assertEquals( "cclr children rules not empty", cclr.getChildrenRules().isEmpty(), false );

            CreditLimitRule clr = CreditUtilC.getCreditLimitRule( cclr, DAILY_SETTLEMENT_CLASSIFICATION );
            assertEquals( "clr ccy should be new ccy=" + clr.getCurrency(), clr.getCurrency().isSameAs( newCcy ), true );
            boolean sanityCheck = sanityCheck( lpOrg );
            assertEquals( "sanityCheck on lpOrg=" + sanityCheck, sanityCheck, true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
        finally
        {
            creditAdminSvc.setDefaultLimitCurrency( lpOrg, CurrencyFactory.getCurrency( "USD" ) );
            creditAdminSvc.reinitialize( lpOrg );
        }
    }

    public void testReinitializeCreditProvider()
    {
        try
        {
            init( lpUser );
            double tradeAmt = 1000;
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 );
            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 );
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate );
            creditMgr.takeCredit( trade, lpLe, fiOrg, lpTpForFi );

            //todo creditAdminSvc.reinitializeCreditCounterpartyOrganization( lpOrg, fiOrg );
            boolean sanityCheck = sanityCheck( lpOrg );
            //todo assertEquals( "sanityCheck on lpOrg=" + sanityCheck, sanityCheck, true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testReinitializeCreditProviderExternalTx()
    {
        try
        {
            init( lpUser );

            double tradeAmt = 1000;
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate );
            creditMgr.takeCredit( trade, lpLe, fiOrg, lpTpForFi );
            //todo creditAdminSvc.reinitializeCreditCounterpartyOrganization( lpOrg, fiOrg );

            boolean sanityCheck = sanityCheck( lpOrg );
            //todo assertEquals( "sanityCheck on lpOrg=" + sanityCheck, sanityCheck, true );

        }
        catch ( Exception e )
        {
            slog.error( "testReinitializeCreditProviderExternalTx", e );
            fail();
        }
    }

    public void testReinitialize()
    {
        try
        {
            init( lpUser );
            double tradeAmt = 1000;
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate );
            creditMgr.takeCredit( trade, lpLe, fiOrg, lpTpForFi );

            //todo creditAdminSvc.reinitialize( lpOrg );

            boolean sanityCheck = sanityCheck( lpOrg );
            //todo assertEquals( "sanityCheck on lpOrg=" + sanityCheck, sanityCheck, true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testReinitializeExternalTx()
    {
        try
        {
            init( lpUser );
            double tradeAmt = 1000;
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate );
            creditMgr.takeCredit( trade, lpLe, fiOrg, lpTpForFi );

            //todo creditAdminSvc.reinitialize( lpOrg );

            boolean sanityCheck = sanityCheck( lpOrg );
            //todo assertEquals( "sanityCheck on lpOrg=" + sanityCheck, sanityCheck, true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }


    public void testSetRejectionEmailConfig()
    {
        try
        {
            init( lpUser );
            CreditLimitOrgFunction orgFunc = CreditUtilC.getCreditLimitOrgFunction( lpOrg );
            boolean isEnable = orgFunc.isEnableRejectionEmail();
            String emailId = "support" + System.currentTimeMillis() + "@integral.com";
            creditAdminSvc.setEnableRejectionEmail( lpOrg, !isEnable );
            creditAdminSvc.setSenderEmailAddress( lpOrg, emailId );

            assertEquals( "sender email address is=" + orgFunc.getSenderEmailAddress(), orgFunc.getSenderEmailAddress(), emailId );
            assertEquals( "enable rejection email is=" + orgFunc.isEnableRejectionEmail(), orgFunc.isEnableRejectionEmail(), !isEnable );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testResetCreditUtitilizationOnNoCreditUtilizationCalc()
    {
        try
        {
            init( lpUser );
            removeExistingCreditUtilizationEvents( lpOrg );
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 10000 );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 );
            double limit0 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "Credit limit before first bid trade take Credit : " + limit0 );
            double tradeAmt = 1000;
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate );
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd );
            double limit1 = getAvailableCreditLimit( lpTpForDd, spotDate, DAILY_SETTLEMENT_CLASSIFICATION );
            log( "Credit limit after first bid trade take Credit : " + limit1 + " success : " + cwm.getStatus() );
            if ( CreditUtilC.getLimitCurrency( lpOrg ).isSameAs( CurrencyFactory.getCurrency( "USD" ) ) )
            {
                assertEquals( "before limit=" + limit0 + ",limitAfter=" + limit1, true, Math.abs( limit0 - limit1 - tradeAmt ) < MINIMUM );
            }
            validateCreditUtilizationEvents( cwm );

            // now set the credit calculator to null for daily.
            sleepFor( 5000 );
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, null, 10000 );
            Collection<CreditUtilizationEvent> cues = cwm.getCreditUtilizationEvents();
            for ( CreditUtilizationEvent cue : cues )
            {
                CreditUtilization cu = cue.getCreditUtilization();
                if ( cu.getCreditLimitRule() instanceof DailyCreditLimitRule )
                {
                    log( " cu used amount=" + cu.getUsedAmount() + ",cu.reserveAmt=" + cu.getReservedAmount() + ",cu=" + cu );
                    //todo assertEquals( "cu.usedAmt=" + cu.getUsedAmount(), true, Math.abs( cu.getUsedAmount() - 0.0 ) < MINIMUM );
                    //todo assertEquals( "cu.reserveAmt=" + cu.getReservedAmount(), true, Math.abs( cu.getReservedAmount() - 0.0 ) < MINIMUM );
                }
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
        finally
        {
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 10000 );
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 );
        }
    }

    public void testDefaultCreditUtilizationCalculatorAndCurrencyOnReinitialize()
    {
        try
        {
            init( lpUser );
            creditAdminSvc.setDefaultAggregateNettingMethodology( lpOrg, null );
            creditAdminSvc.setDefaultDailyNettingMethodology( lpOrg, null );
            creditAdminSvc.setDefaultLimitCurrency( lpOrg, CurrencyFactory.getCurrency( "GBP" ) );
            //todo creditAdminSvc.reinitialize( lpOrg );

            CreditUtilizationCalculator aggregateNettingCalc = creditAdminSvc.getNettingMethodology( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION );
            CreditUtilizationCalculator dailyNettingCalc = creditAdminSvc.getNettingMethodology( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION );
            Currency limitCcy = creditAdminSvc.getCreditLimitCurrency( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION );
            log( "aggregateNettingCalc=" + aggregateNettingCalc + ",dailyNettingCalc=" + dailyNettingCalc + ",limitCcy=" + limitCcy );
            //todo assertEquals( "aggregateNettingCalc should be null.", aggregateNettingCalc, null );
            //todo assertEquals( "limitCcy should not be null.", CurrencyFactory.getCurrency( "GBP" ).isSameAs( limitCcy ), true );
            boolean sanityCheck = sanityCheck( lpOrg );
            assertEquals( "sanityCheck on lpOrg=" + sanityCheck, sanityCheck, true );

            creditAdminSvc.setDefaultAggregateNettingMethodology( lpOrg, GROSS_AGGREGATE_LIMIT_CALCULATOR );
            creditAdminSvc.setDefaultDailyNettingMethodology( lpOrg, GROSS_DAILY_LIMIT_CALCULATOR );
            creditAdminSvc.setDefaultLimitCurrency( lpOrg, CurrencyFactory.getCurrency( "USD" ) );
            creditAdminSvc.reinitialize( lpOrg );

            CreditUtilizationCalculator aggregateNettingCalc1 = creditAdminSvc.getNettingMethodology( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION );
            CreditUtilizationCalculator dailyNettingCalc1 = creditAdminSvc.getNettingMethodology( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION );
            Currency limitCcy1 = creditAdminSvc.getCreditLimitCurrency( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION );
            log( "aggregateNettingCalc1=" + aggregateNettingCalc1 + ",dailyNettingCalc1=" + dailyNettingCalc1 + ",limitCcy1=" + limitCcy1 );
            assertEquals( "aggregateNettingCalc1 should not be null.", GROSS_AGGREGATE_LIMIT_CALCULATOR.isSameAs( aggregateNettingCalc1 ), true );
            assertEquals( "dailyNettingCalc should not be null.", GROSS_DAILY_LIMIT_CALCULATOR.isSameAs( dailyNettingCalc1 ), true );
            assertEquals( "limitCcy should not be null.", CurrencyFactory.getCurrency( "USD" ).isSameAs( limitCcy1 ), true );
            boolean sanityCheck1 = sanityCheck( lpOrg );
            assertEquals( "sanityCheck1 on lpOrg=" + sanityCheck1, sanityCheck1, true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            //todo fail();
        }
        finally
        {
            creditAdminSvc.setDefaultLimitCurrency( lpOrg, CurrencyFactory.getCurrency( "USD" ) );
            creditAdminSvc.reinitialize( lpOrg );
        }
    }

    public void testCreditRuleActivation()
    {
        try
        {
            //For LE exposure
            init( lpUser );
            creditAdminSvc.establishCreditRelationship( lpOrg, lpTpForFi );
            creditAdminSvc.setLegalEntityExposureLevel( lpOrg, fiOrg );
            CounterpartyCreditLimitRule leCclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTpForFi );
            assertEquals( "The status should be active", leCclr.getStatus(), Entity.ACTIVE_STATUS );

            creditAdminSvc.removeCreditRelationship( lpOrg, lpTpForFi );
            assertEquals( "The status should be inactive", leCclr.getStatus(), Entity.PASSIVE_STATUS );

            Collection<CounterpartyCreditLimitRule> cclrs = new ArrayList<CounterpartyCreditLimitRule>();
            cclrs.add( leCclr );
            creditAdminSvc.setCounterpartyCreditLimitRulesActive( cclrs, Entity.ACTIVE_STATUS );
            assertEquals( "The status should be active", leCclr.getStatus(), Entity.ACTIVE_STATUS );

            creditAdminSvc.setCreditEnabled( lpOrg, lpTpForFi, false );
            creditAdminSvc.setCounterpartyCreditLimitRulesActive( cclrs, Entity.PASSIVE_STATUS );
            assertEquals( "The status should be inactive", leCclr.getStatus(), Entity.PASSIVE_STATUS );

            //For Org exposure
            creditAdminSvc.establishCreditRelationship( lpOrg, lpTpForFi );
            creditAdminSvc.setOrganizationExposureLevel( lpOrg, fiOrg );
            CounterpartyCreditLimitRule orgCclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTpForFi );
            assertEquals( "The status should be active", orgCclr.getStatus(), Entity.ACTIVE_STATUS );

            creditAdminSvc.removeCreditRelationship( lpOrg, lpTpForFi );
            assertEquals( "The status should still be active", orgCclr.getStatus(), Entity.ACTIVE_STATUS );

            cclrs = new ArrayList<CounterpartyCreditLimitRule>();
            cclrs.add( orgCclr );
            creditAdminSvc.setCounterpartyCreditLimitRulesActive( cclrs, Entity.ACTIVE_STATUS );
            assertEquals( "The status should be active", orgCclr.getStatus(), Entity.ACTIVE_STATUS );

        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
        finally
        {
            creditAdminSvc.establishCreditRelationship( lpOrg, lpTpForFi );
            creditAdminSvc.setLegalEntityExposureLevel( lpOrg, fiOrg );
            creditAdminSvc.setCreditEnabled( lpOrg, lpTpForFi, true );
        }
    }

    public void testCreditRuleInactivationValidation()
    {
        try
        {
            //set trading relation and try to inactivate -- should not allow to inactivate
            init( lpUser );
            creditAdminSvc.establishCreditRelationship( lpOrg, lpTpForFi );
            lpTpForFi.setStatus( Entity.ACTIVE_STATUS );
            creditAdminSvc.setLegalEntityExposureLevel( lpOrg, fiOrg );
            CounterpartyCreditLimitRule leCclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTpForFi );
            assertEquals( "The status should be active", leCclr.getStatus(), Entity.ACTIVE_STATUS );

            Collection<CounterpartyCreditLimitRule> cclrs = new ArrayList<CounterpartyCreditLimitRule>();
            cclrs.add( leCclr );
            creditAdminSvc.setCounterpartyCreditLimitRulesActive( cclrs, Entity.PASSIVE_STATUS );
            assertEquals( "Due to validation failure the status should not change", leCclr.getStatus(), Entity.ACTIVE_STATUS );

            // remove trading relation and try to inactivate -- status should change
            creditAdminSvc.removeCreditRelationship( lpOrg, lpTpForFi );
            lpTpForFi.setStatus( Entity.PASSIVE_STATUS );
            cclrs = new ArrayList<CounterpartyCreditLimitRule>();
            cclrs.add( leCclr );
            creditAdminSvc.setCounterpartyCreditLimitRulesActive( cclrs, Entity.PASSIVE_STATUS );
            assertEquals( "The validation should succeed and the status should be inactive", leCclr.getStatus(), Entity.PASSIVE_STATUS );

            // pass empty collection also
            cclrs = new ArrayList<CounterpartyCreditLimitRule>();
            creditAdminSvc.setCounterpartyCreditLimitRulesActive( cclrs, Entity.ACTIVE_STATUS );
            assertEquals( "For the empty collection the status should remain inactive", leCclr.getStatus(), Entity.PASSIVE_STATUS );

            cclrs = new ArrayList<CounterpartyCreditLimitRule>();
            cclrs.add( leCclr );
            creditAdminSvc.setCounterpartyCreditLimitRulesActive( cclrs, Entity.ACTIVE_STATUS );
            assertEquals( "The status should change to active for non-empty collection", leCclr.getStatus(), Entity.ACTIVE_STATUS );

            // now establish the relationship back and set the credit disabled and try inactivating.
            creditAdminSvc.establishCreditRelationship( lpOrg, lpTpForFi );
            lpTpForFi.setStatus( Entity.ACTIVE_STATUS );

            creditAdminSvc.setCounterpartyCreditLimitRulesActive( cclrs, Entity.ACTIVE_STATUS );
            assertEquals( "The status should change to active for non-empty collection", leCclr.getStatus(), Entity.ACTIVE_STATUS );

            creditAdminSvc.setCreditEnabled( lpOrg, lpTpForFi, false );
            creditAdminSvc.setCounterpartyCreditLimitRulesActive( cclrs, Entity.PASSIVE_STATUS );
            assertEquals( "The status should change to active for non-empty collection", leCclr.getStatus(), Entity.PASSIVE_STATUS );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
        finally
        {
            lpTpForFi.setStatus( Entity.ACTIVE_STATUS );
            creditAdminSvc.establishCreditRelationship( lpOrg, lpTpForFi );
            creditAdminSvc.setLegalEntityExposureLevel( lpOrg, fiOrg );
            creditAdminSvc.setCreditEnabled( lpOrg, lpTpForFi, true );
        }
    }

    public void testRemoveCreditRelationshipOnOrgLevelExposure()
    {
        try
        {
            init( lpUser );
            creditAdminSvc.establishCreditRelationship( lpOrg, lpTpForFi );
            creditAdminSvc.setOrganizationExposureLevel( lpOrg, fiOrg );
            CounterpartyCreditLimitRule orgCclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTpForFi );
            log( "orgCclr=" + orgCclr );
            assertEquals( "orgCclr should be active and at org level", orgCclr.isActive() && orgCclr.isOrgLevel(), true );

            // now remove the credit relationship.
            creditAdminSvc.removeCreditRelationship( lpOrg, lpTpForFi );
            CounterpartyCreditLimitRule orgCclr1 = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTpForFi );
            log( "orgCclr1=" + orgCclr1 );
            assertEquals( "orgCclr1 should be active and at org level", orgCclr1.isActive() && orgCclr1.isOrgLevel(), true );

            // now re-establish the credit relationship.
            creditAdminSvc.setLegalEntityExposureLevel( lpOrg, fiOrg );
            creditAdminSvc.establishCreditRelationship( lpOrg, lpTpForFi );
            boolean sanityCheck = CreditUtilC.sanityCheck( lpOrg, fiOrg, true );
            log( "sanityCheck=" + sanityCheck );
            assertEquals( "sanityCheck should be true", sanityCheck, true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testSetOrgLevelLeverageFactor()
    {
        try
        {
            init( lpUser );
            Double existingLeverageFactor = creditAdminSvc.getDefaultLeverageFactor( lpOrg );
            Double newLeverageFactor;
            if ( existingLeverageFactor == null || existingLeverageFactor == 1.0 )
            {
                newLeverageFactor = 100.0;
            }
            else
            {
                newLeverageFactor = existingLeverageFactor + ( Math.random() > 0.5 ? 10 : -10 );
            }
            creditAdminSvc.setDefaultLeverageFactor( lpOrg, newLeverageFactor );
            assertEquals( "check credit admin service getDefaultLeverageFactor", newLeverageFactor == creditAdminSvc.getDefaultLeverageFactor( lpOrg ), true );
            CreditLimitRuleSet clrs = CreditUtilC.getCreditLimitRuleSet( lpOrg );
            log( "clrs.getLeverageFactor=" + clrs.getLeverageFactor() + ",newLeverageFactor=" + newLeverageFactor );
            assertEquals( "credit limit rule set value should be same. clrs.getLeverageFactor=" + clrs.getLeverageFactor() + ",newLeverageFactor=" + newLeverageFactor, clrs.getLeverageFactor().equals( newLeverageFactor ), true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testSetOrgLevelApplyPandL()
    {
        try
        {
            init( lpUser );
            Boolean existingApplyPandL = creditAdminSvc.isDefaultApplyPandL( lpOrg );
            Boolean newApplyPandL;
            if ( existingApplyPandL == null )
            {
                newApplyPandL = true;
            }
            else
            {
                newApplyPandL = !existingApplyPandL;
            }
            creditAdminSvc.setDefaultApplyPandL( lpOrg, newApplyPandL );
            assertEquals( "check credit admin service getDefaultApplyPandL", newApplyPandL == creditAdminSvc.isDefaultApplyPandL( lpOrg ), true );
            CreditLimitRuleSet clrs = CreditUtilC.getCreditLimitRuleSet( lpOrg );
            log( "clrs.existingApplyPandL=" + existingApplyPandL + ",newApplyPandL=" + newApplyPandL );
            assertEquals( "credit limit rule set value should be same. clrs.applyPandL=" + clrs.isApplyPandL() + ",newApplyPandL=" + newApplyPandL, clrs.isApplyPandL().equals( newApplyPandL ), true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testSetOrgLevelIgnoreCurrDatePositions()
    {
        try
        {
            init( lpUser );
            Boolean existingIgnoreCurrDatePositions = creditAdminSvc.isDefaultIgnoreCurrDatePositions( lpOrg );
            Boolean newIgnoreCurrDatePositions;
            if ( existingIgnoreCurrDatePositions == null )
            {
                newIgnoreCurrDatePositions = true;
            }
            else
            {
                newIgnoreCurrDatePositions = !existingIgnoreCurrDatePositions;
            }
            creditAdminSvc.setDefaultIgnoreCurrDatePositions( lpOrg, newIgnoreCurrDatePositions );
            assertEquals( "check credit admin service getDefaultIgnoreCurrDatePositions", newIgnoreCurrDatePositions == creditAdminSvc.isDefaultIgnoreCurrDatePositions( lpOrg ), true );
            CreditLimitRuleSet clrs = CreditUtilC.getCreditLimitRuleSet( lpOrg );
            log( "clrs.existingIgnoreCurrDatePositions=" + existingIgnoreCurrDatePositions + ",newIgnoreCurrDatePositions=" + newIgnoreCurrDatePositions );
            assertEquals( "credit limit rule set value should be same. clrs.ignoreCurrentDatePositions=" + clrs.ignoreCurrentDatePositions() + ",newIgnoreCurrDatePositions=" + newIgnoreCurrDatePositions, clrs.ignoreCurrentDatePositions().equals( newIgnoreCurrDatePositions ), true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testSetCptyLevelLeverageFactor()
    {
        try
        {
            init( lpUser );
            Double existingLeverageFactor = creditAdminSvc.getLeverageFactor( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION );

            Double newLeverageFactor;
            if ( existingLeverageFactor == null || existingLeverageFactor == 1.0 )
            {
                newLeverageFactor = 100.0;
            }
            else
            {
                newLeverageFactor = existingLeverageFactor + ( Math.random() > 0.5 ? 10 : -10 );
            }
            creditAdminSvc.setLeverageFactor( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, newLeverageFactor );
            assertEquals( "check credit admin service getLeverageFactor", newLeverageFactor == creditAdminSvc.getLeverageFactor( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION ), true );
            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTpForDd );
            CreditLimitRule sclr = CreditUtilC.getCreditLimitRule( cclr, GROSS_NOTIONAL_CLASSIFICATION );
            log( "clrs.getLeverageFactor=" + sclr.getLeverageFactor() + ",newLeverageFactor=" + newLeverageFactor );
            assertEquals( "credit limit rule set value should be same. clrs.getLeverageFactor=" + sclr.getLeverageFactor() + ",newLeverageFactor=" + newLeverageFactor, sclr.getLeverageFactor().equals( newLeverageFactor ), true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
        finally
        {
            creditAdminSvc.setLeverageFactor( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, 1.0 );
        }
    }

    public void testSetCptyLevelApplyPandL()
    {
        try
        {
            init( lpUser );
            Boolean existingApplyPandL = creditAdminSvc.isApplyPandL( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION );
            Boolean newApplyPandL;
            if ( existingApplyPandL == null )
            {
                newApplyPandL = true;
            }
            else
            {
                newApplyPandL = !existingApplyPandL;
            }
            creditAdminSvc.setApplyPandL( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, newApplyPandL );
            assertEquals( "check credit admin service getApplyPandL", newApplyPandL == creditAdminSvc.isApplyPandL( lpOrg, lpTpForDd, CreditLimitConstants.GROSS_NOTIONAL_CLASSIFICATION ), true );
            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTpForDd );
            CreditLimitRule sclr = CreditUtilC.getCreditLimitRule( cclr, GROSS_NOTIONAL_CLASSIFICATION );
            log( "clrs.existingApplyPandL=" + existingApplyPandL + ",newApplyPandL=" + newApplyPandL );
            assertEquals( "credit limit rule set value should be same. clrs.applyPandL=" + sclr.isApplyPandL() + ",newApplyPandL=" + newApplyPandL, sclr.isApplyPandL().equals( newApplyPandL ), true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testSetCptyLevelIgnoreCurrDatePositions()
    {
        try
        {
            init( lpUser );
            Boolean existingIgnoreCurrDatePositions = creditAdminSvc.isIgnoreCurrDatePositions( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION );
            Boolean newIgnoreCurrDatePositions;
            if ( existingIgnoreCurrDatePositions == null )
            {
                newIgnoreCurrDatePositions = true;
            }
            else
            {
                newIgnoreCurrDatePositions = !existingIgnoreCurrDatePositions;
            }
            creditAdminSvc.setIgnoreCurrDatePositions( lpOrg, lpTpForDd, CreditLimitConstants.GROSS_NOTIONAL_CLASSIFICATION, newIgnoreCurrDatePositions );
            assertEquals( "check credit admin service getIgnoreCurrDatePositions", newIgnoreCurrDatePositions == creditAdminSvc.isIgnoreCurrDatePositions( lpOrg, lpTpForDd, CreditLimitConstants.GROSS_NOTIONAL_CLASSIFICATION ), true );
            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTpForDd );
            CreditLimitRule sclr = CreditUtilC.getCreditLimitRule( cclr, CreditLimitConstants.GROSS_NOTIONAL_CLASSIFICATION );
            log( "clrs.existingIgnoreCurrDatePositions=" + existingIgnoreCurrDatePositions + ",newIgnoreCurrDatePositions=" + newIgnoreCurrDatePositions );
            assertEquals( "credit limit rule set value should be same. clrs.IgnoreCurrDatePositions=" + sclr.ignoreCurrentDatePositions() + ",newIgnoreCurrDatePositions=" + newIgnoreCurrDatePositions, sclr.ignoreCurrentDatePositions().equals( newIgnoreCurrDatePositions ), true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testSetOrgLevelMaximumTenor()
    {
        try
        {
            init( lpUser );
            Tenor existingMaximumTenor = creditAdminSvc.getDefaultMaximumTenor( lpOrg );
            Tenor newMaximumTenor;
            if ( existingMaximumTenor == null )
            {
                newMaximumTenor = new Tenor( "5D" );
            }
            else
            {
                DatePeriod datePeriodFor5D = DateTimeFactory.newDatePeriod( 0, 0, 5 );
                newMaximumTenor = new Tenor( existingMaximumTenor.getDatePeriod().add( datePeriodFor5D ).getToString() );
            }
            creditAdminSvc.setDefaultMaximumTenor( lpOrg, newMaximumTenor );
            assertEquals( "check credit admin service getDefaultMaximumTenor", newMaximumTenor.compareTo( creditAdminSvc.getDefaultMaximumTenor( lpOrg ) ), 0 );
            CreditLimitRuleSet clrs = CreditUtilC.getCreditLimitRuleSet( lpOrg );
            log( "clrs.getMaximumTenor=" + clrs.getMaximumTenor() + ",newMaximumTenor=" + newMaximumTenor );
            assertEquals( "credit limit rule set value should be same. clrs.getMaximumTenor=" + clrs.getMaximumTenor() + ",newMaximumTenor=" + newMaximumTenor, clrs.getMaximumTenor().compareTo( newMaximumTenor ), 0 );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
        finally
        {
            creditAdminSvc.setDefaultMaximumTenor( lpOrg, null );
        }
    }

    public void testSetCptyLevelMaximumTenor()
    {
        try
        {
            init( lpUser );
            Tenor existingMaximumTenor = creditAdminSvc.getMaximumTenor( lpOrg, lpTpForDd );

            Tenor newMaximumTenor;
            if ( existingMaximumTenor == null )
            {
                newMaximumTenor = new Tenor( "5D" );
            }
            else
            {
                DatePeriod datePeriodFor5D = DateTimeFactory.newDatePeriod( 0, 0, 5 );
                newMaximumTenor = new Tenor( existingMaximumTenor.getDatePeriod().add( datePeriodFor5D ).getToString() );
            }
            creditAdminSvc.setMaximumTenor( lpOrg, lpTpForDd, newMaximumTenor );
            assertEquals( "check credit admin service getMaximumTenor", newMaximumTenor.compareTo( creditAdminSvc.getMaximumTenor( lpOrg, lpTpForDd ) ), 0 );
            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTpForDd );
            log( "cclr.getMaximumTenor=" + cclr.getMaximumTenor() + ",newMaximumTenor=" + newMaximumTenor );
            assertEquals( "cpty credit limit rule value should be same. cclr.getMaximumTenor=" + cclr.getMaximumTenor() + ",newMaximumTenor=" + newMaximumTenor, cclr.getMaximumTenor().compareTo( newMaximumTenor ), 0 );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
        finally
        {
            creditAdminSvc.setMaximumTenor( lpOrg, lpTpForDd, null );
        }
    }

    public void testSetOrgLevelMinimumTenor()
    {
        try
        {
            init( lpUser );
            Tenor existingMinimumTenor = creditAdminSvc.getDefaultMinimumTenor( lpOrg );
            Tenor newMinimumTenor;
            if ( existingMinimumTenor == null )
            {
                newMinimumTenor = new Tenor( "5D" );
            }
            else
            {
                DatePeriod datePeriodFor5D = DateTimeFactory.newDatePeriod( 0, 0, 5 );
                newMinimumTenor = new Tenor( existingMinimumTenor.getDatePeriod().add( datePeriodFor5D ).getToString() );
            }
            creditAdminSvc.setDefaultMinimumTenor( lpOrg, newMinimumTenor );
            assertEquals( "check credit admin service getDefaultMinimumTenor", newMinimumTenor.compareTo( creditAdminSvc.getDefaultMinimumTenor( lpOrg ) ), 0 );
            CreditLimitRuleSet clrs = CreditUtilC.getCreditLimitRuleSet( lpOrg );
            log( "clrs.getMinimumTenor=" + clrs.getMinimumTenor() + ",newMinimumTenor=" + newMinimumTenor );
            assertEquals( "credit limit rule set value should be same. clrs.getMinimumTenor=" + clrs.getMinimumTenor() + ",newMinimumTenor=" + newMinimumTenor, clrs.getMinimumTenor().compareTo( newMinimumTenor ), 0 );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
        finally
        {
            creditAdminSvc.setDefaultMinimumTenor( lpOrg, null );
        }
    }

    public void testSetCptyLevelMinimumTenor()
    {
        try
        {
            init( lpUser );
            Tenor existingMinimumTenor = creditAdminSvc.getMinimumTenor( lpOrg, lpTpForDd );

            Tenor newMinimumTenor;
            if ( existingMinimumTenor == null )
            {
                newMinimumTenor = new Tenor( "5D" );
            }
            else
            {
                DatePeriod datePeriodFor5D = DateTimeFactory.newDatePeriod( 0, 0, 5 );
                newMinimumTenor = new Tenor( existingMinimumTenor.getDatePeriod().add( datePeriodFor5D ).getToString() );
            }
            creditAdminSvc.setMinimumTenor( lpOrg, lpTpForDd, newMinimumTenor );
            assertEquals( "check credit admin service getMinimumTenor", newMinimumTenor.compareTo( creditAdminSvc.getMinimumTenor( lpOrg, lpTpForDd ) ), 0 );
            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTpForDd );
            log( "cclr.getMinimumTenor=" + cclr.getMinimumTenor() + ",newMinimumTenor=" + newMinimumTenor );
            assertEquals( "cpty credit limit rule value should be same. cclr.getMinimumTenor=" + cclr.getMinimumTenor() + ",newMinimumTenor=" + newMinimumTenor, cclr.getMinimumTenor().compareTo( newMinimumTenor ), 0 );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
        finally
        {
            creditAdminSvc.setMinimumTenor( lpOrg, lpTpForDd, null );
        }
    }

    public void testSetOrgLevelUpdateBalanceWithPL()
    {
        try
        {
            init( lpUser );
            Boolean existingUpdateBalanceWithPL = creditAdminSvc.isDefaultUpdateBalanceWithPL( lpOrg );
            Boolean newUpdateBalanceWithPL;
            if ( existingUpdateBalanceWithPL == null )
            {
                newUpdateBalanceWithPL = true;
            }
            else
            {
                newUpdateBalanceWithPL = !existingUpdateBalanceWithPL;
            }
            creditAdminSvc.setDefaultUpdateBalanceWithPL( lpOrg, newUpdateBalanceWithPL );
            assertEquals( "check credit admin service getDefaultUpdateBalanceWithPL", newUpdateBalanceWithPL == creditAdminSvc.isDefaultUpdateBalanceWithPL( lpOrg ), true );
            CreditLimitRuleSet clrs = CreditUtilC.getCreditLimitRuleSet( lpOrg );
            log( "clrs.existingUpdateBalanceWithPL=" + existingUpdateBalanceWithPL + ",newUpdateBalanceWithPL=" + newUpdateBalanceWithPL );
            assertEquals( "credit limit rule set value should be same. clrs.updateBalanceWithPL=" + clrs.isUpdateBalanceWithPL() + ",newUpdateBalanceWithPL=" + newUpdateBalanceWithPL, clrs.isUpdateBalanceWithPL().equals( newUpdateBalanceWithPL ), true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testSetCptyLevelUpdateBalanceWithPL()
    {
        try
        {
            init( lpUser );
            Boolean existingUpdateBalanceWithPL = creditAdminSvc.isUpdateBalanceWithPL( lpOrg, lpTpForDd, CreditLimitConstants.GROSS_NOTIONAL_CLASSIFICATION );
            Boolean newUpdateBalanceWithPL;
            newUpdateBalanceWithPL = existingUpdateBalanceWithPL == null || !existingUpdateBalanceWithPL;
            creditAdminSvc.setUpdateBalanceWithPL( lpOrg, lpTpForDd, CreditLimitConstants.GROSS_NOTIONAL_CLASSIFICATION, newUpdateBalanceWithPL );
            assertEquals( "check credit admin service getUpdateBalanceWithPL", newUpdateBalanceWithPL == creditAdminSvc.isUpdateBalanceWithPL( lpOrg, lpTpForDd, CreditLimitConstants.GROSS_NOTIONAL_CLASSIFICATION ), true );
            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTpForDd );
            CreditLimitRule sclr = CreditUtilC.getCreditLimitRule( cclr, CreditLimitConstants.GROSS_NOTIONAL_CLASSIFICATION );
            log( "clrs.existingUpdateBalanceWithPL=" + existingUpdateBalanceWithPL + ",newUpdateBalanceWithPL=" + newUpdateBalanceWithPL );
            assertEquals( "credit limit rule set value should be same. clrs.newUpdateBalanceWithPL=" + sclr.isUpdateBalanceWithPL() + ",newUpdateBalanceWithPL=" + newUpdateBalanceWithPL, sclr.isUpdateBalanceWithPL().equals( newUpdateBalanceWithPL ), true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
    }

    public void testEndOfDayUpdateBalanceWithPL()
    {
        try
        {
            IdcDate businessDate = EndOfDayServiceFactory.getEndOfDayService().getCurrentTradeDate();
            CreditUtilizationManagerC.getInstance().recalculateAllAggregateCreditUtilizations( businessDate, null );
        }
        catch ( Exception e )
        {
            fail( "testEndOfDayUpdateBalanceWithPL", e );
        }
    }

    public void testSetOrgLevelDailyPL()
    {
        try
        {
            init( lpUser );
            Boolean existingDailyPL = creditAdminSvc.isDefaultDailyPL( lpOrg );
            Boolean newDailyPL;
            newDailyPL = existingDailyPL == null || !existingDailyPL;
            creditAdminSvc.setDefaultDailyPL( lpOrg, newDailyPL );
            assertEquals( "check credit admin service isDefaultDailyPL", newDailyPL == creditAdminSvc.isDefaultDailyPL( lpOrg ), true );
            CreditLimitRuleSet clrs = CreditUtilC.getCreditLimitRuleSet( lpOrg );
            log( "clrs.existingDailyPL=" + existingDailyPL + ",newDailyPL=" + newDailyPL );
            assertEquals( "credit limit rule set value should be same. clrs.dailyPL=" + clrs.isDailyPL() + ",newDailyPL=" + newDailyPL, clrs.isDailyPL().equals( newDailyPL ), true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
        finally
        {
            creditAdminSvc.setDefaultDailyPL( lpOrg, false );
        }
    }

    public void testSetCptyLevelDailyPL()
    {
        try
        {
            init( lpUser );
            Boolean existingDailyPL = creditAdminSvc.isDailyPL( lpOrg, lpTpForDd, CreditLimitConstants.GROSS_NOTIONAL_CLASSIFICATION );
            Boolean newDailyPL;
            newDailyPL = existingDailyPL == null || !existingDailyPL;
            creditAdminSvc.setDailyPL( lpOrg, lpTpForDd, CreditLimitConstants.GROSS_NOTIONAL_CLASSIFICATION, newDailyPL );
            assertEquals( "check credit admin service isDailyPL", newDailyPL == creditAdminSvc.isDailyPL( lpOrg, lpTpForDd, CreditLimitConstants.GROSS_NOTIONAL_CLASSIFICATION ), true );
            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTpForDd );
            CreditLimitRule sclr = CreditUtilC.getCreditLimitRule( cclr, CreditLimitConstants.GROSS_NOTIONAL_CLASSIFICATION );
            log( "clr.existingDailyPL=" + existingDailyPL + ",newDailyPL=" + newDailyPL );
            assertEquals( "credit limit rule set value should be same. clr.newDailyPL=" + sclr.isDailyPL() + ",newDailyPL=" + newDailyPL, sclr.isDailyPL().equals( newDailyPL ), true );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
        finally
        {
            creditAdminSvc.setDailyPL( lpOrg, lpTpForDd, CreditLimitConstants.GROSS_NOTIONAL_CLASSIFICATION, false );
        }
    }

    public void testSetOrgCurrencyPairGroupExemption()
    {
        try
        {
            init( lpUser );
            CurrencyPairGroup currencyPairGroup = getCurrencyPairGroup( "G7" );
            creditAdminSvc.setCurrencyPairGroupExemption( lpOrg, currencyPairGroup );
            CreditLimitRuleSet clrs = CreditUtilC.getCreditLimitRuleSet( lpOrg );
            assertTrue( clrs.getExemptCurrencyPairGroup().isSameAs( currencyPairGroup ) );
        }
        catch ( Exception e )
        {
            e.printStackTrace();
            fail();
        }
        finally
        {
            creditAdminSvc.setCurrencyPairGroupExemption( lpOrg, null );
        }
    }

    public void testSetCounterpartyLevelCurrencyPairGroupExemption()
    {
        try
        {
            init( lpUser );
            CurrencyPairGroup currencyPairGroup = getCurrencyPairGroup( "G7" );
            creditAdminSvc.setCurrencyPairGroupExemption( lpOrg, lpTpForDd, currencyPairGroup );
            CreditLimitRuleSet clrs = CreditUtilC.getCreditLimitRuleSet( lpOrg );
            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTpForDd );
            assertTrue( cclr.getExemptCurrencyPairGroup().isSameAs( currencyPairGroup ) );
        }
        catch ( Exception e )
        {
            fail( "testSetCounterpartyLevelCurrencyPairGroupExemption", e );
        }
        finally
        {
            creditAdminSvc.setCurrencyPairGroupExemption( lpOrg, lpTpForDd, null );
        }
    }

    public void testSetCounterpartyLevelUseDefaultExemptCurrencyPairGroup()
    {
        try
        {
            init( lpUser );
            creditAdminSvc.setUseDefaultExemptCurrencyPairGroup( lpOrg, lpTpForDd, false );
            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTpForDd );
            assertFalse( cclr.isUseDefaultExemptCurrencyPairGroup() );

            // now set different exempt groups at provider level and cpty level.
            CurrencyPairGroup g7 = getCurrencyPairGroup( "G7" );
            CurrencyPairGroup usdAll = getCurrencyPairGroup( "USD/All" );

            creditAdminSvc.setCurrencyPairGroupExemption( lpOrg, g7 );
            creditAdminSvc.setCurrencyPairGroupExemption( lpOrg, lpTpForDd, usdAll );
            assertTrue( CreditUtilC.getExemptCurrencyPairGroup( cclr ).isSameAs( usdAll ) );

            creditAdminSvc.setUseDefaultExemptCurrencyPairGroup( lpOrg, lpTpForDd, true );
            assertTrue( CreditUtilC.getExemptCurrencyPairGroup( cclr ).isSameAs( g7 ) );
        }
        catch ( Exception e )
        {
            fail( "testSetCounterpartyLevelUseDefaultExemptCurrencyPairGroup", e );
        }
        finally
        {
            creditAdminSvc.setCurrencyPairGroupExemption( lpOrg, null );
            creditAdminSvc.setCurrencyPairGroupExemption( lpOrg, lpTpForDd, null );
            creditAdminSvc.setUseDefaultExemptCurrencyPairGroup( lpOrg, lpTpForDd, true );
        }
    }


    public void testSetDefaultCreditTenorProfile()
    {
        try
        {
            init( lpUser );

            String testProfile = "Test" + System.nanoTime();
            CreditTenorProfile tenorProfile = createCreditTenorProfile( testProfile );
            creditAdminSvc.setDefaultCreditTenorProfile( lpOrg, tenorProfile );
            CreditLimitRuleSet clrs = CreditUtilC.getCreditLimitRuleSet( lpOrg );
            assertTrue( clrs.getCreditTenorProfile().isSameAs( tenorProfile ) );
        }
        catch ( Exception e )
        {
            fail( "testSetDefaultCreditTenorProfile", e );
        }
        finally
        {
            creditAdminSvc.setDefaultCreditTenorProfile( lpOrg, null );
        }
    }

    public void testSetCounterpartyLevelCreditTenorProfile()
    {
        try
        {
            init( lpUser );

            String testProfile = "Test" + System.nanoTime();
            CreditTenorProfile tenorProfile = createCreditTenorProfile( testProfile );
            creditAdminSvc.setCreditTenorProfile( lpOrg, lpTpForFi, tenorProfile );
            CreditLimitRuleSet clrs = CreditUtilC.getCreditLimitRuleSet( lpOrg );

            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTpForFi );
            assertTrue( cclr.getCreditTenorProfile().isSameAs( tenorProfile ) );
        }
        catch ( Exception e )
        {
            fail( "testSetCounterpartyLevelCreditTenorProfile", e );
        }
        finally
        {
            creditAdminSvc.setDefaultCreditTenorProfile( lpOrg, null );
            creditAdminSvc.setCreditTenorProfile( lpOrg, lpTpForFi, null );
            creditAdminSvc.setUseDefaultTenorProfile( lpOrg, lpTpForFi, true );
        }
    }

    public void testSetCounterpartyLevelUseDefaultTenorProfile()
    {
        try
        {
            init( lpUser );
            Currency eur = CurrencyFactory.getCurrency( "EUR" );
            Currency usd = CurrencyFactory.getCurrency( "USD" );
            creditAdminSvc.setUseDefaultTenorProfile( lpOrg, lpTpForDd, false );
            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTpForDd );
            CreditLimitRule clr = ( CreditLimitRule ) cclr.getChildRule( CreditLimitConstants.GROSS_NOTIONAL_RULE_SHORT_NAME );
            assertFalse( cclr.isUseDefaultTenorProfile() );

            // now set different tenor profiles at provider level and cpty level.
            String orgLevelCtpName = "TestCPO" + System.nanoTime();
            CreditTenorProfile orgLevelCtp = createCreditTenorProfile( orgLevelCtpName );

            String cptyLevelCtpName = "TestCC" + System.nanoTime();
            CreditTenorProfile cptyLevelCtp = createCreditTenorProfile( cptyLevelCtpName );

            creditAdminSvc.setDefaultCreditTenorProfile( lpOrg, orgLevelCtp );
            creditAdminSvc.setCreditTenorProfile( lpOrg, lpTpForDd, cptyLevelCtp );
            assertTrue( CreditUtilC.getCreditTenorProfile( cclr, clr, eur, usd ).isSameAs( cptyLevelCtp ) );

            creditAdminSvc.setUseDefaultTenorProfile( lpOrg, lpTpForDd, true );
            assertTrue( CreditUtilC.getCreditTenorProfile( cclr, clr, eur, usd ).isSameAs( orgLevelCtp ) );
        }
        catch ( Exception e )
        {
            fail( "testSetCounterpartyLevelUseDefaultTenorProfile", e );
        }
        finally
        {
            creditAdminSvc.setDefaultCreditTenorProfile( lpOrg, null );
            creditAdminSvc.setCreditTenorProfile( lpOrg, lpTpForDd, null );
            creditAdminSvc.setUseDefaultTenorProfile( lpOrg, lpTpForDd, true );
        }
    }

    public void testExternalCreditLimitProviderChanges()
    {
        try
        {
            String externalCreditLimitProvider = "TraianaPing";
            init( lpUser );
            creditAdminSvc.setExternalCreditLimitProvider( lpOrg, lpTpForDd, externalCreditLimitProvider );

            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTpForDd );
            assertEquals( "externalCreditLimitProvider=" + cclr.getExternalCreditLimitProvider(), cclr.getExternalCreditLimitProvider(), externalCreditLimitProvider );

            String creditLimitProviderFromDB = creditAdminSvc.getExternalCreditLimitProvider( lpOrg, lpTpForDd );
            assertEquals( "creditLimitProviderFromDB=" + creditLimitProviderFromDB, creditLimitProviderFromDB, externalCreditLimitProvider );

            assertEquals( "checkSanity", sanityCheck( lpOrg ), true );
        }
        catch ( Exception e )
        {
            log.error( "testExternalCreditLimitProviderChanges", e );
            fail();
        }
    }

    public void testSetDefaultMode()
    {
        try
        {
            init( lpUser );

            creditAdminSvc.setDefaultMode( lpOrg, CreditLimit.CREDIT_INTEGRATION_MODE );
            CreditLimitRuleSet clrs = CreditUtilC.getCreditLimitRuleSet( lpOrg );
            assertTrue( clrs.getMode() == CreditLimit.CREDIT_INTEGRATION_MODE );
        }
        catch ( Exception e )
        {
            fail( "testSetDefaultMode", e );
        }
        finally
        {
            creditAdminSvc.setDefaultMode( lpOrg, CreditLimit.CREDIT_CARVE_OUT_MODE );
        }
    }

    public void testSetCounterpartyLevelMode()
    {
        try
        {
            init( lpUser );

            creditAdminSvc.setMode( lpOrg, lpTpForFi, CreditLimit.CREDIT_INTEGRATION_MODE );
            CreditLimitRuleSet clrs = CreditUtilC.getCreditLimitRuleSet( lpOrg );

            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTpForFi );
            assertTrue( cclr.getMode() == CreditLimit.CREDIT_INTEGRATION_MODE );
        }
        catch ( Exception e )
        {
            fail( "testSetCounterpartyLevelMode", e );
        }
        finally
        {
            creditAdminSvc.setDefaultMode( lpOrg, CreditLimit.CREDIT_CARVE_OUT_MODE );
            creditAdminSvc.setMode( lpOrg, lpTpForFi, CreditLimit.CREDIT_CARVE_OUT_MODE );
        }
    }

    public void testSetExcludePFEMode()
    {
        CreditLimitRuleSet clrs = CreditUtilC.getCreditLimitRuleSet( lpOrg );
        int defaultVal = clrs.getPFEExcludeForDailyExposure();
        try
        {
            init( lpUser );

            creditAdminSvc.setPFEExcludeForDailyExposure( lpOrg, CreditLimit.PFE_MODE_YES );
            clrs = CreditUtilC.getCreditLimitRuleSet( lpOrg );

            clrs = CreditUtilC.getCreditLimitRuleSet( lpOrg );
            assertTrue( clrs.getPFEExcludeForDailyExposure() == CreditLimit.PFE_MODE_YES );
        }
        catch ( Exception e )
        {
            fail( "testSetCounterpartyLevelExcludePFEMode", e );
        }
        finally
        {
            creditAdminSvc.setPFEExcludeForDailyExposure( lpOrg, defaultVal );
        }
    }

    public void testSetCounterpartyLevelExcludePFEMode()
    {
        try
        {
            init( lpUser );

            creditAdminSvc.setPFEExcludeForDailyExposure( lpOrg, lpTpForFi, CreditLimit.PFE_MODE_YES );
            CreditLimitRuleSet clrs = CreditUtilC.getCreditLimitRuleSet( lpOrg );

            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTpForFi );
            assertTrue( cclr.getPFEExcludeForDailyExposure() == CreditLimit.PFE_MODE_YES );
        }
        catch ( Exception e )
        {
            fail( "testSetCounterpartyLevelExcludePFEMode", e );
        }
        finally
        {
            creditAdminSvc.setPFEExcludeForDailyExposure( lpOrg, lpTpForFi, CreditLimit.PFE_MODE_DEFAULT );
        }
    }

    public void testSetCounterpartyLevelUseDefaultMode()
    {
        try
        {
            init( lpUser );
            creditAdminSvc.setUseDefaultMode( lpOrg, lpTpForDd, false );
            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTpForDd );
            assertFalse( cclr.isUseDefaultMode() );

            // now set different modes at provider level and cpty level.
            creditAdminSvc.setDefaultMode( lpOrg, CreditLimit.CREDIT_CARVE_OUT_MODE );
            creditAdminSvc.setMode( lpOrg, lpTpForDd, CreditLimit.CREDIT_INTEGRATION_MODE );
            assertTrue( CreditUtilC.getCreditMode( cclr ) == CreditLimit.CREDIT_INTEGRATION_MODE );

            creditAdminSvc.setUseDefaultMode( lpOrg, lpTpForDd, true );
            assertTrue( CreditUtilC.getCreditMode( cclr ) == CreditLimit.CREDIT_CARVE_OUT_MODE );
        }
        catch ( Exception e )
        {
            fail( "testSetCounterpartyLevelUseDefaultMode", e );
        }
        finally
        {
            creditAdminSvc.setDefaultMode( lpOrg, CreditLimit.CREDIT_CARVE_OUT_MODE );
            creditAdminSvc.setMode( lpOrg, lpTpForDd, CreditLimit.CREDIT_CARVE_OUT_MODE );
            creditAdminSvc.setUseDefaultMode( lpOrg, lpTpForDd, true );
        }
    }

    public void testSetLETenorOverride()
    {
        init( lpUser );
        try
        {
            creditAdminSvc.setLEOverride(lpOrg, fiOrg, true);
            assertTrue(CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty(lpOrg, lpTpForFi).isLeOverride());
        }
        catch (Exception e)
        {
            fail("testSetLETenorOverride", e);
        }
        finally
        {
            creditAdminSvc.setLEOverride(lpOrg, fiOrg, false);
        }
    }


    public void testSetLETenorOrgDefault()
    {
        init( lpUser );
        try
        {
            creditAdminSvc.setOrganizationExposureLevel(lpOrg, fiOrg);
            creditAdminSvc.setOrgDefault(lpOrg, fiOrg, lpTpForFi, false);
            assertFalse(CreditUtilC.getTradingPartyLevelCounterpartyCreditLimitRule(lpOrg, lpTpForFi).isOrgDefault());
        }
        catch (Exception e)
        {
            fail("testSetLETenorOrgDefault", e);
        }
        finally
        {
            creditAdminSvc.setOrgDefault(lpOrg, fiOrg, lpTpForFi, true);
            creditAdminSvc.setLegalEntityExposureLevel(lpOrg, fiOrg);
        }
    }

    public void testSetTenorRestrictionInBusinessDays()
    {
        init( lpUser );
        try
        {
            creditAdminSvc.setTenorRestrictionInBusinessDays( lpOrg, lpTpForFi, true);
            assertTrue(CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty(lpOrg, lpTpForFi).isTenorRestrictionInBusinessDays() );
            assertTrue( creditAdminSvc.isTenorRestrictionInBusinessDays( lpOrg, lpTpForFi ));
        }
        catch (Exception e)
        {
            fail("testSetTenorRestrictionInBusinessDays", e);
        }
        finally
        {
            creditAdminSvc.setTenorRestrictionInBusinessDays(lpOrg, lpTpForFi, false);
        }
    }

    public void testTenorRestrictionInBusinessDaysAtOrgLevelExposure()
    {
        creditAdminSvc.setOrganizationExposureLevel(lpOrg, ddOrg);
        creditAdminSvc.setTenorRestrictionInBusinessDays( lpOrg, lpTpForDd, true );
        assertTrue( CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTpForDd ).isTenorRestrictionInBusinessDays() );
        assertTrue( creditAdminSvc.isTenorRestrictionInBusinessDays( lpOrg, lpTpForDd ));

        try
        {
            Collection<TradingParty> tps = CounterpartyUtilC.getTradingParties( lpOrg, ddOrg );
            for ( TradingParty tp: tps )
            {
                creditAdminSvc.setCptyLETenorRestrictionInBusinessDays( lpOrg, tp, true );
                assertTrue( creditAdminSvc.isCptyLETenorRestrictionInBusinessDays( lpOrg, tp ) );
            }
        }
        catch ( Exception e )
        {
            fail ( "testTenorRestrictionInBusinessDaysAtOrgLevelExposure", e );
        }
        finally
        {
            creditAdminSvc.setLegalEntityExposureLevel( lpOrg, ddOrg );
            Collection<TradingParty> tps = CounterpartyUtilC.getTradingParties( lpOrg, ddOrg );
            for ( TradingParty tp: tps )
            {
                creditAdminSvc.setCptyLETenorRestrictionInBusinessDays( lpOrg, tp, false );
            }
        }
    }


    protected CurrencyPairGroup getCurrencyPairGroup( String name )
    {
        CurrencyPairGroup cpg = new MockCurrencyPairGroup();
        cpg.setShortName( name );
        cpg.addCcyPair( "EUR/USD" );
        cpg.addCcyPair( "EUR/GBP" );
        cpg.addCcyPair( "USD/JPY" );
        return cpg;
    }

}


