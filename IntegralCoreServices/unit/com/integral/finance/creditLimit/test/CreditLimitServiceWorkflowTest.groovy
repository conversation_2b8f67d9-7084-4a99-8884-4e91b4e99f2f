package com.integral.finance.creditLimit.test

import com.integral.finance.creditLimit.*
import com.integral.finance.creditLimit.functor.CreditUtilizationCacheSynchronizeNotificationFunctorC
import com.integral.finance.currency.Currency
import com.integral.finance.currency.CurrencyFactory
import com.integral.finance.currency.CurrencyPair
import com.integral.finance.fx.FXSingleLeg
import com.integral.finance.instrument.AmountOfInstrument

// Copyright (c) 2005 Integral Development Corporation.  All Rights Reserved.
import com.integral.finance.marketData.fx.FXMarketDataElement
import com.integral.finance.marketData.fx.FXMarketDataSet
import com.integral.finance.price.fx.FXPrice
import com.integral.finance.trade.Tenor
import com.integral.finance.trade.Trade
import com.integral.message.ErrorMessage
import com.integral.message.MessageStatus
import com.integral.session.IdcTransaction
import com.integral.session.RemoteTransactionNotification
import com.integral.time.IdcDate
import com.integral.workflow.dealing.DealingLimitCollection

/**
 * Tests the credit limit service workflows.
 *
 * <AUTHOR> Development Corp.
 */
class CreditLimitServiceWorkflowTest extends CreditLimitServiceBaseTestCase
{
    void testMinTenorValidationSuccess()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )
            removeExistingCreditUtilizationEvents( fiOrg )

            creditAdminSvc.setCreditLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, ********, CreditUtilC.getCounterpartyCreditLimitCurrency( lpOrg, fiOrg, lpTpForFi ) )
            creditAdminSvc.setCreditLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, 20000000, CreditUtilC.getCounterpartyCreditLimitCurrency( lpOrg, fiOrg, lpTpForFi ) )
            // set the minTenor limit to 5 days and value date 10 days
            creditAdminSvc.setMinimumTenor( lpOrg, lpTpForFi, new Tenor( "5D" ) )

            double limitBefore = getAvailableCreditLimit( lpTpForFi, spotDate.addDays( 10 ) )
            log( "credit limit before take Credit: " + limitBefore )
            double tradeAmt = 10000
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate.addDays( 10 ) )
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( fiLe, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS )

            double limitAfter = getAvailableCreditLimit( lpTpForFi, spotDate.addDays( 10 ) )
            log( "credit limit after take Credit: " + limitAfter )

            assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS )
            assertEquals( "The credit limit should change.", true, Math.abs( limitBefore - limitAfter - tradeAmt ) < MINIMUM )
        }
        catch ( Exception e )
        {
            fail( "testMinTenorValidationSuccess", e )
        }
        finally
        {
            creditAdminSvc.setMinimumTenor( lpOrg, lpTpForFi, null )
        }
    }

    void testMaxTenorValidationSuccess()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )
            removeExistingCreditUtilizationEvents( fiOrg )

            creditAdminSvc.setCreditLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, ********, CreditUtilC.getCounterpartyCreditLimitCurrency( lpOrg, fiOrg, lpTpForFi ) )
            creditAdminSvc.setCreditLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, 20000000, CreditUtilC.getCounterpartyCreditLimitCurrency( lpOrg, fiOrg, lpTpForFi ) )
            // set the maxTenor limit to 10 days and value date 10 days
            Tenor maxTenor = new Tenor( "10D" )
            creditAdminSvc.setMaximumTenor( lpOrg, lpTpForFi, maxTenor )

            // calculate the value date based on the maximum tenor.
            IdcDate maxDate = stdQuoteConv.getFXRateBasis( "EUR", "USD" ).getValueDate( tradeDate, maxTenor )

            double limitBefore = getAvailableCreditLimit( lpTpForFi, maxDate )
            log( "credit limit before take Credit: " + limitBefore )
            double tradeAmt = 10000
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], maxDate )
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( fiLe, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS )

            double limitAfter = getAvailableCreditLimit( lpTpForFi, maxDate )
            log( "credit limit after take Credit: " + limitAfter )

            assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS )
            assertEquals( "The credit limit should change.", true, Math.abs( limitBefore - limitAfter - tradeAmt ) < MINIMUM )
        }
        catch ( Exception e )
        {
            fail( "testMaxTenorValidationSuccess", e )
        }
        finally
        {
            creditAdminSvc.setMaximumTenor( lpOrg, lpTpForFi, null )
        }
    }

    void testMultiCreditRuleTakeCredit()
    {
        try
        {
            init( fiUser )
            removeExistingCreditUtilizationEvents( fiOrg )
            removeExistingCreditUtilizationEvents( cptyOrg )
            double limit0 = getAvailableCreditLimit( fiTpForCpty, spotDate )
            log( "testMultiCreditRuleTakeCredit : credit limit  before take Credit : " + limit0 )
            double tradeAmt = 1000
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, fiOrg, fiTpForCpty, bidRates[0], spotDate )
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, fiLe, cptyOrg, fiTpForCpty )
            double limit1 = getAvailableCreditLimit( fiTpForCpty, spotDate )
            log( "testMultiCreditRuleTakeCredit : credit limit  after take Credit : " + limit1 + " success : " + cwm.getStatus() )
            assertEquals( "success=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS )
            assertEquals( "testMultiCreditRuleTakeCredit : 1000 was taken :", true, Math.abs( limit0 - limit1 - tradeAmt ) < MINIMUM )
            validateCreditUtilizationEvents( cwm )
        }
        catch ( Exception e )
        {
            slog.error( "testMultiCreditRuleTakeCredit", e )
            fail()
        }
    }

    void testMultiCreditRuleTakeCreditFailure_Insufficient_Credit()
    {
        try
        {
            init( fiUser )
            removeExistingCreditUtilizationEvents( fiOrg )
            removeExistingCreditUtilizationEvents( cptyOrg )
            double limit0 = getAvailableCreditLimit( fiTpForCpty, spotDate )
            log( "testMultiCreditRuleTakeCreditFailure_Insufficient_Credit : credit limit  before take Credit : " + limit0 )
            Double suspensionPercent = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( fiOrg, fiTpForCpty ).getSuspensionPercentage()
            if ( suspensionPercent == null )
            {
                creditAdminSvc.setSuspensionPercentage( fiOrg, fiTpForCpty, 98.0 )
                suspensionPercent = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( fiOrg, fiTpForCpty ).getSuspensionPercentage()
            }
            Double totalLimit = getTotalCreditLimit( fiOrg, fiTpForCpty )
            Double bufferLimit = totalLimit * ( 1 - suspensionPercent / 100 )
            double tradeAmt = limit0 + ( bufferLimit + 200 )
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, fiOrg, fiTpForCpty, bidRates[0], spotDate )
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, fiLe, cptyOrg, fiTpForCpty )
            double limit1 = getAvailableCreditLimit( fiTpForCpty, spotDate )
            log( "testMultiCreditRuleTakeCreditFailure_Insufficient_Credit : credit limit  after take Credit : " + limit1 + " success : " + cwm.getStatus() )
            assertEquals( "testMultiCreditRuleTakeCreditFailure_Insufficient_Credit : 1000 was taken :", true, Math.abs( limit0 - limit1 ) < MINIMUM )
            assertEquals( "testMultiCreditRuleTakeCreditFailure_Insufficient_Credit : status :  ", cwm.getStatus(), MessageStatus.FAILURE )
            validateCreditUtilizationEvents( cwm )
        }
        catch ( Exception e )
        {
            slog.error( "testMultiCreditRuleTakeCreditFailure_Insufficient_Credit", e )
            fail()
        }
    }

    void testMultiCreditRuleTakeCredit_Exceed_SuspensionPercent()
    {
        try
        {
            init( fiUser )
            removeExistingCreditUtilizationEvents( fiOrg )
            removeExistingCreditUtilizationEvents( cptyOrg )
            double limit0 = getAvailableCreditLimit( fiTpForCpty, spotDate )
            log( "testMultiCreditRuleTakeCreditFailure_Insufficient_Credit : credit limit  before take Credit : " + limit0 )
            Double suspensionPercent = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( fiOrg, fiTpForCpty ).getSuspensionPercentage()
            if ( suspensionPercent == null )
            {
                creditAdminSvc.setSuspensionPercentage( fiOrg, fiTpForCpty, 98.0 )
                suspensionPercent = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( fiOrg, fiTpForCpty ).getSuspensionPercentage()
            }
            double totalLimit = getTotalCreditLimit( fiOrg, fiTpForCpty )
            Double bufferLimit = totalLimit * ( 1 - suspensionPercent / 100 )
            double tradeAmt = limit0 + ( bufferLimit )
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, fiOrg, fiTpForCpty, bidRates[0], spotDate )
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, fiLe, cptyOrg, fiTpForCpty )
            double limit1 = getAvailableCreditLimit( fiTpForCpty, spotDate )
            log( "testMultiCreditRuleTakeCredit_Exceed_SuspensionPercent : credit limit  after take Credit : " + limit1 + " success : " + cwm.getStatus() )
            assertEquals( "testMultiCreditRuleTakeCredit_Exceed_SuspensionPercent : 1000 was taken :", true, Math.abs( limit0 - limit1 - totalLimit ) < MINIMUM )
            assertEquals( "testMultiCreditRuleTakeCredit_Exceed_SuspensionPercent : status :  ", cwm.getStatus(), MessageStatus.SUCCESS )
            validateCreditUtilizationEvents( cwm )
        }
        catch ( Exception e )
        {
            slog.error( "testMultiCreditRuleTakeCredit_Exceed_SuspensionPercent", e )
            fail()
        }
    }

    void testMultiCreditRuleTakeCreditFailure_No_FXRate()
    {
        FXMarketDataElement fxMde = null
        try
        {
            init( fiUser )
            removeExistingCreditUtilizationEvents( fiOrg )
            removeExistingCreditUtilizationEvents( cptyOrg )
            CurrencyPair ccyPair = ( CurrencyPair ) CurrencyFactory.getCurrencyPairFromString( "EUR/USD" )
            removeExistingCreditUtilizationEvents( fiOrg )

            double limit0 = getAvailableCreditLimit( fiTpForCpty, spotDate )
            log( "testMultiCreditRuleTakeCreditFailure_No_FXRate : credit limit  before take Credit : " + limit0 )
            double tradeAmt = 1000
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, ccyPair.getName(), fiOrg, fiTpForCpty, bidRates[0], spotDate )

            // remove the rate from mds
            fxMde = staticMds.findSpotConversionMarketDataElement( ccyPair.getBaseCurrency(), ccyPair.getVariableCurrency(), true )
            if ( fxMde != null )
            {
                staticMds.removeMarketDataElement( fxMde )
            }

            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, fiLe, cptyOrg, fiTpForCpty )
            double limit1 = getAvailableCreditLimit( fiTpForCpty, spotDate )
            log( "testMultiCreditRuleTakeCreditFailure_No_FXRate : credit limit  after take Credit : " + limit1 + " success : " + cwm.getStatus() )
            assertEquals( "testMultiCreditRuleTakeCreditFailure_No_FXRate : 1000 was taken :", true, Math.abs( limit0 - limit1 ) < MINIMUM )
            assertEquals( "testMultiCreditRuleTakeCreditFailure_No_FXRate : status :  ", cwm.getStatus(), MessageStatus.FAILURE )
            assertEquals( CreditLimit.ERROR_CONVERSION_RATE_UNAVAILABLE, cwm.getErrorCode() )
            validateCreditUtilizationEvents( cwm )
        }
        catch ( Exception e )
        {
            slog.error( "testMultiCreditRuleTakeCreditFailure_No_FXRate", e )
            fail()
        }
        finally
        {
            if ( fxMde != null )
            {
                staticMds.addMarketDataElement( fxMde )
            }
        }
    }

    void testMultiCreditRuleTakeCreditFailure_No_CreditUtilization()
    {
        try
        {
            init( fiUser )
            removeExistingCreditUtilizationEvents( fiOrg )
            removeExistingCreditUtilizationEvents( cptyOrg )
            double limit0 = getAvailableCreditLimit( fiTpForCpty, spotDate )
            log( "testMultiCreditRuleTakeCreditFailure_No_CreditUtilization : credit limit  before take Credit : " + limit0 )
            double tradeAmt = 1000

            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, fiOrg, fiTpForCpty, bidRates[0], spotDate.addYears( 11 ) )
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, fiLe, cptyOrg, fiTpForCpty )
            double limit1 = getAvailableCreditLimit( fiTpForCpty, spotDate )
            log( "testMultiCreditRuleTakeCreditFailure_No_CreditUtilization : credit limit  after take Credit : " + limit1 + " success : " + cwm.getStatus() )
            assertEquals( "testMultiCreditRuleTakeCreditFailure_No_CreditUtilization : 1000 was taken :", true, Math.abs( limit0 - limit1 ) < MINIMUM )
            assertEquals( "testMultiCreditRuleTakeCreditFailure_No_CreditUtilization : status :  ", cwm.getStatus(), MessageStatus.FAILURE )
            validateCreditUtilizationEvents( cwm )
            assertEquals( CreditLimit.ERROR_CREDIT_UTILIZATION_UNAVAILABLE, cwm.getErrorCode() )
        }
        catch ( Exception e )
        {
            slog.error( "testMultiCreditRuleTakeCreditFailure_No_CreditUtilization", e )
            fail()
        }
    }

    void testBilateralTakeCreditFailure_No_CreditUtilization()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )
            removeExistingCreditUtilizationEvents( fiOrg )

            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 )
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 )
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 )
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 )

            double dailyFILimit0 = getAvailableCreditLimit( lpTpForFi, spotDate, DAILY_SETTLEMENT_CLASSIFICATION )
            double dailyLPLimit0 = getAvailableCreditLimit( fiTpForLp, spotDate, DAILY_SETTLEMENT_CLASSIFICATION )
            double aggregateFILimit0 = getAvailableCreditLimit( lpTpForFi, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            double aggregateLPLimit0 = getAvailableCreditLimit( fiTpForLp, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "daily FI credit limit  before take Credit : " + dailyFILimit0 )
            log( "daily LP credit limit  before take Credit : " + dailyLPLimit0 )
            log( "aggregate FI credit limit  before take Credit : " + aggregateFILimit0 )
            log( "aggregate LP credit limit  before take Credit : " + aggregateLPLimit0 )
            double tradeAmt = 1000
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate.addYears( 11 ) )
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( fiLe, cptyLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS )
            double dailyFILimit1 = getAvailableCreditLimit( lpTpForFi, spotDate, DAILY_SETTLEMENT_CLASSIFICATION )
            double dailyLPLimit1 = getAvailableCreditLimit( fiTpForLp, spotDate, DAILY_SETTLEMENT_CLASSIFICATION )
            double aggregateFILimit1 = getAvailableCreditLimit( lpTpForFi, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            double aggregateLPLimit1 = getAvailableCreditLimit( fiTpForLp, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            log( "daily FI credit limit  after take Credit : " + dailyFILimit1 + " success : " + cwm.getStatus() )
            log( "daily LP credit limit  after take Credit : " + dailyLPLimit1 + " success : " + cwm.getStatus() )
            log( "aggregate FI credit limit  after take Credit : " + aggregateFILimit1 + " success : " + cwm.getStatus() )
            log( "aggregate LP credit limit  after take Credit : " + aggregateLPLimit1 + " success : " + cwm.getStatus() )
            assertEquals( "Limit should not be different", true, Math.abs( dailyFILimit0 - dailyFILimit1 ) < MINIMUM )
            assertEquals( "Limit should not be different", true, Math.abs( dailyLPLimit0 - dailyLPLimit1 ) < MINIMUM )
            assertEquals( "Limit should not be different", true, Math.abs( aggregateFILimit0 - aggregateFILimit1 ) < MINIMUM )
            assertEquals( "Limit should not be different", true, Math.abs( aggregateLPLimit0 - aggregateLPLimit1 ) < MINIMUM )
            assertEquals( "staus should be failure=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.FAILURE )
            validateCreditUtilizationEvents( cwm )
        }
        catch ( Exception e )
        {
            e.printStackTrace()
            fail()
        }
    }

    void testMultiCreditRuleTakeCreditFailure_DailyInsufficientCredit()
    {
        try
        {
            init( fiUser )
            removeExistingCreditUtilizationEvents( cptyOrg )
            removeExistingCreditUtilizationEvents( fiOrg )
            double dailyLimit = getDailyAvailableCreditLimit( fiTpForCpty, spotDate )
            double grossLimit = getGrossNotionalAvailableCreditLimit( fiTpForCpty, spotDate )
            log( "testMultiCreditRuleTakeCreditFailure_DailyInsufficientCredit :  daily credit limit  before take Credit : " + dailyLimit + " gross limit : " + grossLimit )
            Double suspensionPercent = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( fiOrg, fiTpForCpty ).getSuspensionPercentage()
            if ( suspensionPercent == null )
            {
                creditAdminSvc.setSuspensionPercentage( fiOrg, fiTpForCpty, 98.0 )
            }
            Double totalLimit = getTotalDailyCreditLimit( fiTpForCpty, spotDate )
            Double bufferLimit = totalLimit * ( 1 - suspensionPercent / 100 )
            double tradeAmt = dailyLimit + ( bufferLimit + 200 )
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, fiOrg, fiTpForCpty, bidRates[0], spotDate )
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, fiLe, cptyOrg, fiTpForCpty )
            double dailylimit1 = getDailyAvailableCreditLimit( fiTpForCpty, spotDate )
            double grossLimit1 = getGrossNotionalAvailableCreditLimit( fiTpForCpty, spotDate )
            log( "testMultiCreditRuleTakeCreditFailure_DailyInsufficientCredit : daily credit limit  after take Credit : " + dailylimit1 + " gross limit : " + grossLimit1 + " success : " + cwm.getStatus() )
            assertEquals( "testMultiCreditRuleTakeCreditFailure_DailyInsufficientCredit : gross limit is unchanged :", true, Math.abs( grossLimit1 - grossLimit ) < MINIMUM )
            assertEquals( "testMultiCreditRuleTakeCreditFailure_DailyInsufficientCredit : daily limit is unchanged :", true, Math.abs( dailylimit1 - dailyLimit ) < MINIMUM )
            assertEquals( "testMultiCreditRuleTakeCreditFailure_DailyInsufficientCredit : status :  ", cwm.getStatus(), MessageStatus.FAILURE )
            validateCreditUtilizationEvents( cwm )
        }
        catch ( Exception e )
        {
            slog.error( "testMultiCreditRuleTakeCreditFailure_DailyInsufficientCredit", e )
            fail()
        }
    }

    void testCreditLimits()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )
            removeExistingCreditUtilizationEvents( ddOrg )
            double grossLimit = getGrossNotionalAvailableCreditLimit( lpTpForDd, spotDate )
            log( "******** grossLimit before any trade=" + grossLimit )

            double tradeAmt = 2000
            Trade trade = prepareSingleLegTrade( tradeAmt, false, true, EURUSD, lpOrg, lpTpForDd, 1.3028, spotDate )
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd )

            CreditUtilization cu = cwm.getCreditUtilizationEvents().iterator().next().getCreditUtilization()
            CurrencyPositionCollection positions = cu.getCurrencyPositions()
            double[] netAmounts = positions.getNetAmounts( cu.getCurrency(), ( FXMarketDataSet ) ( ( ( CreditLimitRuleSet ) cu.getCreditLimitRule().getParentRule().getRuleSet() ).getMarketDataSet() ), new HashMap<String, FXPrice>() )
            double pandLNetReceivable = netAmounts[CurrencyPositionCollection.PANDL_NET_RECEIVABLE]
            double pandLNetPayable = netAmounts[CurrencyPositionCollection.PANDL_NET_PAYABLE]
            double profitAndLoss = positions.getProfitAndLoss( pandLNetReceivable, pandLNetPayable, true )
            log( "************ Used Amount=" + cu.getUsedAmount() )
            log( "************ Utilized Percentage=" + cu.getUtilizedPercentage() )
            log( "************ P/L=" + profitAndLoss )
            log( "************ Available=" + cu.getAvailableMarginReserveAmount() )

            tradeAmt = 1000
            trade = prepareSingleLegTrade( tradeAmt, true, true, EURUSD, lpOrg, lpTpForDd, 1.3032, spotDate )
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd )

            cu = cwm1.getCreditUtilizationEvents().iterator().next().getCreditUtilization()
            positions = cu.getCurrencyPositions()
            netAmounts = positions.getNetAmounts( cu.getCurrency(), ( FXMarketDataSet ) ( ( ( CreditLimitRuleSet ) cu.getCreditLimitRule().getParentRule().getRuleSet() ).getMarketDataSet() ), new HashMap<String, FXPrice>() )
            pandLNetReceivable = netAmounts[CurrencyPositionCollection.PANDL_NET_RECEIVABLE]
            pandLNetPayable = netAmounts[CurrencyPositionCollection.PANDL_NET_PAYABLE]
            profitAndLoss = positions.getProfitAndLoss( pandLNetReceivable, pandLNetPayable, true )
            log( "************ Used Amount=" + cu.getUsedAmount() )
            log( "************ Utilized Percentage=" + cu.getUtilizedPercentage() )
            log( "************ P/L=" + profitAndLoss )
            log( "************ Available=" + cu.getAvailableMarginReserveAmount() )

            tradeAmt = 2000
            trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, 1.3028, spotDate )
            CreditWorkflowMessage cwm2 = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd )

            cu = cwm2.getCreditUtilizationEvents().iterator().next().getCreditUtilization()
            positions = cu.getCurrencyPositions()
            netAmounts = positions.getNetAmounts( cu.getCurrency(), ( FXMarketDataSet ) ( ( ( CreditLimitRuleSet ) cu.getCreditLimitRule().getParentRule().getRuleSet() ).getMarketDataSet() ), new HashMap<String, FXPrice>() )
            pandLNetReceivable = netAmounts[CurrencyPositionCollection.PANDL_NET_RECEIVABLE]
            pandLNetPayable = netAmounts[CurrencyPositionCollection.PANDL_NET_PAYABLE]
            profitAndLoss = positions.getProfitAndLoss( pandLNetReceivable, pandLNetPayable, true )
            log( "************ Used Amount=" + cu.getUsedAmount() )
            log( "************ Utilized Percentage=" + cu.getUtilizedPercentage() )
            log( "************ P/L=" + profitAndLoss )
            log( "************ Available=" + cu.getAvailableMarginReserveAmount() )


            tradeAmt = 4000
            trade = prepareSingleLegTrade( tradeAmt, true, false, EURGBP, lpOrg, lpTpForDd, 0.6977, spotDate )
            CreditWorkflowMessage cwm3 = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd )

            cu = cwm3.getCreditUtilizationEvents().iterator().next().getCreditUtilization()
            positions = cu.getCurrencyPositions()
            netAmounts = positions.getNetAmounts( cu.getCurrency(), ( FXMarketDataSet ) ( ( ( CreditLimitRuleSet ) cu.getCreditLimitRule().getParentRule().getRuleSet() ).getMarketDataSet() ), new HashMap<String, FXPrice>() )
            pandLNetReceivable = netAmounts[CurrencyPositionCollection.PANDL_NET_RECEIVABLE]
            pandLNetPayable = netAmounts[CurrencyPositionCollection.PANDL_NET_PAYABLE]
            profitAndLoss = positions.getProfitAndLoss( pandLNetReceivable, pandLNetPayable, true )
            log( "************ Used Amount=" + cu.getUsedAmount() )
            log( "************ Utilized Percentage=" + cu.getUtilizedPercentage() )
            log( "************ P/L=" + profitAndLoss )
            log( "************ Available=" + cu.getAvailableMarginReserveAmount() )

            tradeAmt = 4000
            trade = prepareSingleLegTrade( tradeAmt, false, false, EURGBP, lpOrg, lpTpForDd, 0.698, spotDate )
            CreditWorkflowMessage cwm4 = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd )

            cu = cwm4.getCreditUtilizationEvents().iterator().next().getCreditUtilization()
            positions = cu.getCurrencyPositions()
            netAmounts = positions.getNetAmounts( cu.getCurrency(), ( FXMarketDataSet ) ( ( ( CreditLimitRuleSet ) cu.getCreditLimitRule().getParentRule().getRuleSet() ).getMarketDataSet() ), new HashMap<String, FXPrice>() )
            pandLNetReceivable = netAmounts[CurrencyPositionCollection.PANDL_NET_RECEIVABLE]
            pandLNetPayable = netAmounts[CurrencyPositionCollection.PANDL_NET_PAYABLE]
            profitAndLoss = positions.getProfitAndLoss( pandLNetReceivable, pandLNetPayable, true )
            log( "************ Used Amount=" + cu.getUsedAmount() )
            log( "************ Utilized Percentage=" + cu.getUtilizedPercentage() )
            log( "************ P/L=" + profitAndLoss )
            log( "************ Available=" + cu.getAvailableMarginReserveAmount() )
        }
        catch ( Exception e )
        {
            fail( "testCreditLimits", e )
        }
    }


    void testMultiCreditRuleTakeCreditFailure_GrossInsufficientCredit()
    {
        try
        {
            init( fiUser )
            removeExistingCreditUtilizationEvents( cptyOrg )
            removeExistingCreditUtilizationEvents( fiOrg )
            setCalcAndLimit( fiOrg, fiTpForCpty, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 2000000 )
            setCalcAndLimit( fiOrg, fiTpForCpty, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 )
            double dailyLimit = getDailyAvailableCreditLimit( fiTpForCpty, spotDate )
            double grossLimit = getGrossNotionalAvailableCreditLimit( fiTpForCpty, spotDate )
            log( "testMultiCreditRuleTakeCreditFailure_GrossInsufficientCredit :  daily credit limit  before take Credit : " + dailyLimit + " gross limit : " + grossLimit )

            // remove the daily limit for spot date.
            Trade trade1 = prepareSingleLegTrade( dailyLimit, true, false, EURUSD, fiOrg, fiTpForCpty, bidRates[0], spotDate )
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, fiLe, cptyOrg, fiTpForCpty )
            double dailylimit1 = getDailyAvailableCreditLimit( fiTpForCpty, spotDate )
            double grossLimit1 = getGrossNotionalAvailableCreditLimit( fiTpForCpty, spotDate )
            log( "testMultiCreditRuleTakeCreditFailure_GrossInsufficientCredit : daily credit limit after take Credit : " + dailylimit1 + " gross limit : " + grossLimit1 + " success : " + cwm1.getStatus() )

            // remove the daily limit for spot next date.
            double spotNextDailyAvailable = getDailyAvailableCreditLimit( fiTpForCpty, spotDate.addDays( 1 ) )
            log( "testMultiCreditRuleTakeCreditFailure_GrossInsufficientCredit :  on spot+1 day daily credit limit  before take Credit : " + spotNextDailyAvailable + " gross limit : " + grossLimit1 )
            Trade trade2 = prepareSingleLegTrade( spotNextDailyAvailable, true, false, EURUSD, fiOrg, fiTpForCpty, bidRates[0], spotDate.addDays( 1 ) )
            CreditWorkflowMessage cwm2 = creditMgr.takeCredit( trade2, fiLe, cptyOrg, fiTpForCpty )
            double dailylimit2 = getDailyAvailableCreditLimit( fiTpForCpty, spotDate.addDays( 1 ) )
            double grossLimit2 = getGrossNotionalAvailableCreditLimit( fiTpForCpty, spotDate.addDays( 1 ) )
            log( "testMultiCreditRuleTakeCreditFailure_GrossInsufficientCredit : spot + 1 daily credit limit  after take Credit : " + dailylimit2 + " gross limit : " + grossLimit2 + " success : " + cwm2.getStatus() )

            // try to do a trade with spot date + 2
            double spot2DailyLimit = getDailyAvailableCreditLimit( fiTpForCpty, spotDate.addDays( 2 ) )
            log( "testMultiCreditRuleTakeCreditFailure_GrossInsufficientCredit :  on spot+2 day daily credit limit  before take Credit : " + spot2DailyLimit + " gross limit : " + grossLimit2 )
            Trade trade3 = prepareSingleLegTrade( spot2DailyLimit, true, false, EURUSD, fiOrg, fiTpForCpty, bidRates[0], spotDate.addDays( 2 ) )
            CreditWorkflowMessage cwm3 = creditMgr.takeCredit( trade3, fiLe, cptyOrg, fiTpForCpty )
            double dailylimit3 = getDailyAvailableCreditLimit( fiTpForCpty, spotDate.addDays( 2 ) )
            double grossLimit3 = getGrossNotionalAvailableCreditLimit( fiTpForCpty, spotDate.addDays( 2 ) )
            log( "testMultiCreditRuleTakeCreditFailure_GrossInsufficientCredit : spot + 2 daily credit limit  after take Credit : " + dailylimit3 + " gross limit : " + grossLimit3 + " success : " + cwm3.getStatus() )

            assertEquals( "testMultiCreditRuleTakeCreditFailure_GrossInsufficientCredit : gross limit is unchanged :", true, Math.abs( grossLimit3 - grossLimit2 ) < MINIMUM )
            assertEquals( "testMultiCreditRuleTakeCreditFailure_GrossInsufficientCredit : daily limit is unchanged :", true, Math.abs( dailylimit3 - spot2DailyLimit ) < MINIMUM )
            assertEquals( "testMultiCreditRuleTakeCreditFailure_GrossInsufficientCredit : status :  ", cwm3.getStatus(), MessageStatus.FAILURE )
            validateCreditUtilizationEvents( cwm3 )
        }
        catch ( Exception e )
        {
            slog.error( "testMultiCreditRuleTakeCreditFailure_GrossInsufficientCredit", e )
            fail()
        }
    }

    void testMultiCreditUndoCredit()
    {

    }

    void testMultiCreditUndoCreditFailure()
    {

    }

    void testMultiCreditUpdateCredit()
    {

    }

    void testMultiCreditCheckCredit()
    {

    }

    void testMultiCreditCheckCreditFailure()
    {

    }

    void testMultiCreditReserve()
    {

    }

    void testMultiCreditReserveFailure()
    {

    }

    void testMultiCreditUndoReserve()
    {

    }

    void testMultiCreditRuleTakeCreditExternalTx()
    {
        try
        {
            init( fiUser )
            IdcTransaction tx = initTransaction( true )
            testMultiCreditRuleTakeCredit()
            commitTransaction( tx )
        }
        catch ( Exception e )
        {
            slog.error( "testMultiCreditRuleTakeCredit", e )
            fail()
        }
    }

    void testMultiCreditRuleTakeCreditFailure_No_FXRateExternalTx()
    {
        try
        {
            init( fiUser )
            IdcTransaction tx = initTransaction( true )
            testMultiCreditRuleTakeCreditFailure_No_FXRate()
            commitTransaction( tx )
        }
        catch ( Exception e )
        {
            slog.error( "testMultiCreditRuleTakeCreditFailure_No_FXRate", e )
            fail()
        }
    }

    void testMultiCreditRuleTakeCreditFailure_No_CreditUtilizationExternalTx()
    {
        try
        {
            init( fiUser )
            IdcTransaction tx = initTransaction( true )
            testMultiCreditRuleTakeCreditFailure_No_CreditUtilization()
            commitTransaction( tx )
        }
        catch ( Exception e )
        {
            slog.error( "testMultiCreditRuleTakeCreditFailure_No_CreditUtilization", e )
            fail()
        }
    }

    void testMultiCreditRuleTakeCreditFailure_DailyInsufficientCreditExternalTx()
    {
        try
        {
            init( fiUser )
            IdcTransaction tx = initTransaction( true )
            testMultiCreditRuleTakeCreditFailure_DailyInsufficientCredit()
            commitTransaction( tx )
        }
        catch ( Exception e )
        {
            slog.error( "testMultiCreditRuleTakeCreditFailure_DailyInsufficientCredit", e )
            fail()
        }
    }

    void testMultiCreditUndoCreditExternalTx()
    {
        try
        {
            init( fiUser )
            IdcTransaction tx = initTransaction( true )
            testMultiCreditUndoCredit()
            commitTransaction( tx )
        }
        catch ( Exception e )
        {
            slog.error( "testMultiCreditUndoCreditExternalTx", e )
            fail()
        }
    }

    void testMultiCreditUndoCreditFailureExternalTx()
    {
        try
        {
            init( fiUser )
            IdcTransaction tx = initTransaction( true )
            testMultiCreditUndoCreditFailure()
            commitTransaction( tx )
        }
        catch ( Exception e )
        {
            slog.error( "testMultiCreditUndoCreditFailureExternalTx", e )
            fail()
        }
    }

    void testMultiCreditUpdateCreditExternalTx()
    {
        try
        {
            init( fiUser )
            IdcTransaction tx = initTransaction( true )
            testMultiCreditUpdateCredit()
            commitTransaction( tx )
        }
        catch ( Exception e )
        {
            slog.error( "testMultiCreditUpdateCreditExternalTx", e )
            fail()
        }
    }

    void testMultiCreditCheckCreditExternalTx()
    {
        try
        {
            init( fiUser )
            IdcTransaction tx = initTransaction( true )
            testMultiCreditCheckCredit()
            commitTransaction( tx )
        }
        catch ( Exception e )
        {
            slog.error( "testMultiCreditCheckCreditExternalTx", e )
            fail()
        }
    }

    void testMultiCreditCheckCreditFailureExternalTx()
    {
        try
        {
            init( fiUser )
            IdcTransaction tx = initTransaction( true )
            testMultiCreditCheckCreditFailure()
            commitTransaction( tx )
        }
        catch ( Exception e )
        {
            slog.error( "testMultiCreditCheckCreditFailureExternalTx", e )
            fail()
        }
    }

    void testMultiCreditReserveExternalTx()
    {
        try
        {
            init( fiUser )
            IdcTransaction tx = initTransaction( true )
            testMultiCreditReserve()
            commitTransaction( tx )
        }
        catch ( Exception e )
        {
            slog.error( "testMultiCreditReserveExternalTx", e )
            fail()
        }
    }

    void testMultiCreditReserveFailureExternalTx()
    {
        try
        {
            init( fiUser )
            IdcTransaction tx = initTransaction( true )
            testMultiCreditReserveFailure()
            commitTransaction( tx )
        }
        catch ( Exception e )
        {
            slog.error( "testMultiCreditReserveFailureExternalTx", e )
            fail()
        }
    }

    void testMultiCreditUndoReserveExternalTx()
    {
        try
        {
            init( fiUser )
            IdcTransaction tx = initTransaction( true )
            testMultiCreditUndoReserve()
            commitTransaction( tx )
        }
        catch ( Exception e )
        {
            slog.error( "testMultiCreditUndoReserveExternalTx", e )
            fail()
        }
    }

    void testSingleRulePublishCreditLimits()
    {
        try
        {
            init( lpUser )

            // subscribe for couple of currency pair updates.
            CreditUtilizationManagerC.getInstance().subscribe( lpLe, lpTpForDd, ddOrg, CurrencyFactory.getCurrency( "EUR" ), CurrencyFactory.getCurrency( "USD" ) )
            CreditUtilizationManagerC.getInstance().subscribe( lpLe, lpTpForDd, ddOrg, CurrencyFactory.getCurrency( "GBP" ), CurrencyFactory.getCurrency( "JPY" ) )

            double tradeAmt = 1000
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, ddOrg, lpTpForDd )
            assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS )
        }
        catch ( Exception e )
        {
            e.printStackTrace()
            fail()
        }
        finally
        {
            removeCreditLimitSubscriptions( lpLe, lpTpForDd )
        }
    }

    void testBilateralCreditTakeWithBothSuccess()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )
            removeExistingCreditUtilizationEvents( fiOrg )
            removeCreditLimitSubscriptions( lpLe, lpTpForFi )
            removeCreditLimitSubscriptions( fiLe, fiTpForLp )

            creditAdminSvc.setCreditLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, ********, CreditUtilC.getCounterpartyCreditLimitCurrency( lpOrg, fiOrg, lpTpForFi ) )
            creditAdminSvc.setCreditLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, 20000000, CreditUtilC.getCounterpartyCreditLimitCurrency( lpOrg, fiOrg, lpTpForFi ) )

            // subscribe for couple of currency pair updates.
            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate( lpLe, fiLe, CurrencyFactory.getCurrency( "EUR" ), CurrencyFactory.getCurrency( "USD" ) )
            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate( lpLe, fiLe, CurrencyFactory.getCurrency( "GBP" ), CurrencyFactory.getCurrency( "JPY" ) )

            double tradeAmt = 1000
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate )
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( fiLe, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS )
            assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS )
        }
        catch ( Exception e )
        {
            e.printStackTrace()
            fail()
        }
        finally
        {
            removeCreditLimitSubscriptions( lpLe, lpTpForFi )
            removeCreditLimitSubscriptions( fiLe, fiTpForLp )
        }
    }

    void testBilateralCreditTakeWitBothSuccessExternalTx()
    {
        try
        {
            init( lpUser )
            IdcTransaction tx = initTransaction( true )
            testBilateralCreditTakeWithBothSuccess()
            commitTransaction( tx )
        }
        catch ( Exception e )
        {
            e.printStackTrace()
            fail()
        }
    }

    void testBilateralCreditTakeWithSecondFailure()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( fiOrg )
            removeExistingCreditUtilizationEvents( lpOrg )
            double lowerLimit = 1000000
            double higherLimit = 2000000
            creditAdminSvc.setCreditLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, higherLimit, CreditUtilC.getCounterpartyCreditLimitCurrency( lpOrg, fiOrg, lpTpForFi ) )
            creditAdminSvc.setCreditLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, higherLimit, CreditUtilC.getCounterpartyCreditLimitCurrency( lpOrg, fiOrg, lpTpForFi ) )
            creditAdminSvc.setCreditLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, lowerLimit, CreditUtilC.getCounterpartyCreditLimitCurrency( fiOrg, lpOrg, fiTpForLp ) )
            creditAdminSvc.setCreditLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, lowerLimit, CreditUtilC.getCounterpartyCreditLimitCurrency( fiOrg, lpOrg, fiTpForLp ) )

            // subscribe for couple of currency pair updates.
            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate( lpLe, fiLe, CurrencyFactory.getCurrency( "EUR" ), CurrencyFactory.getCurrency( "USD" ) )
            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate( lpLe, fiLe, CurrencyFactory.getCurrency( "GBP" ), CurrencyFactory.getCurrency( "JPY" ) )

            AmountOfInstrument availableLpLimit = CreditUtilizationManagerC.getInstance().getAvailableLimitAmountOfInstrument( lpLe, lpTpForFi, fiOrg, spotDate )
            AmountOfInstrument availableFiLimit = CreditUtilizationManagerC.getInstance().getAvailableLimitAmountOfInstrument( fiLe, fiTpForLp, lpOrg, spotDate )
            AmountOfInstrument availableLimit = CreditUtilizationManagerC.getInstance().getAvailableBilateralCreditLimitAmountOfInstrument( lpLe, fiLe, spotDate )

            Double suspensionPercent = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( lpOrg, lpTpForFi ).getSuspensionPercentage()
            Double totalLimit = getTotalCreditLimit( lpOrg, lpTpForFi )
            Double bufferLimitForLPCreditProvider = totalLimit * ( 1 - suspensionPercent / 100 )
            suspensionPercent = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( fiOrg, fiTpForLp ).getSuspensionPercentage()
            totalLimit = getTotalCreditLimit( fiOrg, fiTpForLp )
            Double bufferLimitForFICreditProvider = totalLimit * ( 1 - suspensionPercent / 100 )
            Double bufferLimit = availableLpLimit.getAmount() < availableFiLimit.getAmount() ? bufferLimitForLPCreditProvider : bufferLimitForFICreditProvider

            double tradeAmt = availableLimit.getAmount() + ( bufferLimit + 200 )
            log( "availableLpLimit=" + availableLpLimit + ",availableFiLimit=" + availableFiLimit + ",availableLimit=" + availableLimit + ",tradeAmt=" + tradeAmt )
            Trade trade = prepareSingleLegTrade( tradeAmt, false, CurrencyFactory.getCurrency( "EUR" ).isSameAs( availableLimit.getInstrument() ) ? true : false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate )
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( lpLe, fiLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS )
            assertEquals( "Message should have failure status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.FAILURE )

            AmountOfInstrument availableLpLimit1 = CreditUtilizationManagerC.getInstance().getAvailableLimitAmountOfInstrument( lpLe, lpTpForFi, fiOrg, spotDate )
            AmountOfInstrument availableFiLimit1 = CreditUtilizationManagerC.getInstance().getAvailableLimitAmountOfInstrument( fiLe, fiTpForLp, lpOrg, spotDate )
            AmountOfInstrument availableLimit1 = CreditUtilizationManagerC.getInstance().getAvailableBilateralCreditLimitAmountOfInstrument( lpLe, fiLe, spotDate )

            log( "availableLpLimit1=" + availableLpLimit1 + ",availableFiLimit1=" + availableFiLimit1 + ",availableLimit1=" + availableLimit1 )
            assertEquals( "availableLpLimit should be same.", availableLpLimit.getAmount(), availableLpLimit1.getAmount() )
            assertEquals( "availableFiLimit should be same.", availableFiLimit.getAmount(), availableFiLimit1.getAmount() )
            assertEquals( "availableLimit should be same.", availableLimit.getAmount(), availableLimit1.getAmount() )
        }
        catch ( Exception e )
        {
            e.printStackTrace()
            fail()
        }
        finally
        {
            removeCreditLimitSubscriptions( lpLe, lpTpForFi )
            removeCreditLimitSubscriptions( fiLe, fiTpForLp )
        }
    }

    void testBilateralCreditTakeWithSecondFailureExernalTx()
    {
        try
        {
            init( lpUser )
            IdcTransaction tx = initTransaction( true )
            testBilateralCreditTakeWithSecondFailure()
            commitTransaction( tx )
        }
        catch ( Exception e )
        {
            e.printStackTrace()
            fail()
        }
    }

    void testBilateralCreditUndo()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )
            removeExistingCreditUtilizationEvents( fiOrg )

            creditAdminSvc.setCreditLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, ********, CreditUtilC.getCounterpartyCreditLimitCurrency( lpOrg, fiOrg, lpTpForFi ) )
            creditAdminSvc.setCreditLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, 20000000, CreditUtilC.getCounterpartyCreditLimitCurrency( lpOrg, fiOrg, lpTpForFi ) )

            // subscribe for couple of currency pair updates.
            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate( lpLe, fiLe, CurrencyFactory.getCurrency( "EUR" ), CurrencyFactory.getCurrency( "USD" ) )
            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate( lpLe, fiLe, CurrencyFactory.getCurrency( "GBP" ), CurrencyFactory.getCurrency( "JPY" ) )

            AmountOfInstrument availableLpLimit = CreditUtilizationManagerC.getInstance().getAvailableLimitAmountOfInstrument( lpLe, lpTpForFi, fiOrg, spotDate )
            AmountOfInstrument availableFiLimit = CreditUtilizationManagerC.getInstance().getAvailableLimitAmountOfInstrument( fiLe, fiTpForLp, lpOrg, spotDate )
            AmountOfInstrument availableLimit = CreditUtilizationManagerC.getInstance().getAvailableBilateralCreditLimitAmountOfInstrument( lpLe, fiLe, spotDate )
            log( "availableLpLimit=" + availableLpLimit + ",availableFiLimit=" + availableFiLimit + ",availableLimit=" + availableLimit )

            double tradeAmt = 1000
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate )
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( fiLe, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS )
            assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS )

            CreditWorkflowMessage cwm1 = creditMgr.undoBilateralCredit( fiLe, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS )
            assertEquals( "Undo Message success status=" + cwm1.getStatus(), cwm1.getStatus(), MessageStatus.SUCCESS )

            AmountOfInstrument availableLpLimit1 = CreditUtilizationManagerC.getInstance().getAvailableLimitAmountOfInstrument( lpLe, lpTpForFi, fiOrg, spotDate )
            AmountOfInstrument availableFiLimit1 = CreditUtilizationManagerC.getInstance().getAvailableLimitAmountOfInstrument( fiLe, fiTpForLp, lpOrg, spotDate )
            AmountOfInstrument availableLimit1 = CreditUtilizationManagerC.getInstance().getAvailableBilateralCreditLimitAmountOfInstrument( lpLe, fiLe, spotDate )

            log( "availableLpLimit1=" + availableLpLimit1 + ",availableFiLimit1=" + availableFiLimit1 + ",availableLimit1=" + availableLimit1 )
            assertEquals( "availableLpLimit should be same.", availableLpLimit.getAmount(), availableLpLimit1.getAmount() )
            assertEquals( "availableFiLimit should be same.", availableFiLimit.getAmount(), availableFiLimit1.getAmount() )
            assertEquals( "availableLimit should be same.", availableLimit.getAmount(), availableLimit1.getAmount() )
        }
        catch ( Exception e )
        {
            e.printStackTrace()
            fail()
        }
        finally
        {
            removeCreditLimitSubscriptions( lpLe, lpTpForFi )
            removeCreditLimitSubscriptions( fiLe, fiTpForLp )
        }
    }

    void testBilateralCreditUndoExternalTx()
    {
        try
        {
            init( lpUser )
            IdcTransaction tx = initTransaction( true )
            testBilateralCreditUndo()
            commitTransaction( tx )
        }
        catch ( Exception e )
        {
            e.printStackTrace()
            fail()
        }
    }

    void testBilateralCreditTakeWithBothSuccessOnExternalTxFailure()
    {
        try
        {
            init( lpUser )
            IdcTransaction tx = initTransaction( true )
            testBilateralCreditTakeWithBothSuccess()

            //check the real-time utilization amount. This should be zero since transaction rolled back.
            double fiDailyUsedAmt = getRealtimeDailyUtilizedAmount( fiTpForLp, spotDate )
            double fiAggregateUsedAmt = getRealtimeAggregateUtilizedAmount( fiTpForLp, spotDate )
            double lpDailyUsedAmt = getRealtimeDailyUtilizedAmount( lpTpForFi, spotDate )
            double lpAggregateUsedAmt = getRealtimeAggregateUtilizedAmount( lpTpForFi, spotDate )
            log( "After credit workflow. fiDailyUsedAmt=" + fiDailyUsedAmt + ",fiAggregateUsedAmt=" + fiAggregateUsedAmt + ",lpDailyUsedAmt=" + lpDailyUsedAmt + ",lpAggregateUsedAmt=" + lpAggregateUsedAmt )
            assertTrue( fiDailyUsedAmt > 0.0 )
            assertTrue( fiAggregateUsedAmt > 0.0 )
            assertTrue( lpDailyUsedAmt > 0.0 )
            assertTrue( lpAggregateUsedAmt > 0.0 )

            // now release the transaction so that utilizations has to be removed.
            releaseTransaction( tx )

            //check the real-time utilization amount. This should be zero since transaction rolled back.
            fiDailyUsedAmt = getRealtimeDailyUtilizedAmount( fiTpForLp, spotDate )
            fiAggregateUsedAmt = getRealtimeAggregateUtilizedAmount( fiTpForLp, spotDate )
            lpDailyUsedAmt = getRealtimeDailyUtilizedAmount( lpTpForFi, spotDate )
            lpAggregateUsedAmt = getRealtimeAggregateUtilizedAmount( lpTpForFi, spotDate )
            log( "After transaction release. fiDailyUsedAmt=" + fiDailyUsedAmt + ",fiAggregateUsedAmt=" + fiAggregateUsedAmt + ",lpDailyUsedAmt=" + lpDailyUsedAmt + ",lpAggregateUsedAmt=" + lpAggregateUsedAmt )
            assertEquals( fiDailyUsedAmt, 0.0 )
            assertEquals( fiAggregateUsedAmt, 0.0 )
            assertEquals( lpDailyUsedAmt, 0.0 )
            assertEquals( lpAggregateUsedAmt, 0.0 )
        }
        catch ( Exception e )
        {
            e.printStackTrace()
            fail()
        }
    }

    void testBilateralCreditTakeWithSecondFailureOnExternalTxFailure()
    {
        try
        {
            init( lpUser )
            IdcTransaction tx = initTransaction( true )
            testBilateralCreditTakeWithSecondFailure()

            //check the real-time utilization amount. This should be zero since transaction rolled back.
            double fiDailyUsedAmt = getRealtimeDailyUtilizedAmount( fiTpForLp, spotDate )
            double fiAggregateUsedAmt = getRealtimeAggregateUtilizedAmount( fiTpForLp, spotDate )
            double lpDailyUsedAmt = getRealtimeDailyUtilizedAmount( lpTpForFi, spotDate )
            double lpAggregateUsedAmt = getRealtimeAggregateUtilizedAmount( lpTpForFi, spotDate )
            log( "After credit workflow. fiDailyUsedAmt=" + fiDailyUsedAmt + ",fiAggregateUsedAmt=" + fiAggregateUsedAmt + ",lpDailyUsedAmt=" + lpDailyUsedAmt + ",lpAggregateUsedAmt=" + lpAggregateUsedAmt )
            assertEquals( fiDailyUsedAmt, 0.0 )
            assertEquals( fiAggregateUsedAmt, 0.0 )
            assertEquals( lpDailyUsedAmt, 0.0 )
            assertEquals( lpAggregateUsedAmt, 0.0 )

            // now release the transaction so that utilizations has to be removed.
            releaseTransaction( tx )

            //check the real-time utilization amount. This should be zero since transaction rolled back.
            fiDailyUsedAmt = getRealtimeDailyUtilizedAmount( fiTpForLp, spotDate )
            fiAggregateUsedAmt = getRealtimeAggregateUtilizedAmount( fiTpForLp, spotDate )
            lpDailyUsedAmt = getRealtimeDailyUtilizedAmount( lpTpForFi, spotDate )
            lpAggregateUsedAmt = getRealtimeAggregateUtilizedAmount( lpTpForFi, spotDate )
            log( "After transaction release. fiDailyUsedAmt=" + fiDailyUsedAmt + ",fiAggregateUsedAmt=" + fiAggregateUsedAmt + ",lpDailyUsedAmt=" + lpDailyUsedAmt + ",lpAggregateUsedAmt=" + lpAggregateUsedAmt )
            assertEquals( fiDailyUsedAmt, 0.0 )
            assertEquals( fiAggregateUsedAmt, 0.0 )
            assertEquals( lpDailyUsedAmt, 0.0 )
            assertEquals( lpAggregateUsedAmt, 0.0 )
        }
        catch ( Exception e )
        {
            e.printStackTrace()
            fail()
        }
    }

    void testBilateralCreditUndoOnExternalTxFailure()
    {
        try
        {
            init( lpUser )
            IdcTransaction tx = initTransaction( true )
            testBilateralCreditUndo()

            //check the real-time utilization amount. This should be zero since transaction rolled back.
            double fiDailyUsedAmt = getRealtimeDailyUtilizedAmount( fiTpForLp, spotDate )
            double fiAggregateUsedAmt = getRealtimeAggregateUtilizedAmount( fiTpForLp, spotDate )
            double lpDailyUsedAmt = getRealtimeDailyUtilizedAmount( lpTpForFi, spotDate )
            double lpAggregateUsedAmt = getRealtimeAggregateUtilizedAmount( lpTpForFi, spotDate )
            log( "After credit workflow. fiDailyUsedAmt=" + fiDailyUsedAmt + ",fiAggregateUsedAmt=" + fiAggregateUsedAmt + ",lpDailyUsedAmt=" + lpDailyUsedAmt + ",lpAggregateUsedAmt=" + lpAggregateUsedAmt )
            assertEquals( fiDailyUsedAmt, 0.0 )
            assertEquals( fiAggregateUsedAmt, 0.0 )
            assertEquals( lpDailyUsedAmt, 0.0 )
            assertEquals( lpAggregateUsedAmt, 0.0 )

            // now release the transaction so that utilizations has to be removed.
            releaseTransaction( tx )

            //check the real-time utilization amount. This should be zero since transaction rolled back.
            fiDailyUsedAmt = getRealtimeDailyUtilizedAmount( fiTpForLp, spotDate )
            fiAggregateUsedAmt = getRealtimeAggregateUtilizedAmount( fiTpForLp, spotDate )
            lpDailyUsedAmt = getRealtimeDailyUtilizedAmount( lpTpForFi, spotDate )
            lpAggregateUsedAmt = getRealtimeAggregateUtilizedAmount( lpTpForFi, spotDate )
            log( "After transaction release. fiDailyUsedAmt=" + fiDailyUsedAmt + ",fiAggregateUsedAmt=" + fiAggregateUsedAmt + ",lpDailyUsedAmt=" + lpDailyUsedAmt + ",lpAggregateUsedAmt=" + lpAggregateUsedAmt )
            assertEquals( fiDailyUsedAmt, 0.0 )
            assertEquals( fiAggregateUsedAmt, 0.0 )
            assertEquals( lpDailyUsedAmt, 0.0 )
            assertEquals( lpAggregateUsedAmt, 0.0 )
        }
        catch ( Exception e )
        {
            e.printStackTrace()
            fail()
        }
    }

    void testBilateralCreditUndoAfterSuccessfulTakeOnExternalTxFailure()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )
            removeExistingCreditUtilizationEvents( fiOrg )
            removeCreditLimitSubscriptions( lpLe, lpTpForFi )
            removeCreditLimitSubscriptions( fiLe, fiTpForLp )

            creditAdminSvc.setCreditLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, ********, CreditUtilC.getCounterpartyCreditLimitCurrency( lpOrg, fiOrg, lpTpForFi ) )
            creditAdminSvc.setCreditLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, 20000000, CreditUtilC.getCounterpartyCreditLimitCurrency( lpOrg, fiOrg, lpTpForFi ) )

            double tradeAmt = 1000
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate )
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( fiLe, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS )

            //check the real-time utilization amount. This should be zero since transaction rolled back.
            double fiDailyUsedAmt = getRealtimeDailyUtilizedAmount( fiTpForLp, spotDate )
            double fiAggregateUsedAmt = getRealtimeAggregateUtilizedAmount( fiTpForLp, spotDate )
            double lpDailyUsedAmt = getRealtimeDailyUtilizedAmount( lpTpForFi, spotDate )
            double lpAggregateUsedAmt = getRealtimeAggregateUtilizedAmount( lpTpForFi, spotDate )
            log( "After credit workflow. fiDailyUsedAmt=" + fiDailyUsedAmt + ",fiAggregateUsedAmt=" + fiAggregateUsedAmt + ",lpDailyUsedAmt=" + lpDailyUsedAmt + ",lpAggregateUsedAmt=" + lpAggregateUsedAmt )
            assertTrue( fiDailyUsedAmt > 0.0 )
            assertTrue( fiAggregateUsedAmt > 0.0 )
            assertTrue( lpDailyUsedAmt > 0.0 )
            assertTrue( lpAggregateUsedAmt > 0.0 )

            init( lpUser )
            IdcTransaction tx = initTransaction( true )

            CreditWorkflowMessage cwm1 = creditMgr.undoBilateralCredit( fiLe, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS )
            assertEquals( "Undo Message success status=" + cwm1.getStatus(), cwm1.getStatus(), MessageStatus.SUCCESS )

            //check the real-time utilization amount. This should be zero since transaction rolled back.
            fiDailyUsedAmt = getRealtimeDailyUtilizedAmount( fiTpForLp, spotDate )
            fiAggregateUsedAmt = getRealtimeAggregateUtilizedAmount( fiTpForLp, spotDate )
            lpDailyUsedAmt = getRealtimeDailyUtilizedAmount( lpTpForFi, spotDate )
            lpAggregateUsedAmt = getRealtimeAggregateUtilizedAmount( lpTpForFi, spotDate )
            log( "After Undo credit workflow. fiDailyUsedAmt=" + fiDailyUsedAmt + ",fiAggregateUsedAmt=" + fiAggregateUsedAmt + ",lpDailyUsedAmt=" + lpDailyUsedAmt + ",lpAggregateUsedAmt=" + lpAggregateUsedAmt )
            assertEquals( fiDailyUsedAmt, 0.0 )
            assertEquals( fiAggregateUsedAmt, 0.0 )
            assertEquals( lpDailyUsedAmt, 0.0 )
            assertEquals( lpAggregateUsedAmt, 0.0 )

            // now release the transaction so that utilizations has to be removed.
            releaseTransaction( tx )

            //check the real-time utilization amount. This should be zero since transaction rolled back.
            fiDailyUsedAmt = getRealtimeDailyUtilizedAmount( fiTpForLp, spotDate )
            fiAggregateUsedAmt = getRealtimeAggregateUtilizedAmount( fiTpForLp, spotDate )
            lpDailyUsedAmt = getRealtimeDailyUtilizedAmount( lpTpForFi, spotDate )
            lpAggregateUsedAmt = getRealtimeAggregateUtilizedAmount( lpTpForFi, spotDate )
            log( "After transaction release. fiDailyUsedAmt=" + fiDailyUsedAmt + ",fiAggregateUsedAmt=" + fiAggregateUsedAmt + ",lpDailyUsedAmt=" + lpDailyUsedAmt + ",lpAggregateUsedAmt=" + lpAggregateUsedAmt )
            assertEquals( fiDailyUsedAmt, 0.0 )
            assertEquals( fiAggregateUsedAmt, 0.0 )
            assertEquals( lpDailyUsedAmt, 0.0 )
            assertEquals( lpAggregateUsedAmt, 0.0 )

            //now undo the trade
            CreditWorkflowMessage cwm2 = creditMgr.undoBilateralCredit( fiLe, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS )
            assertEquals( "Undo Message success status=" + cwm2.getStatus(), cwm2.getStatus(), MessageStatus.FAILURE )

            fiDailyUsedAmt = getRealtimeDailyUtilizedAmount( fiTpForLp, spotDate )
            fiAggregateUsedAmt = getRealtimeAggregateUtilizedAmount( fiTpForLp, spotDate )
            lpDailyUsedAmt = getRealtimeDailyUtilizedAmount( lpTpForFi, spotDate )
            lpAggregateUsedAmt = getRealtimeAggregateUtilizedAmount( lpTpForFi, spotDate )
            log( "After proper Undo credit workflow. fiDailyUsedAmt=" + fiDailyUsedAmt + ",fiAggregateUsedAmt=" + fiAggregateUsedAmt + ",lpDailyUsedAmt=" + lpDailyUsedAmt + ",lpAggregateUsedAmt=" + lpAggregateUsedAmt )
            assertEquals( fiDailyUsedAmt, 0.0 )
            assertEquals( fiAggregateUsedAmt, 0.0 )
            assertEquals( lpDailyUsedAmt, 0.0 )
            assertEquals( lpAggregateUsedAmt, 0.0 )

            // start a transaction and undo the trade.
            init( lpUser )
            IdcTransaction tx1 = initTransaction( true )

            CreditWorkflowMessage cwm3 = creditMgr.undoBilateralCredit( fiLe, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS )
            assertEquals( "Undo Message failure status=" + cwm3.getStatus(), cwm3.getStatus(), MessageStatus.FAILURE )

            fiDailyUsedAmt = getRealtimeDailyUtilizedAmount( fiTpForLp, spotDate )
            fiAggregateUsedAmt = getRealtimeAggregateUtilizedAmount( fiTpForLp, spotDate )
            lpDailyUsedAmt = getRealtimeDailyUtilizedAmount( lpTpForFi, spotDate )
            lpAggregateUsedAmt = getRealtimeAggregateUtilizedAmount( lpTpForFi, spotDate )
            log( "After second Undo credit workflow. fiDailyUsedAmt=" + fiDailyUsedAmt + ",fiAggregateUsedAmt=" + fiAggregateUsedAmt + ",lpDailyUsedAmt=" + lpDailyUsedAmt + ",lpAggregateUsedAmt=" + lpAggregateUsedAmt )
            assertEquals( fiDailyUsedAmt, 0.0 )
            assertEquals( fiAggregateUsedAmt, 0.0 )
            assertEquals( lpDailyUsedAmt, 0.0 )
            assertEquals( lpAggregateUsedAmt, 0.0 )

            //release the transaction
            releaseTransaction( tx1 )

            fiDailyUsedAmt = getRealtimeDailyUtilizedAmount( fiTpForLp, spotDate )
            fiAggregateUsedAmt = getRealtimeAggregateUtilizedAmount( fiTpForLp, spotDate )
            lpDailyUsedAmt = getRealtimeDailyUtilizedAmount( lpTpForFi, spotDate )
            lpAggregateUsedAmt = getRealtimeAggregateUtilizedAmount( lpTpForFi, spotDate )
            log( "After second undo and release tx Undo credit workflow. fiDailyUsedAmt=" + fiDailyUsedAmt + ",fiAggregateUsedAmt=" + fiAggregateUsedAmt + ",lpDailyUsedAmt=" + lpDailyUsedAmt + ",lpAggregateUsedAmt=" + lpAggregateUsedAmt )
            assertEquals( fiDailyUsedAmt, 0.0 )
            assertEquals( fiAggregateUsedAmt, 0.0 )
            assertEquals( lpDailyUsedAmt, 0.0 )
            assertEquals( lpAggregateUsedAmt, 0.0 )
        }
        catch ( Exception e )
        {
            e.printStackTrace()
            fail()
        }
    }


    void testBilateralCreditUpdate()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )
            removeExistingCreditUtilizationEvents( fiOrg )

            creditAdminSvc.setCreditLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, ********, CreditUtilC.getCounterpartyCreditLimitCurrency( lpOrg, fiOrg, lpTpForFi ) )
            creditAdminSvc.setCreditLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, 20000000, CreditUtilC.getCounterpartyCreditLimitCurrency( lpOrg, fiOrg, lpTpForFi ) )

            // subscribe for couple of currency pair updates.
            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate( lpLe, fiLe, CurrencyFactory.getCurrency( "EUR" ), CurrencyFactory.getCurrency( "USD" ) )
            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate( lpLe, fiLe, CurrencyFactory.getCurrency( "GBP" ), CurrencyFactory.getCurrency( "JPY" ) )

            double tradeAmt = 1000
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate )
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( fiLe, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS )
            assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS )

            AmountOfInstrument availableLpLimit = CreditUtilizationManagerC.getInstance().getAvailableLimitAmountOfInstrument( lpLe, lpTpForFi, fiOrg, spotDate )
            AmountOfInstrument availableFiLimit = CreditUtilizationManagerC.getInstance().getAvailableLimitAmountOfInstrument( fiLe, fiTpForLp, lpOrg, spotDate )
            AmountOfInstrument availableLimit = CreditUtilizationManagerC.getInstance().getAvailableBilateralCreditLimitAmountOfInstrument( lpLe, fiLe, spotDate )
            log( "availableLpLimit=" + availableLpLimit + ",availableFiLimit=" + availableFiLimit + ",availableLimit=" + availableLimit )

            // change the trade amount
            double offset = 500
            updateTradeAmount( trade, tradeAmt + offset, true, false )

        }
        catch ( Exception e )
        {
            e.printStackTrace()
            fail()
        }
        finally
        {
            removeCreditLimitSubscriptions( lpLe, lpTpForFi )
            removeCreditLimitSubscriptions( fiLe, fiTpForLp )
        }
    }

    void testBilateralCreditUpdateExternalTx()
    {
        try
        {
            init( lpUser )
            IdcTransaction tx = initTransaction( true )
            testBilateralCreditUpdate()
            commitTransaction( tx )
        }
        catch ( Exception e )
        {
            e.printStackTrace()
            fail()
        }
    }

    void testBilateralCreditUtilizationAmountUpdate()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )
            removeExistingCreditUtilizationEvents( fiOrg )

            creditAdminSvc.setCreditLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, ********, CreditUtilC.getCounterpartyCreditLimitCurrency( lpOrg, fiOrg, lpTpForFi ) )
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 20000000 )

            // subscribe for couple of currency pair updates.
            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate( lpLe, fiLe, CurrencyFactory.getCurrency( "EUR" ), CurrencyFactory.getCurrency( "USD" ) )
            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate( lpLe, fiLe, CurrencyFactory.getCurrency( "GBP" ), CurrencyFactory.getCurrency( "JPY" ) )

            double tradeAmt = 1000
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate )

            //update Credit utilization amount before take credit should fail
            CreditWorkflowMessage cwm1 = creditMgr.updateBilateralCreditUtilizationAmount( fiLe, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS )
            assertEquals( "Undo Message success status=" + cwm1.getStatus(), cwm1.getStatus(), MessageStatus.FAILURE )

            //do update Credit utilization amount after take credit for single-leg trade should pass
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( fiLe, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS )
            assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS )

            double avaliableLpAggLimit = getAvailableCreditLimit( lpTpForFi, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            double avaliableFiAggLimit = getAvailableCreditLimit( fiTpForLp, spotDate, GROSS_NOTIONAL_CLASSIFICATION )

            AmountOfInstrument availableLpLimit = CreditUtilizationManagerC.getInstance().getAvailableLimitAmountOfInstrument( lpLe, lpTpForFi, fiOrg, spotDate )
            AmountOfInstrument availableFiLimit = CreditUtilizationManagerC.getInstance().getAvailableLimitAmountOfInstrument( fiLe, fiTpForLp, lpOrg, spotDate )
            AmountOfInstrument availableLimit = CreditUtilizationManagerC.getInstance().getAvailableBilateralCreditLimitAmountOfInstrument( lpLe, fiLe, spotDate )
            log( "availableLpLimit=" + availableLpLimit + ",availableFiLimit=" + availableFiLimit + ",availableLimit=" + availableLimit )
            log( "avaliableLpAggLimit=" + avaliableLpAggLimit + ",avaliableFiAggLimit=" + avaliableFiAggLimit )

            // change the trade amount
            double offset = -500
            updateTradeAmount( trade, tradeAmt + offset, true, false )

            CreditWorkflowMessage cwm2 = creditMgr.updateBilateralCreditUtilizationAmount( fiLe, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS )
            assertEquals( "Undo Message success status=" + cwm2.getStatus(), cwm2.getStatus(), MessageStatus.SUCCESS )

            double avaliableLpAggLimit1 = getAvailableCreditLimit( lpTpForFi, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            double avaliableFiAggLimit1 = getAvailableCreditLimit( fiTpForLp, spotDate, GROSS_NOTIONAL_CLASSIFICATION )


            AmountOfInstrument availableLpLimit1 = CreditUtilizationManagerC.getInstance().getAvailableLimitAmountOfInstrument( lpLe, lpTpForFi, fiOrg, spotDate )
            AmountOfInstrument availableFiLimit1 = CreditUtilizationManagerC.getInstance().getAvailableLimitAmountOfInstrument( fiLe, fiTpForLp, lpOrg, spotDate )
            AmountOfInstrument availableLimit1 = CreditUtilizationManagerC.getInstance().getAvailableBilateralCreditLimitAmountOfInstrument( lpLe, fiLe, spotDate )

            log( "availableLpLimit1=" + availableLpLimit1 + ",availableFiLimit1=" + availableFiLimit1 + ",availableLimit1=" + availableLimit1 )
            log( "avaliableLpAggLimit1=" + avaliableLpAggLimit1 + ",avaliableFiAggLimit1=" + avaliableFiAggLimit1 )
            assertEquals( "aggregate LP limits should be sum of offset", Math.abs( avaliableLpAggLimit - avaliableLpAggLimit1 - offset ) < CREDIT_CALCULATION_MINIMUM, true )
            assertEquals( "aggregate FI limits should be sum of offset", Math.abs( avaliableFiAggLimit - avaliableFiAggLimit1 - offset ) < CREDIT_CALCULATION_MINIMUM, true )

            assertEquals( "availableLpLimit should not be same.", Math.abs( availableLpLimit.getAmount() - availableLpLimit1.getAmount() ) < CREDIT_CALCULATION_MINIMUM, false )
            assertEquals( "availableFiLimit should not be same.", Math.abs( availableFiLimit.getAmount() - availableFiLimit1.getAmount() ) < CREDIT_CALCULATION_MINIMUM, false )
            assertEquals( "availableLimit should not be same.", Math.abs( availableLimit.getAmount() - availableLimit1.getAmount() ) < CREDIT_CALCULATION_MINIMUM, false )

            assertEquals( "availableLpLimit should not be same.", Math.abs( availableLpLimit.getAmount() - availableLpLimit1.getAmount() - offset ) < CREDIT_CALCULATION_MINIMUM, true )
            assertEquals( "availableFiLimit should not be same.", Math.abs( availableFiLimit.getAmount() - availableFiLimit1.getAmount() - offset ) < CREDIT_CALCULATION_MINIMUM, true )
        }
        catch ( Exception e )
        {
            e.printStackTrace()
            fail()
        }
        finally
        {
            removeCreditLimitSubscriptions( lpLe, lpTpForFi )
            removeCreditLimitSubscriptions( fiLe, fiTpForLp )
        }
    }

    void testUndoBilateralCreditWithBothCreditDisabled()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )
            removeExistingCreditUtilizationEvents( fiOrg )

            creditAdminSvc.setCreditEnabled( lpOrg, false )
            creditAdminSvc.setCreditEnabled( lpOrg, lpTpForFi, false )
            creditAdminSvc.setCreditEnabled( fiOrg, false )
            creditAdminSvc.setCreditEnabled( fiOrg, fiTpForLp, false )

            double tradeAmt = 1000
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate )
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( lpLe, fiLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS )
            assertEquals( "Undo Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS )

            CreditWorkflowMessage cwm1 = creditMgr.undoBilateralCredit( lpLe, fiLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS )
            assertEquals( "Undo Message success status=" + cwm1.getStatus(), cwm1.getStatus(), MessageStatus.FAILURE )
            assertEquals( "Message error.", ( ( ErrorMessage ) cwm1.getErrors().toArray()[0] ).getCode(), CreditLimit.ERROR_CREDIT_LIMIT_WORKFLOW_STATE_UNAVAILABLE )
        }
        catch ( Exception e )
        {
            e.printStackTrace()
            fail()
        }
        finally
        {
            creditAdminSvc.setCreditEnabled( lpOrg, true )
            creditAdminSvc.setCreditEnabled( lpOrg, lpTpForFi, true )
            creditAdminSvc.setCreditEnabled( fiOrg, true )
            creditAdminSvc.setCreditEnabled( fiOrg, fiTpForLp, true )

        }
    }


    void testCreditUtilizationAmountUpdateOnSwap()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )
            removeExistingCreditUtilizationEvents( fiOrg )

            creditAdminSvc.setCreditEnabled( lpOrg, true )
            creditAdminSvc.setCreditEnabled( lpOrg, lpTpForFi, true )
            creditAdminSvc.setCreditEnabled( fiOrg, true )
            creditAdminSvc.setCreditEnabled( fiOrg, fiTpForLp, true )

            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 )
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 )
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 )
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 )

            // subscribe for couple of currency pair updates.
            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate( lpLe, fiLe, CurrencyFactory.getCurrency( "EUR" ), CurrencyFactory.getCurrency( "USD" ) )
            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate( lpLe, fiLe, CurrencyFactory.getCurrency( "GBP" ), CurrencyFactory.getCurrency( "JPY" ) )

            double nearTradeAmt = 1000
            double farTradeAmt = 2000
            Trade trade = prepareSwapTrade( nearTradeAmt, farTradeAmt, true, false, false, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], bidRates[0], spotDate, spotDate.addDays( 2 ) )

            // take credit for swap trade should pass
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( fiLe, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS )
            assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS )

            // available amount on both near and far spot dates before Update of util amount
            double availableLpNearLimit = getAvailableCreditLimit( lpTpForFi, spotDate, DAILY_SETTLEMENT_CLASSIFICATION )
            double availableFiNearLimit = getAvailableCreditLimit( fiTpForLp, spotDate, DAILY_SETTLEMENT_CLASSIFICATION )
            AmountOfInstrument availableLimit = CreditUtilizationManagerC.getInstance().getAvailableBilateralCreditLimitAmountOfInstrument( lpLe, fiLe, spotDate )
            log( "availableLpNearLimit=" + availableLpNearLimit + ",availableFiNearLimit=" + availableFiNearLimit + ",availableLimit=" + availableLimit )

            double availableLpFarLimit = getAvailableCreditLimit( lpTpForFi, spotDate.addDays( 2 ), DAILY_SETTLEMENT_CLASSIFICATION )
            double availableFiFarLimit = getAvailableCreditLimit( fiTpForLp, spotDate.addDays( 2 ), DAILY_SETTLEMENT_CLASSIFICATION )
            log( "availableLpFarLimit=" + availableLpFarLimit + ",availableFiFarLimit=" + availableFiFarLimit )

            // change the trade amount
            double nearLegOffset = 1000
            double farLegOffset = 2000
            updateSwapTradeAmount( trade, nearTradeAmt + nearLegOffset, farTradeAmt + farLegOffset, true, false )
            FXMarketDataElement fxMde = staticMds.findSpotConversionMarketDataElement( CurrencyFactory.getCurrency( "EUR" ), CurrencyFactory.getCurrency( "USD" ), true )
            double spread = fxMde.getFXPrice().getOfferFXRate().getSpotRate() - fxMde.getFXPrice().getBidFXRate().getSpotRate()
            double spreadOffset = ( farTradeAmt + farLegOffset ) * spread
            log( "spreadOffset=" + spreadOffset )

            CreditWorkflowMessage cwm2 = creditMgr.updateBilateralCreditUtilizationAmount( fiLe, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS )
            assertEquals( "Undo Message success status=" + cwm2.getStatus(), cwm2.getStatus(), MessageStatus.SUCCESS )

            // available amount on both near and far spot dates after Update of util amount
            double availableLpNearLimit1 = getAvailableCreditLimit( lpTpForFi, spotDate, DAILY_SETTLEMENT_CLASSIFICATION )
            double availableFiNearLimit1 = getAvailableCreditLimit( fiTpForLp, spotDate, DAILY_SETTLEMENT_CLASSIFICATION )
            AmountOfInstrument availableLimit1 = CreditUtilizationManagerC.getInstance().getAvailableBilateralCreditLimitAmountOfInstrument( lpLe, fiLe, spotDate )
            log( "availableLpNearLimit1=" + availableLpNearLimit1 + ",availableFiNearLimit1=" + availableFiNearLimit1 + ",availableLimit1=" + availableLimit1 )

            assertEquals( "availableLpNearLimit should not be same.", Math.abs( availableLpNearLimit - availableLpNearLimit1 - nearLegOffset ) < CREDIT_CALCULATION_MINIMUM + spreadOffset, true )
            assertEquals( "availableFiLimit should not be same.", Math.abs( availableFiNearLimit - availableFiNearLimit1 - nearLegOffset ) < CREDIT_CALCULATION_MINIMUM + spreadOffset, true )

            double availableLpFarLimit1 = getAvailableCreditLimit( lpTpForFi, spotDate.addDays( 2 ), DAILY_SETTLEMENT_CLASSIFICATION )
            double availableFiFarLimit1 = getAvailableCreditLimit( fiTpForLp, spotDate.addDays( 2 ), DAILY_SETTLEMENT_CLASSIFICATION )
            log( "availableLpFarLimit1=" + availableLpFarLimit1 + ",availableFiFarLimit1=" + availableFiFarLimit1 )

            assertEquals( "availableLpFarLimit should not be same.", Math.abs( availableLpFarLimit - availableLpFarLimit1 - farLegOffset ) < CREDIT_CALCULATION_MINIMUM + spreadOffset, true )
            assertEquals( "availableFiFarLimit should not be same.", Math.abs( availableFiFarLimit - availableFiFarLimit1 - farLegOffset ) < CREDIT_CALCULATION_MINIMUM + spreadOffset, true )

            assertEquals( "availableLimit should not be same.", Math.abs( availableLimit.getAmount() - availableLimit1.getAmount() ) > CREDIT_CALCULATION_MINIMUM, true )
        }
        catch ( Exception e )
        {
            e.printStackTrace()
            fail()
        }
        finally
        {
            removeCreditLimitSubscriptions( lpLe, lpTpForFi )
            removeCreditLimitSubscriptions( fiLe, fiTpForLp )
        }
    }

    void testMultiFillCreditUtilizationExclTradeInDealCollection()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )
            removeExistingCreditUtilizationEvents( fiOrg )

            creditAdminSvc.setCreditLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, ********, CreditUtilC.getCounterpartyCreditLimitCurrency( lpOrg, fiOrg, lpTpForFi ) )
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 20000000 )

            // subscribe for couple of currency pair updates.
            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate( lpLe, fiLe, CurrencyFactory.getCurrency( "EUR" ), CurrencyFactory.getCurrency( "USD" ) )
            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate( lpLe, fiLe, CurrencyFactory.getCurrency( "GBP" ), CurrencyFactory.getCurrency( "JPY" ) )

            double tradeAmt = 1000
            Trade originalTrade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate )

            //call takeCredit based on the orderAmount
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( fiLe, lpLe, originalTrade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS )
            assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS )

            double avaliableLpAggLimit = getAvailableCreditLimit( lpTpForFi, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            double avaliableFiAggLimit = getAvailableCreditLimit( fiTpForLp, spotDate, GROSS_NOTIONAL_CLASSIFICATION )

            AmountOfInstrument availableLpLimit = CreditUtilizationManagerC.getInstance().getAvailableLimitAmountOfInstrument( lpLe, lpTpForFi, fiOrg, spotDate )
            AmountOfInstrument availableFiLimit = CreditUtilizationManagerC.getInstance().getAvailableLimitAmountOfInstrument( fiLe, fiTpForLp, lpOrg, spotDate )
            AmountOfInstrument availableLimit = CreditUtilizationManagerC.getInstance().getAvailableBilateralCreditLimitAmountOfInstrument( lpLe, fiLe, spotDate )
            log( "availableLpLimit=" + availableLpLimit + ",availableFiLimit=" + availableFiLimit + ",availableLimit=" + availableLimit )
            log( "avaliableLpAggLimit=" + avaliableLpAggLimit + ",avaliableFiAggLimit=" + avaliableFiAggLimit )

            // create fill trades
            double fillAmt1 = 200
            double fillAmt2 = 200
            Trade fillTrade1 = prepareSingleLegTrade( fillAmt1, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate )
            Trade fillTrade2 = prepareSingleLegTrade( fillAmt2, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate )

            Collection<Trade> fillTradeColl = new ArrayList<Trade>()
            fillTradeColl.add( fillTrade1 )
            fillTradeColl.add( fillTrade2 )

            CreditWorkflowMessage cwm2 = creditMgr.updateBilateralMultiFillCredit( fiLe, lpLe, originalTrade, fillTradeColl, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS )
            assertEquals( "Undo Message success status=" + cwm2.getStatus(), cwm2.getStatus(), MessageStatus.SUCCESS )

            double avaliableLpAggLimit1 = getAvailableCreditLimit( lpTpForFi, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            double avaliableFiAggLimit1 = getAvailableCreditLimit( fiTpForLp, spotDate, GROSS_NOTIONAL_CLASSIFICATION )


            AmountOfInstrument availableLpLimit1 = CreditUtilizationManagerC.getInstance().getAvailableLimitAmountOfInstrument( lpLe, lpTpForFi, fiOrg, spotDate )
            AmountOfInstrument availableFiLimit1 = CreditUtilizationManagerC.getInstance().getAvailableLimitAmountOfInstrument( fiLe, fiTpForLp, lpOrg, spotDate )
            AmountOfInstrument availableLimit1 = CreditUtilizationManagerC.getInstance().getAvailableBilateralCreditLimitAmountOfInstrument( lpLe, fiLe, spotDate )

            log( "availableLpLimit1=" + availableLpLimit1 + ",availableFiLimit1=" + availableFiLimit1 + ",availableLimit1=" + availableLimit1 )
            log( "avaliableLpAggLimit1=" + avaliableLpAggLimit1 + ",avaliableFiAggLimit1=" + avaliableFiAggLimit1 )
            /*assertEquals( "aggregate LP limits should be sum of offset", avaliableLpAggLimit == avaliableLpAggLimit1 + (tradeAmt-fillAmt1-fillAmt2), true )
            assertEquals( "aggregate FI limits should be sum of offset", avaliableFiAggLimit == avaliableFiAggLimit1 + (tradeAmt-fillAmt1-fillAmt2), true )*/

            assertEquals( "availableLpLimit should not be same.", Math.abs( availableLpLimit.getAmount() - availableLpLimit1.getAmount() ) > CREDIT_CALCULATION_MINIMUM, true )
            assertEquals( "availableFiLimit should not be same.", Math.abs( availableFiLimit.getAmount() - availableFiLimit1.getAmount() ) > CREDIT_CALCULATION_MINIMUM, true )
            assertEquals( "availableLimit should not be same.", Math.abs( availableLimit.getAmount() - availableLimit1.getAmount() ) > CREDIT_CALCULATION_MINIMUM, true )

            assertEquals( "availableLpLimit should not be same.", Math.abs( availableLpLimit.getAmount() + ( tradeAmt - fillAmt1 - fillAmt2 ) - availableLpLimit1.getAmount() ) < CREDIT_CALCULATION_MINIMUM, true )
            assertEquals( "availableFiLimit should not be same.", Math.abs( availableFiLimit.getAmount() + ( tradeAmt - fillAmt1 - fillAmt2 ) - availableFiLimit1.getAmount() ) < CREDIT_CALCULATION_MINIMUM, true )
        }
        catch ( Exception e )
        {
            e.printStackTrace()
            fail()
        }
        finally
        {
            removeCreditLimitSubscriptions( lpLe, lpTpForFi )
            removeCreditLimitSubscriptions( fiLe, fiTpForLp )
        }
    }

    void testMultiFillCreditUtilizationInclTradeInDealCollection()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )
            removeExistingCreditUtilizationEvents( fiOrg )

            creditAdminSvc.setCreditLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, ********, CreditUtilC.getCounterpartyCreditLimitCurrency( lpOrg, fiOrg, lpTpForFi ) )
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 20000000 )

            // subscribe for couple of currency pair updates.
            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate( lpLe, fiLe, CurrencyFactory.getCurrency( "EUR" ), CurrencyFactory.getCurrency( "USD" ) )
            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate( lpLe, fiLe, CurrencyFactory.getCurrency( "GBP" ), CurrencyFactory.getCurrency( "JPY" ) )

            double tradeAmt = 1000
            Trade originalTrade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate )

            //call takeCredit based on the orderAmount
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( fiLe, lpLe, originalTrade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS )
            assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS )

            double avaliableLpAggLimit = getAvailableCreditLimit( lpTpForFi, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            double avaliableFiAggLimit = getAvailableCreditLimit( fiTpForLp, spotDate, GROSS_NOTIONAL_CLASSIFICATION )

            AmountOfInstrument availableLpLimit = CreditUtilizationManagerC.getInstance().getAvailableLimitAmountOfInstrument( lpLe, lpTpForFi, fiOrg, spotDate )
            AmountOfInstrument availableFiLimit = CreditUtilizationManagerC.getInstance().getAvailableLimitAmountOfInstrument( fiLe, fiTpForLp, lpOrg, spotDate )
            AmountOfInstrument availableLimit = CreditUtilizationManagerC.getInstance().getAvailableBilateralCreditLimitAmountOfInstrument( lpLe, fiLe, spotDate )
            log( "availableLpLimit=" + availableLpLimit + ",availableFiLimit=" + availableFiLimit + ",availableLimit=" + availableLimit )
            log( "avaliableLpAggLimit=" + avaliableLpAggLimit + ",avaliableFiAggLimit=" + avaliableFiAggLimit )

            // create fill trades
            double fillAmt1 = 200
            double fillAmt2 = 200
            updateTradeAmount( originalTrade, fillAmt1, true, false )
            Trade fillTrade1 = prepareSingleLegTrade( fillAmt2, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate )

            Collection<Trade> fillTradeColl = new ArrayList<Trade>()
            // add original trade also to the collection
            fillTradeColl.add( originalTrade )
            fillTradeColl.add( fillTrade1 )

            CreditWorkflowMessage cwm2 = creditMgr.updateBilateralMultiFillCredit( fiLe, lpLe, originalTrade, fillTradeColl, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS )
            assertEquals( "Undo Message success status=" + cwm2.getStatus(), cwm2.getStatus(), MessageStatus.SUCCESS )

            double avaliableLpAggLimit1 = getAvailableCreditLimit( lpTpForFi, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            double avaliableFiAggLimit1 = getAvailableCreditLimit( fiTpForLp, spotDate, GROSS_NOTIONAL_CLASSIFICATION )


            AmountOfInstrument availableLpLimit1 = CreditUtilizationManagerC.getInstance().getAvailableLimitAmountOfInstrument( lpLe, lpTpForFi, fiOrg, spotDate )
            AmountOfInstrument availableFiLimit1 = CreditUtilizationManagerC.getInstance().getAvailableLimitAmountOfInstrument( fiLe, fiTpForLp, lpOrg, spotDate )
            AmountOfInstrument availableLimit1 = CreditUtilizationManagerC.getInstance().getAvailableBilateralCreditLimitAmountOfInstrument( lpLe, fiLe, spotDate )

            log( "availableLpLimit1=" + availableLpLimit1 + ",availableFiLimit1=" + availableFiLimit1 + ",availableLimit1=" + availableLimit1 )
            log( "avaliableLpAggLimit1=" + avaliableLpAggLimit1 + ",avaliableFiAggLimit1=" + avaliableFiAggLimit1 )
            /*assertEquals( "aggregate LP limits should be sum of offset", avaliableLpAggLimit == avaliableLpAggLimit1 + (tradeAmt-fillAmt1-fillAmt2), true )
            assertEquals( "aggregate FI limits should be sum of offset", avaliableFiAggLimit == avaliableFiAggLimit1 + (tradeAmt-fillAmt1-fillAmt2), true )*/

            assertEquals( "availableLpLimit should not be same.", Math.abs( availableLpLimit.getAmount() - availableLpLimit1.getAmount() ) > CREDIT_CALCULATION_MINIMUM, true )
            assertEquals( "availableFiLimit should not be same.", Math.abs( availableFiLimit.getAmount() - availableFiLimit1.getAmount() ) > CREDIT_CALCULATION_MINIMUM, true )
            assertEquals( "availableLimit should not be same.", Math.abs( availableLimit.getAmount() - availableLimit1.getAmount() ) > CREDIT_CALCULATION_MINIMUM, true )

            assertEquals( "availableLpLimit should not be same.", Math.abs( availableLpLimit.getAmount() + ( tradeAmt - fillAmt1 - fillAmt2 ) - availableLpLimit1.getAmount() ) < CREDIT_CALCULATION_MINIMUM, true )
            assertEquals( "availableFiLimit should not be same.", Math.abs( availableFiLimit.getAmount() + ( tradeAmt - fillAmt1 - fillAmt2 ) - availableFiLimit1.getAmount() ) < CREDIT_CALCULATION_MINIMUM, true )
        }
        catch ( Exception e )
        {
            e.printStackTrace()
            fail()
        }
        finally
        {
            removeCreditLimitSubscriptions( lpLe, lpTpForFi )
            removeCreditLimitSubscriptions( fiLe, fiTpForLp )
        }
    }

    void testMultiFillCreditUtilizationInclTradeInDealCollectionExternalTx()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )
            removeExistingCreditUtilizationEvents( fiOrg )

            creditAdminSvc.setCreditLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, ********, CreditUtilC.getCounterpartyCreditLimitCurrency( lpOrg, fiOrg, lpTpForFi ) )
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 20000000 )

            // subscribe for couple of currency pair updates.
            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate( lpLe, fiLe, CurrencyFactory.getCurrency( "EUR" ), CurrencyFactory.getCurrency( "USD" ) )
            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate( lpLe, fiLe, CurrencyFactory.getCurrency( "GBP" ), CurrencyFactory.getCurrency( "JPY" ) )

            double tradeAmt = 1000
            Trade originalTrade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate )

            //call takeCredit based on the orderAmount
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( fiLe, lpLe, originalTrade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS )
            assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS )

            double avaliableLpAggLimit = getAvailableCreditLimit( lpTpForFi, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            double avaliableFiAggLimit = getAvailableCreditLimit( fiTpForLp, spotDate, GROSS_NOTIONAL_CLASSIFICATION )

            AmountOfInstrument availableLpLimit = CreditUtilizationManagerC.getInstance().getAvailableLimitAmountOfInstrument( lpLe, lpTpForFi, fiOrg, spotDate )
            AmountOfInstrument availableFiLimit = CreditUtilizationManagerC.getInstance().getAvailableLimitAmountOfInstrument( fiLe, fiTpForLp, lpOrg, spotDate )
            AmountOfInstrument availableLimit = CreditUtilizationManagerC.getInstance().getAvailableBilateralCreditLimitAmountOfInstrument( lpLe, fiLe, spotDate )
            log( "availableLpLimit=" + availableLpLimit + ",availableFiLimit=" + availableFiLimit + ",availableLimit=" + availableLimit )
            log( "avaliableLpAggLimit=" + avaliableLpAggLimit + ",avaliableFiAggLimit=" + avaliableFiAggLimit )

            // create fill trades
            double fillAmt1 = 200
            double fillAmt2 = 200
            updateTradeAmount( originalTrade, fillAmt1, true, false )
            Trade fillTrade1 = prepareSingleLegTrade( fillAmt2, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate )

            Collection<Trade> fillTradeColl = new ArrayList<Trade>()
            // add original trade also to the collection
            fillTradeColl.add( originalTrade )
            fillTradeColl.add( fillTrade1 )

            IdcTransaction tx = initTransaction( true )
            CreditWorkflowMessage cwm2 = creditMgr.updateBilateralMultiFillCredit( fiLe, lpLe, originalTrade, fillTradeColl, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS )
            commitTransaction( tx )

            assertEquals( "Undo Message success status=" + cwm2.getStatus(), cwm2.getStatus(), MessageStatus.SUCCESS )

            double avaliableLpAggLimit1 = getAvailableCreditLimit( lpTpForFi, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            double avaliableFiAggLimit1 = getAvailableCreditLimit( fiTpForLp, spotDate, GROSS_NOTIONAL_CLASSIFICATION )


            AmountOfInstrument availableLpLimit1 = CreditUtilizationManagerC.getInstance().getAvailableLimitAmountOfInstrument( lpLe, lpTpForFi, fiOrg, spotDate )
            AmountOfInstrument availableFiLimit1 = CreditUtilizationManagerC.getInstance().getAvailableLimitAmountOfInstrument( fiLe, fiTpForLp, lpOrg, spotDate )
            AmountOfInstrument availableLimit1 = CreditUtilizationManagerC.getInstance().getAvailableBilateralCreditLimitAmountOfInstrument( lpLe, fiLe, spotDate )

            log( "availableLpLimit1=" + availableLpLimit1 + ",availableFiLimit1=" + availableFiLimit1 + ",availableLimit1=" + availableLimit1 )
            log( "avaliableLpAggLimit1=" + avaliableLpAggLimit1 + ",avaliableFiAggLimit1=" + avaliableFiAggLimit1 )
            /*assertEquals( "aggregate LP limits should be sum of offset", avaliableLpAggLimit == avaliableLpAggLimit1 + (tradeAmt-fillAmt1-fillAmt2), true )
            assertEquals( "aggregate FI limits should be sum of offset", avaliableFiAggLimit == avaliableFiAggLimit1 + (tradeAmt-fillAmt1-fillAmt2), true )*/

            assertEquals( "availableLpLimit should not be same.", Math.abs( availableLpLimit.getAmount() - availableLpLimit1.getAmount() ) > CREDIT_CALCULATION_MINIMUM, true )
            assertEquals( "availableFiLimit should not be same.", Math.abs( availableFiLimit.getAmount() - availableFiLimit1.getAmount() ) > CREDIT_CALCULATION_MINIMUM, true )
            assertEquals( "availableLimit should not be same.", Math.abs( availableLimit.getAmount() - availableLimit1.getAmount() ) > CREDIT_CALCULATION_MINIMUM, true )

            assertEquals( "availableLpLimit should not be same.", Math.abs( availableLpLimit.getAmount() + ( tradeAmt - fillAmt1 - fillAmt2 ) - availableLpLimit1.getAmount() ) < CREDIT_CALCULATION_MINIMUM, true )
            assertEquals( "availableFiLimit should not be same.", Math.abs( availableFiLimit.getAmount() + ( tradeAmt - fillAmt1 - fillAmt2 ) - availableFiLimit1.getAmount() ) < CREDIT_CALCULATION_MINIMUM, true )
        }
        catch ( Exception e )
        {
            e.printStackTrace()
            fail()
        }
        finally
        {
            removeCreditLimitSubscriptions( lpLe, lpTpForFi )
            removeCreditLimitSubscriptions( fiLe, fiTpForLp )
        }
    }

    void testInMemoryCreditUtilizationUpdate()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )
            removeExistingCreditUtilizationEvents( fiOrg )
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 2000000 )
            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 )
            double tradeAmt = 1000
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate )
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, fiOrg, lpTpForFi )
            assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS )


            Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate )
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, fiOrg, lpTpForFi )
            assertEquals( "Message success status=" + cwm1.getStatus(), cwm1.getStatus(), MessageStatus.SUCCESS )
            validateCreditUtilizationEvents( cwm )
        }
        catch ( Exception e )
        {
            e.printStackTrace()
            fail()
        }
    }

    void testSimpleCreditWorkflow()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )
            removeExistingCreditUtilizationEvents( fiOrg )
            double tradeAmt = 1000
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate )
            Collection<CreditUtilization> creditUtils = CreditUtilizationManagerC.getInstance().getCreditUtilizations( lpLe, fiOrg, lpTpForFi, spotDate, CreditLimitConstants.TRADE_EVENT )
            for ( CreditUtilization cu : creditUtils )
            {
                log( "credit util before credit take=" + cu + ",usedAmt=" + cu.getUsedAmount() + ",availableAmt=" + cu.getAvailableMarginReserveAmount() )
            }
            double availableAmtBeforeCreditTake = getAvailableCreditLimit( lpTpForFi, spotDate )
            log( "creditUtils=" + creditUtils + ",availableAmtBeforeCreditTake=" + availableAmtBeforeCreditTake )
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, fiOrg, lpTpForFi )
            for ( CreditUtilizationEvent cue : cwm.getCreditUtilizationEvents() )
            {
                log( "credit util from the workflow message=" + cue.getCreditUtilization() + ",usedAmt=" + cue.getCreditUtilization().getUsedAmount() + ",availableAmt=" + cue.getCreditUtilization().getAvailableMarginReserveAmount() )
            }
            Collection<CreditUtilization> creditUtilsAfter = CreditUtilizationManagerC.getInstance().getCreditUtilizations( lpLe, fiOrg, lpTpForFi, spotDate, CreditLimitConstants.TRADE_EVENT )
            double availableAmtAfterCreditTake = getAvailableCreditLimit( lpTpForFi, spotDate )
            log( "creditUtilsAfter=" + creditUtilsAfter + ",availableAmtAfterCreditTake=" + availableAmtAfterCreditTake )
            for ( CreditUtilization cu : creditUtilsAfter )
            {
                log( "credit util after credit take=" + cu + ",usedAmt=" + cu.getUsedAmount() + ",availableAmt=" + cu.getAvailableMarginReserveAmount() )
            }
            assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS )

            sleepFor( 5000 )
            Collection<CreditUtilization> creditUtilsAfterPeriodicUpdate = CreditUtilizationManagerC.getInstance().getCreditUtilizations( lpLe, fiOrg, lpTpForFi, spotDate, CreditLimitConstants.RATE_QUALIFICATION_EVENT )
            for ( CreditUtilization cu : creditUtilsAfterPeriodicUpdate )
            {
                log( "credit util after periodic update=" + cu + ",usedAmt=" + cu.getUsedAmount() + ",availableAmt=" + cu.getAvailableMarginReserveAmount() )
            }
            double availableAmtAfterPeriodicUpdate = getAvailableCreditLimit( lpTpForFi, spotDate )
            log( "creditUtilsAfterPeriodicUpdate=" + creditUtilsAfterPeriodicUpdate + ",availableAmtAfterPeriodicUpdate=" + availableAmtAfterPeriodicUpdate )
        }
        catch ( Exception e )
        {
            fail()
            e.printStackTrace()
        }
    }

    void testTradingSuspension()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )
            removeExistingCreditUtilizationEvents( fiOrg )
            double newDailyLimit = 1000000
            double newGrossLimit = 2000000
            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, newDailyLimit )
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, newGrossLimit )

            double newSuspensionPercentage = 90
            creditAdminSvc.setSuspensionPercentage( lpOrg, lpTpForFi, newSuspensionPercentage )
            double tradeAmt = newDailyLimit + ( 200 )
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate )
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, fiOrg, lpTpForFi )
            assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.FAILURE )

            double newTradeAmt = newDailyLimit
            Trade trade1 = prepareSingleLegTrade( newTradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate )
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade1, lpLe, fiOrg, lpTpForFi )
            assertEquals( "Message success status=" + cwm1.getStatus(), cwm1.getStatus(), MessageStatus.SUCCESS )

            // release the limit
            creditMgr.undoCredit( trade1, lpLe, fiOrg, lpTpForFi )
        }
        catch ( Exception e )
        {
            e.printStackTrace()
            fail()
        }
    }

    void testCreditRelationshipChanges()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )
            removeExistingCreditUtilizationEvents( ddOrg )

            creditAdminSvc.setLegalEntityExposureLevel( lpOrg, ddOrg )

            // initialize the credit enabled flag
            creditAdminSvc.setCreditEnabled( lpOrg, true )
            creditAdminSvc.setCreditEnabled( lpOrg, lpTpForDd, true )

            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 1000000 )
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 )

            //remove the trading relation
            creditAdminSvc.removeCreditRelationship( lpOrg, lpTpForDd )

            //populate enable credit
            CreditUtilizationCache creditCache = CreditUtilizationManagerC.getInstance().getCreditUtilizationCache()
            boolean isCreditEnabled = creditCache.isCreditEnabled( lpOrg, lpTpForDd )
            assertEquals( "With no trading relation creditEnable should be false.", false, isCreditEnabled )

            //establish the trading relationship
            creditAdminSvc.establishCreditRelationship( lpOrg, lpTpForDd )

            //reinit should set enable credit to true but the map should still contain false
            boolean isCreditEnabledAfterReinit = creditCache.isCreditEnabled( lpOrg, lpTpForDd )
            assertEquals( "After trading relation set and reinit creditEnable should be true.", true, isCreditEnabledAfterReinit )
        }
        catch ( Exception e )
        {
            fail( "testCreditRelationshipChanges", e )
        }
        finally
        {
            creditAdminSvc.establishCreditRelationship( lpOrg, lpTpForDd )
            creditAdminSvc.setOrganizationExposureLevel( lpOrg, ddOrg )
        }
    }


    void testCreditSuccessWIthLessSevereBreach()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )
            removeExistingCreditUtilizationEvents( fiOrg )
            double limit = ********   // 10M

            Collection<CreditUtilizationCalculator> supportedCalcs = CreditUtilC.getSupportedNonAccountAggregateNettingMethodologies()
            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, limit )
            // disable the daily credit limits
            for ( CreditUtilizationCalculator calculator : supportedCalcs )
            {
                log( "Test credit limit breach for " + calculator )
                removeExistingCreditUtilizationEvents( lpOrg )
                removeExistingCreditUtilizationEvents( fiOrg )

                limit = ********
                setCalcAndLimit( lpOrg, lpTpForFi, CreditUtilC.getCreditLimitClassification( calculator ), calculator, limit )
                creditAdminSvc.setSuspensionPercentage( lpOrg, lpTpForFi, 100 )

                double availableAmtBeforeCreditTake = getAvailableCreditLimit( lpTpForFi, spotDate )
                log( "****************** availableAmtBeforeCreditTake=" + availableAmtBeforeCreditTake )

                // do a trade to exhaust almost entire credit limit
                double tradeAmt = limit
                Trade trade = prepareSingleLegTrade( tradeAmt * 0.95, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate )
                CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, fiOrg, lpTpForFi )
                assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS )

                // the new available limit should be very close to zero limits
                double availableAmtAfterCreditTake = getAvailableCreditLimit( lpTpForFi, spotDate )
                log( "availableAmtAfterCreditTake=" + availableAmtAfterCreditTake )

                //now set the limit to lower limits
                limit = 8000000   // 8M
                setCalcAndLimit( lpOrg, lpTpForFi, CreditUtilC.getCreditLimitClassification( calculator ), calculator, limit )

                // with the new reduded limtis the available limit should be -ve
                double availableAmtAfterLimitChangeBeforeCredit = getAvailableCreditLimit( lpTpForFi, spotDate )
                log( "availableAmtAfterLimitChangeBeforeCredit=" + availableAmtAfterLimitChangeBeforeCredit )
                assertEquals( "After the limit change, the available should be negative.", availableAmtAfterLimitChangeBeforeCredit < 0, true )

                //do another trade which redues the utilization amount based on the netting
                tradeAmt = 1000000 // 1M
                trade = prepareSingleLegTrade( tradeAmt, true, true, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate )
                cwm = creditMgr.takeCredit( trade, lpLe, fiOrg, lpTpForFi )

                double availableAmtAfterLimitChangeAfterCredit = getAvailableCreditLimit( lpTpForFi, spotDate )
                log( "availableAmtAfterLimitChangeAfterCredit=" + availableAmtAfterLimitChangeAfterCredit )

                if ( CreditUtilC.isNettingCalculator( calculator ) )
                {
                    log( "Nettting calculator used." )
                    if ( availableAmtAfterLimitChangeAfterCredit > availableAmtAfterLimitChangeBeforeCredit )
                    {
                        assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS )
                    }
                    else
                    {
                        assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.FAILURE )
                    }
                }
                else
                {
                    log( "No Nettting calculator used. The trade should always reject" )
                    assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.FAILURE )
                }
            }
        }
        catch ( Exception e )
        {
            e.printStackTrace()
            fail()
        }
    }

    void testDisabledCreditLimitRules()
    {
        double newDailyLimit = 1000000
        double newGrossLimit = 2000000
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )
            removeExistingCreditUtilizationEvents( fiOrg )
            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, null, newDailyLimit )
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, null, newGrossLimit )

            double tradeAmt = 1000
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate )
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, fiOrg, lpTpForFi )
            assertEquals( "Message failure status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.FAILURE )
        }
        catch ( Exception e )
        {
            e.printStackTrace()
            fail()
        }
        finally
        {
            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, newDailyLimit )
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, newGrossLimit )
        }
    }

    void testObsoleteMaturityDates()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )
            removeExistingCreditUtilizationEvents( fiOrg )
            double newDailyLimit = 1000000
            double newGrossLimit = 2000000
            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, newDailyLimit )
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, newGrossLimit )

            double tradeAmt = 1000
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate.subtractDays( 10 ) )
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, fiOrg, lpTpForFi )
            assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.FAILURE )
        }
        catch ( Exception e )
        {
            e.printStackTrace()
            fail()
        }
    }

    void testMultiCreditRuleTakeCreditOnSwapTrade()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )
            removeExistingCreditUtilizationEvents( fiOrg )
            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 )
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 )
            double limitOnNearDate = getAvailableCreditLimit( lpTpForFi, spotDate )
            double limitOnFarDate = getAvailableCreditLimit( lpTpForFi, spotDate.addDays( 2 ) )
            log( "near date credit limit before take Credit: " + limitOnNearDate )
            log( "far date credit limit before take Credit : " + limitOnFarDate )
            double tradeAmt = 1000
            Trade trade = prepareSwapTrade( tradeAmt, tradeAmt, true, false, false, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], bidRates[0], spotDate, spotDate.addDays( 2 ) )
            CreditWorkflowMessage cwm = creditMgr.takeCredit( trade, lpLe, fiOrg, lpTpForFi )
            double limit1OnNearDate = getAvailableCreditLimit( lpTpForFi, spotDate )
            double limit1OnFarDate = getAvailableCreditLimit( lpTpForFi, spotDate.addDays( 2 ) )
            log( "near date credit limit  after take Credit : " + limit1OnNearDate + " success : " + cwm.getStatus() )
            log( "far date credit limit  after take Credit : " + limit1OnFarDate + " success : " + cwm.getStatus() )
            assertEquals( "success=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS )
            assertEquals( "1000 was taken :", true, Math.abs( limitOnNearDate - limit1OnNearDate - tradeAmt - tradeAmt ) < MINIMUM )
            assertEquals( "1000 was taken :", true, Math.abs( limitOnFarDate - limit1OnFarDate - tradeAmt - tradeAmt ) < MINIMUM )
            log( "credit utilization events=" + cwm.getCreditUtilizationEvents() )
            assertEquals( "There should be 4 credit utilization events.", cwm.getCreditUtilizationEvents().size(), 4 )
            validateCreditUtilizationEvents( cwm )
        }
        catch ( Exception e )
        {
            e.printStackTrace()
            fail()
        }
    }

    void testCreditUtilizationEventReadObject()
    {
        Collection cues = new ArrayList()
        try
        {
            init( lpUser )
            IdcTransaction tx = initTransaction( true )
            CreditUtilizationEvent cue = ( CreditUtilizationEvent ) CreditLimitFactory.newCreditUtilizationEvent().getRegisteredObject()
            cue.setNamespace( lpUser.getNamespace() )
            cue.setUsedAmount( 1000 )
            cues.add( cue )
            commitTransaction( tx )

            // get the credit utilization event.
            CreditUtilizationEvent cue1 = ( CreditUtilizationEvent ) cues.toArray()[0]
            log( "cue from cache=" + cue1 )

            CreditUtilizationEvent cue2 = ( CreditUtilizationEvent ) CreditUtilC.readObject( cue1 )
            log( "cue after read object=" + cue2 )

            IdcTransaction tx1 = initTransaction( true )
            CreditUtilizationEvent registeredCue = ( CreditUtilizationEvent ) cue2.getRegisteredObject()
            registeredCue.setUsedAmount( 2000 )
            commitTransaction( tx1 )
            log( "cue after commit=" + cue2 )
        }
        catch ( Exception e )
        {
            e.printStackTrace()
            fail()
        }
    }

    void testBilateralCreditTakeWithExcessCreditUtilizationAllowed()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )
            removeExistingCreditUtilizationEvents( fiOrg )

            creditAdminSvc.setCreditLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, ********, CreditUtilC.getCounterpartyCreditLimitCurrency( lpOrg, fiOrg, lpTpForFi ) )
            creditAdminSvc.setCreditLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, 20000000, CreditUtilC.getCounterpartyCreditLimitCurrency( lpOrg, fiOrg, lpTpForFi ) )

            // subscribe for couple of currency pair updates.
            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate( lpLe, fiLe, CurrencyFactory.getCurrency( "EUR" ), CurrencyFactory.getCurrency( "USD" ) )
            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate( lpLe, fiLe, CurrencyFactory.getCurrency( "GBP" ), CurrencyFactory.getCurrency( "JPY" ) )

            Trade trade = prepareSingleLegTrade( 30000000, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate )
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( fiLe, lpLe, trade, CreditWorkflowRidersAllowExcess.CREDIT_WORKFLOW_RIDERS_ALLOW_EXCESS )
            assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS )
        }
        catch ( Exception e )
        {
            e.printStackTrace()
            fail()
        }
        finally
        {
            removeCreditLimitSubscriptions( lpLe, lpTpForFi )
            removeCreditLimitSubscriptions( fiLe, fiTpForLp )
        }
    }

    void testBilateralCreditTradingPartyValidation()
    {
        try
        {
            init( fiUser )

            Trade trade = prepareSingleLegTrade( 30000000, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate )
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( fiLe, ddLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS )
            assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.FAILURE )
            assertEquals( "Message error.", ( ( ErrorMessage ) cwm.getErrors().toArray()[0] ).getCode(), CreditLimit.ERROR_INVALID_TRADING_PARTY )
            CreditWorkflowMessage cwm1 = creditMgr.undoBilateralCredit( fiLe, ddLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS )
            assertEquals( "Message success status=" + cwm1.getStatus(), cwm1.getStatus(), MessageStatus.FAILURE )
        }
        catch ( Exception e )
        {
            fail( "testBilateralCreditTradingPartyValidation", e )
        }
    }

    void testBilateralCreditMaxTenorValidationFailure()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )
            removeExistingCreditUtilizationEvents( fiOrg )

            creditAdminSvc.setCreditLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, ********, CreditUtilC.getCounterpartyCreditLimitCurrency( lpOrg, fiOrg, lpTpForFi ) )
            creditAdminSvc.setCreditLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, 20000000, CreditUtilC.getCounterpartyCreditLimitCurrency( lpOrg, fiOrg, lpTpForFi ) )
            // set the maxTenor limit to 5 days and value date 10 days
            creditAdminSvc.setMaximumTenor( lpOrg, lpTpForFi, new Tenor( "5D" ) )

            double limitBefore = getAvailableCreditLimit( lpTpForFi, spotDate )
            log( "credit limit before take Credit: " + limitBefore )

            Trade trade = prepareSingleLegTrade( 10000, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate.addDays( 10 ) )
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( fiLe, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS )

            double limitAfter = getAvailableCreditLimit( lpTpForFi, spotDate )
            log( "credit limit after take Credit: " + limitAfter )

            assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.FAILURE )
            //todo: cwm sets the error code from first cwm - not aggregate results
            //assertEquals( "Message error.", ( ( ErrorMessage ) cwm.getErrors().toArray()[0] ).getCode(), CreditLimit.ERROR_EXCEED_MAXIMUM_TENOR )
            assertEquals( "The credit limit should not be changed.", true, Math.abs( limitBefore - limitAfter ) < MINIMUM )
        }
        catch ( Exception e )
        {
            fail( "testBilateralCreditMaxTenorValidationFailure", e )
        }
        finally
        {
            creditAdminSvc.setMaximumTenor( lpOrg, lpTpForFi, null )
        }
    }

    void testBilateralCreditMinTenorValidationFailure()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )
            removeExistingCreditUtilizationEvents( fiOrg )

            creditAdminSvc.setCreditLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, ********, CreditUtilC.getCounterpartyCreditLimitCurrency( lpOrg, fiOrg, lpTpForFi ) )
            creditAdminSvc.setCreditLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, 20000000, CreditUtilC.getCounterpartyCreditLimitCurrency( lpOrg, fiOrg, lpTpForFi ) )
            // set the minTenor limit to 5 days and value date as spot
            creditAdminSvc.setMinimumTenor( lpOrg, lpTpForFi, new Tenor( "5D" ) )

            double limitBefore = getAvailableCreditLimit( lpTpForFi, spotDate )
            log( "credit limit before take Credit: " + limitBefore )

            Trade trade = prepareSingleLegTrade( 10000, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate )
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( fiLe, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS )

            double limitAfter = getAvailableCreditLimit( lpTpForFi, spotDate )
            log( "credit limit after take Credit: " + limitAfter )

            assertEquals( "Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.FAILURE )
            assertEquals( "The credit limit should not be changed.", true, Math.abs( limitBefore - limitAfter ) < MINIMUM )
        }
        catch ( Exception e )
        {
            fail( "testBilateralCreditMaxTenorValidationFailure", e )
        }
        finally
        {
            creditAdminSvc.setMinimumTenor( lpOrg, lpTpForFi, null )
        }
    }

    void testBilateralCreditTakeSubscriptionsWithOptimization()
    {
        try
        {
            testBilateralCreditTakeSubscriptionsWithOptimization( true )
            Thread.sleep( 5000 )
            testBilateralCreditTakeSubscriptionsWithOptimization( false )
        }
        catch ( Exception e )
        {

        }
    }

    private void testBilateralCreditTakeSubscriptionsWithOptimizationWithCreditEnabled( boolean creditEnabled )
    {
        Currency EUR = CurrencyFactory.getCurrency( "EUR" )
        Currency USD = CurrencyFactory.getCurrency( "USD" )
        Currency CAD = CurrencyFactory.getCurrency( "CAD" )
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )
            removeExistingCreditUtilizationEvents( fiOrg )
            removeCreditLimitSubscriptions( lpLe, lpTpForFi )
            removeCreditLimitSubscriptions( fiLe, fiTpForLp )

            creditAdminSvc.setCreditEnabled( lpOrg, creditEnabled )
            creditAdminSvc.setCreditEnabled( lpOrg, lpTpForFi, creditEnabled )
            creditAdminSvc.setCreditEnabled( fiOrg, creditEnabled )
            creditAdminSvc.setCreditEnabled( fiOrg, fiTpForLp, creditEnabled )

            creditAdminSvc.setCreditLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, ********0, CreditUtilC.getCounterpartyCreditLimitCurrency( lpOrg, fiOrg, lpTpForFi ) )
            creditAdminSvc.setCreditLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, 200000000, CreditUtilC.getCounterpartyCreditLimitCurrency( lpOrg, fiOrg, lpTpForFi ) )


            IdcDate eurUSDSpotDate = CreditUtilC.getSpotDate( EUR, USD ) == null ? spotDate : CreditUtilC.getSpotDate( EUR, USD )
            IdcDate usdCADSpotDate = CreditUtilC.getSpotDate( USD, CAD ) == null ? spotDate : CreditUtilC.getSpotDate( USD, CAD )

            // subscribe for couple of currency pair updates.
            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate( lpLe, fiLe, EUR, USD, true, true )
            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate( lpLe, fiLe, USD, CAD, true, true )
            Thread.sleep( 10000 )

            double[] bidLimit = [100000], offerLimit = [100000]
            double origBidLimit = bidLimit[0], origOfferLimit = offerLimit[0]
            boolean enable
            enable = CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( lpLe, fiLe, eurUSDSpotDate, EUR, USD, true, bidLimit, offerLimit, true )
            assertEquals( "The bilateral credit enabled should match.", enable, creditEnabled )
            assertEquals( "The available bid limit for EUR/USD should be same as quote limits.", origBidLimit == bidLimit[0], true )
            assertEquals( "The available offer limit for EUR/USD should be same as quote limits.", origOfferLimit == offerLimit[0], true )

            bidLimit = [********]
            offerLimit = [********]
            origBidLimit = bidLimit[0]
            origOfferLimit = offerLimit[0]
            enable = CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( lpLe, fiLe, eurUSDSpotDate, EUR, USD, true, bidLimit, offerLimit, true )
            assertEquals( "The bilateral credit enabled should match.", enable, creditEnabled )
            assertEquals( "The available bid limit for EUR/USD should not be same as quote limits.", origBidLimit == bidLimit[0], !creditEnabled )
            assertEquals( "The available offer limit for EUR/USD should not be same as quote limits.", origOfferLimit == offerLimit[0], !creditEnabled )

            //repeat the same test for USD/CAD ccy pair
            bidLimit = [100000]
            offerLimit = [100000]
            origBidLimit = bidLimit[0]
            origOfferLimit = offerLimit[0]
            enable = CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( lpLe, fiLe, usdCADSpotDate, USD, CAD, true, bidLimit, offerLimit, true )
            assertEquals( "The bilateral credit enabled should match.", enable, creditEnabled )
            assertEquals( "The available bid limit for USD/CAD should be same as quote limits.", origBidLimit == bidLimit[0], true )
            assertEquals( "The available offer limit for USD/CAD should be same as quote limits.", origOfferLimit == offerLimit[0], true )

            bidLimit = [********]
            offerLimit = [********]
            origBidLimit = bidLimit[0]
            origOfferLimit = offerLimit[0]
            enable = CreditUtilizationManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( lpLe, fiLe, usdCADSpotDate, USD, CAD, true, bidLimit, offerLimit, true )
            assertEquals( "The bilateral credit enabled should match.", enable, creditEnabled )
            assertEquals( "The available bid limit for USD/CAD should not be same as quote limits.", origBidLimit == bidLimit[0], !creditEnabled )
            assertEquals( "The available offer limit for USD/CAD should not be same as quote limits.", origOfferLimit == offerLimit[0], !creditEnabled )
        }
        catch ( Exception e )
        {
            e.printStackTrace()
            fail()
        }
        finally
        {
            CreditUtilizationManagerC.getInstance().unsubscribeBilateralCreditLimitUpdate( lpLe, fiLe, EUR, USD, true, true )
            CreditUtilizationManagerC.getInstance().unsubscribeBilateralCreditLimitUpdate( lpLe, fiLe, USD, CAD, true, true )
            removeCreditLimitSubscriptions( lpLe, lpTpForFi )
            removeCreditLimitSubscriptions( fiLe, fiTpForLp )
            creditAdminSvc.setCreditEnabled( lpOrg, true )
            creditAdminSvc.setCreditEnabled( lpOrg, lpTpForFi, true )
            creditAdminSvc.setCreditEnabled( fiOrg, true )
            creditAdminSvc.setCreditEnabled( fiOrg, fiTpForLp, true )
        }
    }

    void testCreditSubscriptionCleanupEOD()
    {
        Currency EUR = CurrencyFactory.getCurrency( "EUR" )
        Currency USD = CurrencyFactory.getCurrency( "USD" )
        Currency CAD = CurrencyFactory.getCurrency( "CAD" )
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )
            removeExistingCreditUtilizationEvents( fiOrg )
            removeCreditLimitSubscriptions( lpLe, lpTpForFi )
            removeCreditLimitSubscriptions( fiLe, fiTpForLp )

            creditAdminSvc.setCreditLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, ********0, CreditUtilC.getCounterpartyCreditLimitCurrency( lpOrg, fiOrg, lpTpForFi ) )
            creditAdminSvc.setCreditLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, 200000000, CreditUtilC.getCounterpartyCreditLimitCurrency( lpOrg, fiOrg, lpTpForFi ) )

            // 1. add credit limit subscriptions for a few currency pairs across diff value dates
            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate( lpLe, fiLe, EUR, USD, true, true, spotDate )
            CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate( lpLe, fiLe, USD, CAD, true, true, spotDate )

        }
        catch ( Exception e )
        {
            e.printStackTrace()
            fail()
        }
        finally
        {
            CreditUtilizationManagerC.getInstance().unsubscribeBilateralCreditLimitUpdate( lpLe, fiLe, EUR, USD, true, true )
            CreditUtilizationManagerC.getInstance().unsubscribeBilateralCreditLimitUpdate( lpLe, fiLe, USD, CAD, true, true )
            removeCreditLimitSubscriptions( lpLe, lpTpForFi )
            removeCreditLimitSubscriptions( fiLe, fiTpForLp )
        }
    }

    void testRebookWithOrgLevelExposure()
    {
        try
        {
            //set org level exposure
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )
            removeExistingCreditUtilizationEvents( fiOrg )
            creditAdminSvc.setOrganizationExposureLevel( lpOrg, fiOrg )
            creditAdminSvc.setOrganizationExposureLevel( fiOrg, lpOrg )

            creditAdminSvc.setCreditEnabled( lpOrg, false )
            creditAdminSvc.setCreditEnabled( lpOrg, lpTpForFi, false )
            creditAdminSvc.setCreditEnabled( fiOrg, false )
            creditAdminSvc.setCreditEnabled( fiOrg, fiTpForLp, false )


            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 )
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 )
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 )
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 )

            // do trade and check credit
            double tradeAmt = 10000
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate )
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( fiLe, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS )
            assertEquals( "success=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS )

            double dailyFILimit0 = getAvailableCreditLimit( lpTpForFi, spotDate, DAILY_SETTLEMENT_CLASSIFICATION )
            double dailyLPLimit0 = getAvailableCreditLimit( fiTpForLp, spotDate, DAILY_SETTLEMENT_CLASSIFICATION )
            double aggregateFILimit0 = getAvailableCreditLimit( lpTpForFi, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            double aggregateLPLimit0 = getAvailableCreditLimit( fiTpForLp, spotDate, GROSS_NOTIONAL_CLASSIFICATION )

            IdcTransaction tx = initTransaction( true )
            Trade regTrade = ( Trade ) trade.getRegisteredObject()
            // rebook the trade with a diff le
            creditMgr.undoBilateralCredit( fiLe, lpLe, regTrade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS )
            FXSingleLeg sLeg = ( FXSingleLeg ) trade
            sLeg.setCounterpartyB( lpLe2 )
            cwm = creditMgr.takeBilateralCredit( fiLe, lpLe2, regTrade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS )
            assertEquals( "success=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS )
            // check credit and it shouldnt change
            double dailyFILimit1 = getAvailableCreditLimit( lpTpForFi, spotDate, DAILY_SETTLEMENT_CLASSIFICATION )
            double dailyLPLimit1 = getAvailableCreditLimit( fiTpForLp, spotDate, DAILY_SETTLEMENT_CLASSIFICATION )
            double aggregateFILimit1 = getAvailableCreditLimit( lpTpForFi, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            double aggregateLPLimit1 = getAvailableCreditLimit( fiTpForLp, spotDate, GROSS_NOTIONAL_CLASSIFICATION )
            assertEquals( "dailyFILimit0=" + dailyFILimit0 + ",dailyFILimit1=" + dailyFILimit1 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyFILimit0 - dailyFILimit1 ) < CREDIT_CALCULATION_MINIMUM )
            assertEquals( "dailyLPLimit0=" + dailyLPLimit0 + ",dailyLPLimit1=" + dailyLPLimit1 + ",tradeAmt=" + tradeAmt, true, Math.abs( dailyLPLimit0 - dailyLPLimit1 ) < CREDIT_CALCULATION_MINIMUM )
            assertEquals( "aggregateFILimit0=" + aggregateFILimit0 + ",aggregateFILimit1=" + aggregateFILimit1 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateFILimit0 - aggregateFILimit1 ) < CREDIT_CALCULATION_MINIMUM )
            assertEquals( "aggregateLPLimit0=" + aggregateLPLimit0 + ",aggregateLPLimit1=" + aggregateLPLimit1 + ",tradeAmt=" + tradeAmt, true, Math.abs( aggregateLPLimit0 - aggregateLPLimit1 ) < CREDIT_CALCULATION_MINIMUM )
            commitTransaction( tx )
        }
        catch ( Exception e )
        {
            slog.error( "testMultiCreditRuleTakeCredit", e )
            fail()
        }
        finally
        {
            // finally - set the exposure back to le level
            creditAdminSvc.setLegalEntityExposureLevel( lpOrg, fiOrg )
            creditAdminSvc.setLegalEntityExposureLevel( fiOrg, lpOrg )

            creditAdminSvc.setCreditEnabled( lpOrg, false )
            creditAdminSvc.setCreditEnabled( lpOrg, lpTpForFi, false )
            creditAdminSvc.setCreditEnabled( fiOrg, false )
            creditAdminSvc.setCreditEnabled( fiOrg, fiTpForLp, false )
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 2000000 )
            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 )
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 )
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 )
        }
    }

    void testCreditUtilCacheSynchronizeNotificationWithStaleCcy()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )
            removeExistingCreditUtilizationEvents( fiOrg )

            creditAdminSvc.setCreditLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, ********, CreditUtilC.getCounterpartyCreditLimitCurrency( lpOrg, fiOrg, lpTpForFi ) )
            creditAdminSvc.setCreditLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, 20000000, CreditUtilC.getCounterpartyCreditLimitCurrency( lpOrg, fiOrg, lpTpForFi ) )

            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 2000000 )
            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 )
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 )
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 )


            assertTrue( CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().isCreditEnabled( lpOrg, lpTpForFi ) )
            assertTrue( CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().isCreditEnabled( fiOrg, fiTpForLp ) )
            double tradeAmt = 10000
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate )
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( fiLe, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS )
            log( "cwm.status=" + cwm.getStatus() )
            assertEquals( cwm.getStatus(), MessageStatus.SUCCESS )

            Set<CreditUtilization> utils = new HashSet<CreditUtilization>( 4 )

            // invoke remote notification functor to sync up the currency positions.
            HashMap<String, Object> propertiesMap = new HashMap<String, Object>( 4 )
            propertiesMap.put( RemoteTransactionNotification.REMOTE_ONLY_KEY, RemoteTransactionNotification.REMOTE_ONLY_VALUE )
            propertiesMap.put( CreditLimit.CREDIT_PROVIDER_ORGANIZATION, cwm.getOrganization().getShortName() )
            propertiesMap.put( CreditLimit.CREDIT_COUNTERPARTY, cwm.getTradingParty().getShortName() )
            propertiesMap.put( CreditLimitConstants.EVENT_PROPERTY, cwm.getEventName() )
            setCreditUtilizationEventGuid( cwm, propertiesMap )

            for ( CreditUtilizationEvent cue : cwm.getCreditUtilizationEvents() )
            {
                CreditUtilization cu = cue.getCreditUtilization()
                if ( cu.getCreditLimitRule().getClassification().isSameAs( GROSS_NOTIONAL_CLASSIFICATION ) )
                {
                    cu.getCurrencyPositions().removeCreditUtilizationEvent( cue )
                }
                else if ( cu.getCreditLimitRule().getClassification().isSameAs( DAILY_SETTLEMENT_CLASSIFICATION ) )
                {
                    cu.getCurrencyPositions().removeCreditUtilizationEvent( cue )
                }
                CreditUtilizationCacheSynchronizeNotificationFunctorMock.cueMap.put( cue.getGUID(), cue )
            }

            CreditUtilizationCacheSynchronizeNotificationFunctorC functor = new CreditUtilizationCacheSynchronizeNotificationFunctorMock()
            functor.onCommit( propertiesMap )

        }
        finally
        {
            CreditUtilizationCacheSynchronizeNotificationFunctorMock.cueMap.clear()
        }
    }

    void testBilateralCreditNoMethodology()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )
            removeExistingCreditUtilizationEvents( fiOrg )

            // set all the credit methodologies to null.
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, null, 1000000 )
            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, null, 1000000 )
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, null, 1000000 )
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, null, 1000000 )

            Trade trade = prepareSingleLegTrade( 10000, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate )
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( fiLe, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS )
            log( "cwm.status=" + cwm.getStatus() )
            assertEquals( "cwm.status should be failure", MessageStatus.FAILURE, cwm.getStatus() )

            // unilateral credit also should fail.
            CreditWorkflowMessage cwm1 = creditMgr.takeCredit( trade, lpLe, fiOrg, lpTpForFi )
            log( "cwm1.status=" + cwm1.getStatus() )
            assertEquals( "cwm1.status should be failure", MessageStatus.FAILURE, cwm1.getStatus() )

            CreditWorkflowMessage cwm2 = creditMgr.takeCredit( trade, fiLe, lpOrg, fiTpForLp )
            log( "cwm2.status=" + cwm2.getStatus() )
            assertEquals( "cwm2.status should be failure", MessageStatus.FAILURE, cwm2.getStatus() )
        }
        catch ( Exception e )
        {
            fail( "testBilateralCreditNoMethodology", e )
        }
        finally
        {
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 )
            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 )
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 )
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 )
        }
    }

    void testMultiCreditRuleTakeCreditFailure_Insufficient_CreditWithSubscriptions()
    {
        try
        {
            init( fiUser )
            removeExistingCreditUtilizationEvents( fiOrg )
            removeExistingCreditUtilizationEvents( lpOrg )
            IdcDate eurUsdSpotDate = CreditUtilC.getSpotDate( CurrencyFactory.getCurrency( "EUR" ), CurrencyFactory.getCurrency( "USD" ) )
            double limit0 = getAvailableCreditLimit( lpTpForFi, eurUsdSpotDate )

            // subscribe some currency pairs.
            Collection<String> ccyPairs = new ArrayList<String>()
            ccyPairs.add( "EUR/USD" )
            ccyPairs.add( "EUR/GBP" )
            ccyPairs.add( "USD/CHF" )
            ccyPairs.add( "CAD/JPY" )
            ccyPairs.add( "USD/CHF" )

            for ( String cp : ccyPairs )
            {
                CurrencyPair ccyPair = CurrencyFactory.getCurrencyPairFromString( cp )
                CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate( lpLe, fiLe, ccyPair.getBaseCurrency(  ), ccyPair.getVariableCurrency(  ), true, true, spotDate )
            }
            DealingLimitCollection subscriptions = CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().getCreditLimitSubscriptions( lpLe, lpTpForFi )
            log( "subscriptions=" + subscriptions )

            log( "testMultiCreditRuleTakeCreditFailure_Insufficient_Credit : credit limit  before take Credit : " + limit0 )
            Double totalLimit = getTotalCreditLimit( lpOrg, lpTpForFi )
            double tradeAmt = totalLimit * 10
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], eurUsdSpotDate )
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( fiLe, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS )
            double limit1 = getAvailableCreditLimit( lpTpForFi, eurUsdSpotDate )
            log( "testMultiCreditRuleTakeCreditFailure_Insufficient_Credit : credit limit  after take Credit : " + limit1 + " success : " + cwm.getStatus() )
            assertEquals( "testMultiCreditRuleTakeCreditFailure_Insufficient_Credit : 1000 was taken :", true, Math.abs( limit0 - limit1 ) < MINIMUM )
            assertEquals( "testMultiCreditRuleTakeCreditFailure_Insufficient_Credit : status :  ", cwm.getStatus(), MessageStatus.FAILURE )
            validateCreditUtilizationEvents( cwm )
        }
        catch ( Exception e )
        {
            slog.error( "testMultiCreditRuleTakeCreditFailure_Insufficient_CreditWithSubscriptions", e )
            fail()
        }
    }

    void testLeLevelOverrideMinTenor()
    {
        try
        {
            init(lpUser)
            removeExistingCreditUtilizationEvents(lpOrg)
            removeExistingCreditUtilizationEvents(fiOrg)


            creditAdminSvc.setOrganizationExposureLevel(lpOrg, fiOrg)

            setCalcAndLimit(lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 )
            setCalcAndLimit(lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 )
            setCalcAndLimit(fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 )
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 )

            creditAdminSvc.setCreditEnabled( fiOrg, false )

            Trade trade = prepareSingleLegTrade( 10000, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate )
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( fiLe, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS )

            assertEquals("Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS)


            // set the minTenor limit to 5 days and value date as spot
            creditAdminSvc.setMinimumTenor(lpOrg, lpTpForFi, new Tenor("5D"))

            Trade trade1 = prepareSingleLegTrade(10000, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate)
            CreditWorkflowMessage cwm1 = creditMgr.takeBilateralCredit(fiLe, lpLe, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS )

            assertEquals("Message success status=" + cwm1.getStatus(), cwm1.getStatus(), MessageStatus.FAILURE )

            // now set the legal entity level override.
            creditAdminSvc.setLEOverride( lpOrg, fiOrg, true )
            creditAdminSvc.setMinimumTenor(lpOrg, lpTpForFi, new Tenor("TOD"), true)
            creditAdminSvc.setOrgDefault(lpOrg, fiOrg, lpTpForFi, false )
            Trade trade2 = prepareSingleLegTrade(10000, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate)
            CreditWorkflowMessage cwm2 = creditMgr.takeBilateralCredit(fiLe, lpLe, trade2, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS )
            assertEquals("Message success status=" + cwm2.getStatus(), cwm2.getStatus(), MessageStatus.SUCCESS)

            creditAdminSvc.setMinimumTenor(lpOrg, lpTpForFi, new Tenor("1Y"), true)
            Trade trade3 = prepareSingleLegTrade(10000, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate)
            CreditWorkflowMessage cwm3 = creditMgr.takeBilateralCredit(fiLe, lpLe, trade3, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS)
            assertEquals("Message success status=" + cwm3.getStatus(), cwm3.getStatus(), MessageStatus.FAILURE)

            creditAdminSvc.setMinimumTenor(lpOrg, lpTpForFi, null, true)
            Trade trade4 = prepareSingleLegTrade(10000, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate)
            CreditWorkflowMessage cwm4 = creditMgr.takeBilateralCredit(fiLe, lpLe, trade4, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS)
            assertEquals("Message success status=" + cwm4.getStatus(), cwm4.getStatus(), MessageStatus.SUCCESS)


            creditAdminSvc.setOrgDefault(lpOrg, fiOrg, lpTpForFi, true)
            Trade trade5 = prepareSingleLegTrade(10000, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate)
            CreditWorkflowMessage cwm5 = creditMgr.takeBilateralCredit(fiLe, lpLe, trade5, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS)
            assertEquals("Message status=" + cwm5.getStatus(), cwm5.getStatus(), MessageStatus.FAILURE)

            creditAdminSvc.setLEOverride(lpOrg, fiOrg, false)
            Trade trade6 = prepareSingleLegTrade(10000, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate)
            CreditWorkflowMessage cwm6 = creditMgr.takeBilateralCredit(fiLe, lpLe, trade6, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS)
            assertEquals("Message status=" + cwm6.getStatus(), cwm6.getStatus(), MessageStatus.FAILURE)

            // verify the null override behavior.
            creditAdminSvc.setLEOverride(lpOrg, fiOrg, true)
            creditAdminSvc.setOrgDefault(lpOrg, fiOrg, lpTpForFi, false)
            creditAdminSvc.setMinimumTenor(lpOrg, lpTpForFi, null, true)
            creditAdminSvc.setMinimumTenor(lpOrg, lpTpForFi, new Tenor("1W") )
            Trade trade7 = prepareSingleLegTrade(10000, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate)
            CreditWorkflowMessage cwm7 = creditMgr.takeBilateralCredit(fiLe, lpLe, trade7, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS)
            assertEquals("Message success status=" + cwm7.getStatus(), cwm7.getStatus(), MessageStatus.SUCCESS)
        }
        catch ( Exception e )
        {
            fail( "testLeLevelOverrideMinTenor", e )
        }
        finally
        {
            creditAdminSvc.setCreditEnabled( fiOrg, true )
            creditAdminSvc.setMinimumTenor(lpOrg, lpTpForFi, null, true)
            creditAdminSvc.setOrgDefault(lpOrg, fiOrg, lpTpForFi, true )
            creditAdminSvc.setLEOverride(lpOrg, fiOrg, false)
            creditAdminSvc.setMinimumTenor( lpOrg, lpTpForFi, null)
            creditAdminSvc.setLegalEntityExposureLevel(lpOrg, fiOrg)
            creditAdminSvc.setMinimumTenor( lpOrg, lpTpForFi, null )
        }
    }

    void testLeLevelOverrideMaxTenor()
    {
        try
        {
            init(lpUser)
            removeExistingCreditUtilizationEvents(lpOrg)
            removeExistingCreditUtilizationEvents(fiOrg)


            creditAdminSvc.setOrganizationExposureLevel(lpOrg, fiOrg)

            setCalcAndLimit(lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000)
            setCalcAndLimit(lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 )
            setCalcAndLimit(fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 )
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 )

            creditAdminSvc.setCreditEnabled(fiOrg, false)

            Trade trade = prepareSingleLegTrade( 10000, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate )
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( fiLe, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS )

            assertEquals("Message success status=" + cwm.getStatus(), cwm.getStatus(), MessageStatus.SUCCESS)


            // set the minTenor limit to 5 days and value date as spot
            creditAdminSvc.setMaximumTenor(lpOrg, lpTpForFi, new Tenor("TOD"))

            Trade trade1 = prepareSingleLegTrade(10000, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate)
            CreditWorkflowMessage cwm1 = creditMgr.takeBilateralCredit(fiLe, lpLe, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS)

            assertEquals("Message success status=" + cwm1.getStatus(), cwm1.getStatus(), MessageStatus.FAILURE )

            // now set the legal entity level override.
            creditAdminSvc.setLEOverride( lpOrg, fiOrg, true )
            creditAdminSvc.setMaximumTenor(lpOrg, lpTpForFi, new Tenor("1Y"), true)
            creditAdminSvc.setOrgDefault(lpOrg, fiOrg, lpTpForFi, false )
            Trade trade2 = prepareSingleLegTrade(10000, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate)
            CreditWorkflowMessage cwm2 = creditMgr.takeBilateralCredit(fiLe, lpLe, trade2, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS)
            assertEquals("Message success status=" + cwm2.getStatus(), cwm2.getStatus(), MessageStatus.SUCCESS)

            creditAdminSvc.setMaximumTenor(lpOrg, lpTpForFi, new Tenor("TOM"), true)
            Trade trade3 = prepareSingleLegTrade(10000, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate)
            CreditWorkflowMessage cwm3 = creditMgr.takeBilateralCredit(fiLe, lpLe, trade3, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS)
            assertEquals("Message success status=" + cwm3.getStatus(), cwm3.getStatus(), MessageStatus.FAILURE)

            creditAdminSvc.setMaximumTenor(lpOrg, lpTpForFi, null, true)
            Trade trade4 = prepareSingleLegTrade(10000, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate)
            CreditWorkflowMessage cwm4 = creditMgr.takeBilateralCredit(fiLe, lpLe, trade4, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS)
            assertEquals("Message success status=" + cwm4.getStatus(), cwm4.getStatus(), MessageStatus.SUCCESS)


            creditAdminSvc.setOrgDefault(lpOrg, fiOrg, lpTpForFi, true)
            Trade trade5 = prepareSingleLegTrade(10000, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate)
            CreditWorkflowMessage cwm5 = creditMgr.takeBilateralCredit(fiLe, lpLe, trade5, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS)
            assertEquals("Message status=" + cwm5.getStatus(), cwm5.getStatus(), MessageStatus.FAILURE)

            creditAdminSvc.setLEOverride(lpOrg, fiOrg, false)
            Trade trade6 = prepareSingleLegTrade(10000, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate)
            CreditWorkflowMessage cwm6 = creditMgr.takeBilateralCredit(fiLe, lpLe, trade6, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS)
            assertEquals("Message status=" + cwm6.getStatus(), cwm6.getStatus(), MessageStatus.FAILURE)

            // verify the null override behavior.
            creditAdminSvc.setLEOverride(lpOrg, fiOrg, true)
            creditAdminSvc.setOrgDefault(lpOrg, fiOrg, lpTpForFi, false)
            creditAdminSvc.setMaximumTenor(lpOrg, lpTpForFi, null, true)
            creditAdminSvc.setMaximumTenor(lpOrg, lpTpForFi, new Tenor("TOD"))
            Trade trade7 = prepareSingleLegTrade(10000, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], spotDate)
            CreditWorkflowMessage cwm7 = creditMgr.takeBilateralCredit(fiLe, lpLe, trade7, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS)
            assertEquals("Message success status=" + cwm7.getStatus(), cwm7.getStatus(), MessageStatus.SUCCESS)
        }
        catch ( Exception e )
        {
            fail( "testLeLevelOverrideMaxTenor", e )
        }
        finally
        {
            creditAdminSvc.setCreditEnabled( fiOrg, true )
            creditAdminSvc.setMaximumTenor(lpOrg, lpTpForFi, null, true)
            creditAdminSvc.setOrgDefault(lpOrg, fiOrg, lpTpForFi, true )
            creditAdminSvc.setLEOverride(lpOrg, fiOrg, false)
            creditAdminSvc.setMaximumTenor(lpOrg, lpTpForFi, null)
            creditAdminSvc.setLegalEntityExposureLevel(lpOrg, fiOrg)
            creditAdminSvc.setMaximumTenor(lpOrg, lpTpForFi, null)
        }
    }

    void testMinTenorInBusinessDaysAtLELevelExposure()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )
            removeExistingCreditUtilizationEvents( fiOrg )
            Currency eur = CurrencyFactory.getCurrency( "EUR" )
            Currency usd = CurrencyFactory.getCurrency( "USD" )

            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 )
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 )
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 )
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 )

            // set the minTenor limit to 21 business days so that value date with and without business lag would be different.
            Tenor minTenor = new Tenor( "21d" )
            creditAdminSvc.setMinimumTenor( lpOrg, lpTpForFi, minTenor )
            creditAdminSvc.setTenorRestrictionInBusinessDays( lpOrg, lpTpForFi, true )

            IdcDate valueDate = CreditUtilC.getStdQuoteConvention().getFXRateBasis( eur, usd ).getValueDate( tradeDate, minTenor )
            IdcDate valueDateWithBusinessDayLag = CreditUtilC.getStdQuoteConvention().getFXRateBasis( eur, usd ).getValueDateWithBusinessDaysLag( tradeDate, minTenor, true )

            double limitBefore = getAvailableCreditLimit( lpTpForFi, valueDateWithBusinessDayLag.addDays( 2 ) )
            log( "credit limit before take Credit: " + limitBefore )
            double tradeAmt = 10000
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], valueDateWithBusinessDayLag.addDays( 2 ) )
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( fiLe, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS )

            double limitAfter = getAvailableCreditLimit( lpTpForFi, valueDateWithBusinessDayLag.addDays( 2 ) )
            log( "credit limit after take Credit: " + limitAfter )

            assertEquals( "Message success status=" + cwm.getStatus(), MessageStatus.SUCCESS, cwm.getStatus() )
            assertEquals( "The credit limit should change.", true, Math.abs( limitBefore - limitAfter - tradeAmt ) < MINIMUM )

            Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], valueDate.addDays( 2 ) )
            CreditWorkflowMessage cwm1 = creditMgr.takeBilateralCredit( fiLe, lpLe, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS )
            assertEquals( "Message success status=" + cwm1.getStatus(), MessageStatus.FAILURE, cwm1.getStatus() )
        }
        catch ( Exception e )
        {
            fail( "testMinTenorInBusinessDaysAtLELevelExposure", e )
        }
        finally
        {
            creditAdminSvc.setMinimumTenor( lpOrg, lpTpForFi, null )
            creditAdminSvc.setMinimumTenor( fiOrg, fiTpForLp, null )
        }
    }

    void testMinTenorInBusinessDaysAtOrgLevelExposure()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )
            removeExistingCreditUtilizationEvents( fiOrg )
            Currency eur = CurrencyFactory.getCurrency( "EUR" )
            Currency usd = CurrencyFactory.getCurrency( "USD" )

            creditAdminSvc.setOrganizationExposureLevel( fiOrg, lpOrg )

            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 )
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 )
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 )
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 )

            // set the minTenor limit to 21 business days so that value date with and without business lag would be different.
            Tenor minTenor = new Tenor( "21d" )
            creditAdminSvc.setMinimumTenor( lpOrg, lpTpForFi, minTenor )
            creditAdminSvc.setTenorRestrictionInBusinessDays( lpOrg, lpTpForFi, true )

            IdcDate valueDate = CreditUtilC.getStdQuoteConvention().getFXRateBasis( eur, usd ).getValueDate( tradeDate, minTenor )
            IdcDate valueDateWithBusinessDayLag = CreditUtilC.getStdQuoteConvention().getFXRateBasis( eur, usd ).getValueDateWithBusinessDaysLag( tradeDate, minTenor, true )

            double limitBefore = getAvailableCreditLimit( lpTpForFi, valueDateWithBusinessDayLag.addDays( 2 ) )
            log( "credit limit before take Credit: " + limitBefore )
            double tradeAmt = 10000
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], valueDateWithBusinessDayLag.addDays( 2 ) )
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( fiLe, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS )

            double limitAfter = getAvailableCreditLimit( lpTpForFi, valueDateWithBusinessDayLag.addDays( 2 ) )
            log( "credit limit after take Credit: " + limitAfter )

            assertEquals( "Message success status=" + cwm.getStatus(), MessageStatus.SUCCESS, cwm.getStatus() )
            assertEquals( "The credit limit should change.", true, Math.abs( limitBefore - limitAfter - tradeAmt ) < MINIMUM )

            Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], valueDate.addDays( 2 ) )
            CreditWorkflowMessage cwm1 = creditMgr.takeBilateralCredit( fiLe, lpLe, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS )
            assertEquals( "Message success status=" + cwm1.getStatus(), MessageStatus.FAILURE, cwm1.getStatus() )
        }
        catch ( Exception e )
        {
            fail( "testMinTenorInBusinessDaysAtOrgLevelExposure", e )
        }
        finally
        {
            creditAdminSvc.setMinimumTenor( lpOrg, lpTpForFi, null )
            creditAdminSvc.setMinimumTenor( fiOrg, fiTpForLp, null )
            creditAdminSvc.setLegalEntityExposureLevel( fiOrg, lpOrg )
        }
    }

    void testMinTenorInBusinessDaysAtOrgLevelExposureWithLEOverride()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )
            removeExistingCreditUtilizationEvents( fiOrg )
            Currency eur = CurrencyFactory.getCurrency( "EUR" )
            Currency usd = CurrencyFactory.getCurrency( "USD" )

            creditAdminSvc.setOrganizationExposureLevel( lpOrg, fiOrg )

            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 )
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 )
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 )
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 )

            creditAdminSvc.setLEOverride( lpOrg, fiOrg, true )
            creditAdminSvc.setOrgDefault( lpOrg, fiOrg, lpTpForFi, false )

            // set the minTenor limit to 21 business days so that value date with and without business lag would be different.
            Tenor minTenor = new Tenor( "21d" )
            creditAdminSvc.setMinimumTenor( lpOrg, lpTpForFi, minTenor, true )
            creditAdminSvc.setCptyLETenorRestrictionInBusinessDays( lpOrg, lpTpForFi, true )

            IdcDate valueDate = CreditUtilC.getStdQuoteConvention().getFXRateBasis( eur, usd ).getValueDate( tradeDate, minTenor )
            IdcDate valueDateWithBusinessDayLag = CreditUtilC.getStdQuoteConvention().getFXRateBasis( eur, usd ).getValueDateWithBusinessDaysLag( tradeDate, minTenor, true )

            double limitBefore = getAvailableCreditLimit( lpTpForFi, valueDateWithBusinessDayLag.addDays( 2 ) )
            log( "credit limit before take Credit: " + limitBefore )
            double tradeAmt = 10000
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], valueDateWithBusinessDayLag.addDays( 2 ) )
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( fiLe, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS )

            double limitAfter = getAvailableCreditLimit( lpTpForFi, valueDateWithBusinessDayLag.addDays( 2 ) )
            log( "credit limit after take Credit: " + limitAfter )

            assertEquals( "Message success status=" + cwm.getStatus(), MessageStatus.SUCCESS, cwm.getStatus() )
            assertEquals( "The credit limit should change.", true, Math.abs( limitBefore - limitAfter - tradeAmt ) < MINIMUM )

            Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], valueDate.addDays( 2 ) )
            CreditWorkflowMessage cwm1 = creditMgr.takeBilateralCredit( fiLe, lpLe, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS )
            assertEquals( "Message success status=" + cwm1.getStatus(), MessageStatus.FAILURE, cwm1.getStatus() )
        }
        catch ( Exception e )
        {
            fail( "testMinTenorInBusinessDaysAtOrgLevelExposureWithLEOverride", e )
        }
        finally
        {
            creditAdminSvc.setLEOverride( lpOrg, fiOrg, false )
            creditAdminSvc.setOrgDefault( lpOrg, fiOrg, lpTpForFi, true )
            creditAdminSvc.setMinimumTenor( lpOrg, lpTpForFi, null )
            creditAdminSvc.setMinimumTenor( fiOrg, fiTpForLp, null )
            creditAdminSvc.setMinimumTenor( lpOrg, lpTpForFi, null, true )
            creditAdminSvc.setMinimumTenor( fiOrg, fiTpForLp, null, true )
            creditAdminSvc.setLegalEntityExposureLevel( lpOrg, fiOrg )
        }
    }

    void testMaxTenorInBusinessDaysAtLELevelExposure()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )
            removeExistingCreditUtilizationEvents( fiOrg )
            Currency eur = CurrencyFactory.getCurrency( "EUR" )
            Currency usd = CurrencyFactory.getCurrency( "USD" )

            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 )
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 )
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 )
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 )

            // set the maxTenor limit to 21 business days so that value date with and without business lag would be different.
            Tenor maxTenor = new Tenor( "21d" )
            creditAdminSvc.setMaximumTenor( lpOrg, lpTpForFi, maxTenor )
            creditAdminSvc.setTenorRestrictionInBusinessDays( lpOrg, lpTpForFi, true )

            IdcDate valueDate = CreditUtilC.getStdQuoteConvention().getFXRateBasis( eur, usd ).getValueDate( tradeDate, maxTenor )
            IdcDate valueDateWithBusinessDayLag = CreditUtilC.getStdQuoteConvention().getFXRateBasis( eur, usd ).getValueDateWithBusinessDaysLag( tradeDate, maxTenor, true )

            double limitBefore = getAvailableCreditLimit( lpTpForFi, valueDateWithBusinessDayLag.addDays( 2 ) )
            log( "credit limit before take Credit: " + limitBefore )
            double tradeAmt = 10000
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], valueDate.addDays( 2 ) )
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( fiLe, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS )

            double limitAfter = getAvailableCreditLimit( lpTpForFi, valueDateWithBusinessDayLag.addDays( 2 ) )
            log( "credit limit after take Credit: " + limitAfter )

            assertEquals( "Message success status=" + cwm.getStatus(), MessageStatus.SUCCESS, cwm.getStatus() )
            assertEquals( "The credit limit should change.", true, Math.abs( limitBefore - limitAfter - tradeAmt ) < MINIMUM )

            Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], valueDateWithBusinessDayLag.addDays( 2 ) )
            CreditWorkflowMessage cwm1 = creditMgr.takeBilateralCredit( fiLe, lpLe, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS )
            assertEquals( "Message success status=" + cwm1.getStatus(), MessageStatus.FAILURE, cwm1.getStatus() )
        }
        catch ( Exception e )
        {
            fail( "testMaxTenorInBusinessDaysAtLELevelExposure", e )
        }
        finally
        {
            creditAdminSvc.setMaximumTenor( lpOrg, lpTpForFi, null )
            creditAdminSvc.setMaximumTenor( fiOrg, fiTpForLp, null )
        }
    }

    void testMaxTenorInBusinessDaysAtOrgLevelExposure()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )
            removeExistingCreditUtilizationEvents( fiOrg )
            Currency eur = CurrencyFactory.getCurrency( "EUR" )
            Currency usd = CurrencyFactory.getCurrency( "USD" )

            creditAdminSvc.setOrganizationExposureLevel( fiOrg, lpOrg )

            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 )
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 )
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 )
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 )

            // set the maxTenor limit to 21 business days so that value date with and without business lag would be different.
            Tenor maxTenor = new Tenor( "21d" )
            creditAdminSvc.setMaximumTenor( lpOrg, lpTpForFi, maxTenor )
            creditAdminSvc.setTenorRestrictionInBusinessDays( lpOrg, lpTpForFi, true )

            IdcDate valueDate = CreditUtilC.getStdQuoteConvention().getFXRateBasis( eur, usd ).getValueDate( tradeDate, maxTenor )
            IdcDate valueDateWithBusinessDayLag = CreditUtilC.getStdQuoteConvention().getFXRateBasis( eur, usd ).getValueDateWithBusinessDaysLag( tradeDate, maxTenor, true )

            double limitBefore = getAvailableCreditLimit( lpTpForFi, valueDateWithBusinessDayLag.addDays( 2 ) )
            log( "credit limit before take Credit: " + limitBefore )
            double tradeAmt = 10000
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], valueDate.addDays( 2 ) )
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( fiLe, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS )

            double limitAfter = getAvailableCreditLimit( lpTpForFi, valueDateWithBusinessDayLag.addDays( 2 ) )
            log( "credit limit after take Credit: " + limitAfter )

            assertEquals( "Message success status=" + cwm.getStatus(), MessageStatus.SUCCESS, cwm.getStatus() )
            assertEquals( "The credit limit should change.", true, Math.abs( limitBefore - limitAfter - tradeAmt ) < MINIMUM )

            Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], valueDateWithBusinessDayLag.addDays( 2 ) )
            CreditWorkflowMessage cwm1 = creditMgr.takeBilateralCredit( fiLe, lpLe, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS )
            assertEquals( "Message success status=" + cwm1.getStatus(), MessageStatus.FAILURE, cwm1.getStatus() )
        }
        catch ( Exception e )
        {
            e.printStackTrace()
            fail( "testMaxTenorInBusinessDaysAtOrgLevelExposure", e )
        }
        finally
        {
            creditAdminSvc.setMaximumTenor( lpOrg, lpTpForFi, null )
            creditAdminSvc.setMaximumTenor( fiOrg, fiTpForLp, null )
            creditAdminSvc.setLegalEntityExposureLevel( fiOrg, lpOrg )
        }
    }

    void testMaxTenorInBusinessDaysAtOrgLevelExposureWithLEOverride()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents( lpOrg )
            removeExistingCreditUtilizationEvents( fiOrg )
            Currency eur = CurrencyFactory.getCurrency( "EUR" )
            Currency usd = CurrencyFactory.getCurrency( "USD" )

            creditAdminSvc.setOrganizationExposureLevel( lpOrg, fiOrg )

            setCalcAndLimit( lpOrg, lpTpForFi, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 )
            setCalcAndLimit( lpOrg, lpTpForFi, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 )
            setCalcAndLimit( fiOrg, fiTpForLp, DAILY_SETTLEMENT_CLASSIFICATION, GROSS_DAILY_LIMIT_CALCULATOR, 1000000 )
            setCalcAndLimit( fiOrg, fiTpForLp, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 1000000 )

            creditAdminSvc.setLEOverride( lpOrg, fiOrg, true )
            creditAdminSvc.setOrgDefault( lpOrg, fiOrg, lpTpForFi, false )

            // set the maxTenor limit to 21 business days so that value date with and without business lag would be different.
            Tenor maxTenor = new Tenor( "21d" )
            creditAdminSvc.setMaximumTenor( lpOrg, lpTpForFi, maxTenor, true )
            creditAdminSvc.setCptyLETenorRestrictionInBusinessDays( lpOrg, lpTpForFi, true )

            IdcDate valueDate = CreditUtilC.getStdQuoteConvention().getFXRateBasis( eur, usd ).getValueDate( tradeDate, maxTenor )
            IdcDate valueDateWithBusinessDayLag = CreditUtilC.getStdQuoteConvention().getFXRateBasis( eur, usd ).getValueDateWithBusinessDaysLag( tradeDate, maxTenor, true )

            double limitBefore = getAvailableCreditLimit( lpTpForFi, valueDateWithBusinessDayLag.addDays( 2 ) )
            log( "credit limit before take Credit: " + limitBefore )
            double tradeAmt = 10000
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], valueDate.addDays( 2 ) )
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( fiLe, lpLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS )

            double limitAfter = getAvailableCreditLimit( lpTpForFi, valueDateWithBusinessDayLag.addDays( 2 ) )
            log( "credit limit after take Credit: " + limitAfter )

            assertEquals( "Message success status=" + cwm.getStatus(), MessageStatus.SUCCESS, cwm.getStatus() )
            assertEquals( "The credit limit should change.", true, Math.abs( limitBefore - limitAfter - tradeAmt ) < MINIMUM )

            Trade trade1 = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForFi, bidRates[0], valueDateWithBusinessDayLag.addDays( 3 ) )
            CreditWorkflowMessage cwm1 = creditMgr.takeBilateralCredit( fiLe, lpLe, trade1, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS )
            assertEquals( "Message success status=" + cwm1.getStatus(), MessageStatus.FAILURE, cwm1.getStatus() )
        }
        catch ( Exception e )
        {
            fail( "testMaxTenorInBusinessDaysAtOrgLevelExposureWithLEOverride", e )
        }
        finally
        {
            creditAdminSvc.setLEOverride( lpOrg, fiOrg, false )
            creditAdminSvc.setOrgDefault( lpOrg, fiOrg, lpTpForFi, true )
            creditAdminSvc.setMaximumTenor( lpOrg, lpTpForFi, null )
            creditAdminSvc.setMaximumTenor( fiOrg, fiTpForLp, null )
            creditAdminSvc.setMaximumTenor( lpOrg, lpTpForFi, null, true )
            creditAdminSvc.setMaximumTenor( fiOrg, fiTpForLp, null, true )
            creditAdminSvc.setLegalEntityExposureLevel( lpOrg, fiOrg )
        }
    }

    void testSkipSettledTradesOnUndoCredit()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents ( lpOrg )

            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 10000 )
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 )

            double tradeAmt = 1000
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( lpLe, ddLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS )
            assertEquals( "success=" + cwm.getStatus(), MessageStatus.SUCCESS, cwm.getStatus() )
            validateCreditUtilizationEvents( cwm )

            //update the event with old value date
            for ( CreditUtilizationEvent cue: cwm.getCreditUtilizationEvents () )
            {
                cue.setSettlementDate ( spotDate.subtractDays ( 10 ) )
            }

            CreditWorkflowRiders riders = new CreditWorkflowRiders ()
            riders.setSkipSettledTradeUndoCredit ( true )
            CreditWorkflowMessage cwm1 = creditMgr.undoBilateralCredit ( lpLe, ddLe, trade, riders )
            assertEquals( "success=" + cwm1.getStatus(), MessageStatus.SUCCESS, cwm1.getStatus() )
        }
        catch ( Exception e )
        {
            fail ( "testSkipSettledTradesOnUndoCredit", e )
        }
        finally
        {
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 10000 )
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 )
        }
    }

    void testSkipSettledTradesOnUndoCredit_Account()
    {
        try
        {
            init( lpUser )
            removeExistingCreditUtilizationEvents ( lpOrg )

            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, null, 10000 )
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, AGGREGATE_NET_CASH_SETTLEMENT_CALCULATOR, 10000 )

            double tradeAmt = 1000
            Trade trade = prepareSingleLegTrade( tradeAmt, true, false, EURUSD, lpOrg, lpTpForDd, bidRates[0], spotDate )
            CreditWorkflowMessage cwm = creditMgr.takeBilateralCredit( lpLe, ddLe, trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS )
            assertEquals( "success=" + cwm.getStatus(), MessageStatus.SUCCESS, cwm.getStatus() )
            validateCreditUtilizationEvents( cwm )

            //update the event with old value date
            for ( CreditUtilizationEvent cue: cwm.getCreditUtilizationEvents () )
            {
                cue.setSettlementDate ( spotDate.subtractDays ( 5 ) )
            }

            CreditWorkflowRiders riders = new CreditWorkflowRiders ()
            riders.setSkipSettledTradeUndoCredit ( true )
            CreditWorkflowMessage cwm1 = creditMgr.undoBilateralCredit ( lpLe, ddLe, trade, riders )
            assertEquals( "success=" + cwm1.getStatus(), MessageStatus.SUCCESS, cwm1.getStatus() )

            CreditUtilization aggCu = getAggregateCreditUtilization ( lpLe, lpTpForDd, spotDate )
            CurrencyPosition eurPos = aggCu.getCurrencyPositions ().getCurrencyPosition ( CurrencyFactory.getCurrency ( "EUR" ) )
            assertTrue( eurPos.getNetAmount ().doubleValue() == 0.0 )

            CurrencyPosition usdPos = aggCu.getCurrencyPositions ().getCurrencyPosition ( CurrencyFactory.getCurrency ( "USD" ) )
            assertTrue( usdPos.getNetAmount ().doubleValue() == 0.0 )

        }
        catch ( Exception e )
        {
            fail ( "testSkipSettledTradesOnUndoCredit_Account", e )
        }
        finally
        {
            setCalcAndLimit( lpOrg, lpTpForDd, DAILY_SETTLEMENT_CLASSIFICATION, DAILY_SETTLEMENT_LIMIT_CALCULATOR, 10000 )
            setCalcAndLimit( lpOrg, lpTpForDd, GROSS_NOTIONAL_CLASSIFICATION, GROSS_AGGREGATE_LIMIT_CALCULATOR, 10000 )
        }
    }

    private void setCreditUtilizationEventGuid( CreditWorkflowMessage cwm, Map<String, Object> propertiesMap )
    {
        int size = cwm.getCreditUtilizationEvents().size()
        long[] cuObjectIds = new long[size]
        String[] guids = new String[size]
        double[] ccySums = new double[size]
        double[] ccyAmtSums = new double[size]
        int index = 0
        for ( CreditUtilizationEvent cue : cwm.getCreditUtilizationEvents() )
        {
            cuObjectIds[index] = cue.getCreditUtilization().getObjectID()
            guids[index] = cue.getGUID()
            index++
        }
        propertiesMap.put( CreditLimit.CREDIT_UTILIZATION, cuObjectIds )
        propertiesMap.put( CreditLimit.CREDIT_UTILIZATION_EVENT, guids )
    }
}
